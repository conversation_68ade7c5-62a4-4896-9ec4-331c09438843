import 'package:equatable/equatable.dart';

/// نموذج مقدم خدمة API
class ApiProvider extends Equatable {
  final String id;
  final String name;
  final String displayName;
  final String baseUrl;
  final String? apiKey;
  final bool isActive;
  final List<String> supportedModels;
  final Map<String, dynamic> headers;
  final ApiProviderType type;
  final String? description;
  final String? iconUrl;
  final DateTime? lastTested;
  final bool isWorking;

  const ApiProvider({
    required this.id,
    required this.name,
    required this.displayName,
    required this.baseUrl,
    this.apiKey,
    required this.isActive,
    required this.supportedModels,
    required this.headers,
    required this.type,
    this.description,
    this.iconUrl,
    this.lastTested,
    required this.isWorking,
  });

  factory ApiProvider.create({
    required String name,
    required String displayName,
    required String baseUrl,
    String? apiKey,
    required ApiProviderType type,
    String? description,
  }) {
    return ApiProvider(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      name: name,
      displayName: displayName,
      baseUrl: baseUrl,
      apiKey: apiKey,
      isActive: apiKey != null && apiKey.isNotEmpty,
      supportedModels: [],
      headers: _getDefaultHeaders(type),
      type: type,
      description: description,
      isWorking: false,
    );
  }

  static Map<String, dynamic> _getDefaultHeaders(ApiProviderType type) {
    switch (type) {
      case ApiProviderType.openai:
      case ApiProviderType.openrouter:
        return {
          'Content-Type': 'application/json',
          'User-Agent': 'DeepSeek-AI-App/1.0',
        };
      case ApiProviderType.gemini:
        return {'Content-Type': 'application/json'};
      case ApiProviderType.anthropic:
        return {
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01',
        };
      case ApiProviderType.huggingface:
        return {'Content-Type': 'application/json'};
      case ApiProviderType.custom:
        return {'Content-Type': 'application/json'};
    }
  }

  /// نسخ مع تعديلات
  ApiProvider copyWith({
    String? id,
    String? name,
    String? displayName,
    String? baseUrl,
    String? apiKey,
    bool? isActive,
    List<String>? supportedModels,
    Map<String, dynamic>? headers,
    ApiProviderType? type,
    String? description,
    String? iconUrl,
    DateTime? lastTested,
    bool? isWorking,
  }) {
    return ApiProvider(
      id: id ?? this.id,
      name: name ?? this.name,
      displayName: displayName ?? this.displayName,
      baseUrl: baseUrl ?? this.baseUrl,
      apiKey: apiKey ?? this.apiKey,
      isActive: isActive ?? this.isActive,
      supportedModels: supportedModels ?? this.supportedModels,
      headers: headers ?? this.headers,
      type: type ?? this.type,
      description: description ?? this.description,
      iconUrl: iconUrl ?? this.iconUrl,
      lastTested: lastTested ?? this.lastTested,
      isWorking: isWorking ?? this.isWorking,
    );
  }

  /// تحويل إلى JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'displayName': displayName,
      'baseUrl': baseUrl,
      'apiKey': apiKey,
      'isActive': isActive,
      'supportedModels': supportedModels,
      'headers': headers,
      'type': type.name,
      'description': description,
      'iconUrl': iconUrl,
      'lastTested': lastTested?.toIso8601String(),
      'isWorking': isWorking,
    };
  }

  /// إنشاء من JSON
  factory ApiProvider.fromJson(Map<String, dynamic> json) {
    return ApiProvider(
      id: json['id'],
      name: json['name'],
      displayName: json['displayName'],
      baseUrl: json['baseUrl'],
      apiKey: json['apiKey'],
      isActive: json['isActive'] ?? false,
      supportedModels: List<String>.from(json['supportedModels'] ?? []),
      headers: Map<String, dynamic>.from(json['headers'] ?? {}),
      type: ApiProviderType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => ApiProviderType.custom,
      ),
      description: json['description'],
      iconUrl: json['iconUrl'],
      lastTested:
          json['lastTested'] != null
              ? DateTime.parse(json['lastTested'])
              : null,
      isWorking: json['isWorking'] ?? false,
    );
  }

  @override
  List<Object?> get props => [
    id,
    name,
    displayName,
    baseUrl,
    apiKey,
    isActive,
    supportedModels,
    headers,
    type,
    description,
    iconUrl,
    lastTested,
    isWorking,
  ];
}

/// أنواع مقدمي الخدمة
enum ApiProviderType {
  openai,
  openrouter,
  gemini,
  anthropic,
  huggingface,
  custom,
}

/// نموذج النموذج المتاح
class AvailableModel extends Equatable {
  final String id;
  final String name;
  final String displayName;
  final String providerId;
  final bool isActive;
  final Map<String, dynamic> capabilities;
  final int? maxTokens;
  final double? costPer1kTokens;

  const AvailableModel({
    required this.id,
    required this.name,
    required this.displayName,
    required this.providerId,
    required this.isActive,
    required this.capabilities,
    this.maxTokens,
    this.costPer1kTokens,
  });

  factory AvailableModel.fromJson(
    Map<String, dynamic> json,
    String providerId,
  ) {
    return AvailableModel(
      id: json['id'] ?? json['model'] ?? '',
      name: json['id'] ?? json['model'] ?? '',
      displayName: json['name'] ?? json['id'] ?? json['model'] ?? '',
      providerId: providerId,
      isActive: true,
      capabilities: json['capabilities'] ?? {},
      maxTokens: json['max_tokens'] ?? json['context_length'],
      costPer1kTokens: json['pricing']?['prompt']?.toDouble(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'displayName': displayName,
      'providerId': providerId,
      'isActive': isActive,
      'capabilities': capabilities,
      'maxTokens': maxTokens,
      'costPer1kTokens': costPer1kTokens,
    };
  }

  @override
  List<Object?> get props => [
    id,
    name,
    displayName,
    providerId,
    isActive,
    capabilities,
    maxTokens,
    costPer1kTokens,
  ];
}
