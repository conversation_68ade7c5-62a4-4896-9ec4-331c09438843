import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/services.dart';
import 'dart:async';
import 'dart:math';

import '../lib/core/services/enhanced_ai_service.dart';
import '../lib/core/analytics/usage_analytics.dart';
import '../lib/core/security/security_service.dart';
import '../lib/core/performance/performance_service.dart';
import '../lib/core/storage/storage_service.dart';

void main() {
  group('🚀 Performance & Load Tests', () {
    
    // ==================== MEMORY TESTS ====================
    group('💾 Memory Performance Tests', () {
      
      test('Memory usage should remain stable under load', () async {
        await PerformanceService.initialize();
        
        final initialMetrics = PerformanceService.metrics;
        final initialMemory = initialMetrics.memoryUsageMB;
        
        // Simulate heavy usage
        for (int i = 0; i < 100; i++) {
          await _simulateHeavyOperation();
          
          // Force garbage collection periodically
          if (i % 10 == 0) {
            await PerformanceService.optimizeMemory();
          }
        }
        
        final finalMetrics = PerformanceService.metrics;
        final finalMemory = finalMetrics.memoryUsageMB;
        
        // Memory should not increase by more than 50MB
        final memoryIncrease = finalMemory - initialMemory;
        expect(memoryIncrease, lessThan(50.0));
        
        print('📊 Memory usage: ${initialMemory.toStringAsFixed(1)}MB → ${finalMemory.toStringAsFixed(1)}MB');
        print('📈 Memory increase: ${memoryIncrease.toStringAsFixed(1)}MB');
      });

      test('Large data encryption should not cause memory leaks', () async {
        await SecurityService.initialize();
        await PerformanceService.initialize();
        
        final initialMemory = PerformanceService.metrics.memoryUsageMB;
        
        // Create large data chunks
        const largeData = 'A' * 10000; // 10KB string
        
        // Encrypt/decrypt 100 times
        for (int i = 0; i < 100; i++) {
          final encrypted = SecurityService.encryptSensitiveData(largeData);
          final decrypted = SecurityService.decryptSensitiveData(encrypted);
          expect(decrypted, largeData);
          
          // Clear variables to help GC
          // encrypted and decrypted will be garbage collected
        }
        
        // Force memory optimization
        await PerformanceService.optimizeMemory();
        
        final finalMemory = PerformanceService.metrics.memoryUsageMB;
        final memoryIncrease = finalMemory - initialMemory;
        
        // Should not leak significant memory
        expect(memoryIncrease, lessThan(20.0));
        
        print('🔐 Encryption memory test: ${memoryIncrease.toStringAsFixed(1)}MB increase');
      });
    });

    // ==================== SPEED TESTS ====================
    group('⚡ Speed Performance Tests', () {
      
      test('Service initialization should be fast', () async {
        final stopwatch = Stopwatch()..start();
        
        await Future.wait([
          UsageAnalytics.initialize(),
          SecurityService.initialize(),
          PerformanceService.initialize(),
        ]);
        
        stopwatch.stop();
        
        // Should initialize within 3 seconds
        expect(stopwatch.elapsedMilliseconds, lessThan(3000));
        print('🚀 Services initialized in ${stopwatch.elapsedMilliseconds}ms');
      });

      test('Analytics tracking should be fast', () async {
        await UsageAnalytics.initialize();
        
        final stopwatch = Stopwatch()..start();
        
        // Track 1000 events
        for (int i = 0; i < 1000; i++) {
          await UsageAnalytics.trackToolUsage('speed_test');
        }
        
        stopwatch.stop();
        
        // Should complete within 1 second
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
        
        final avgTimePerEvent = stopwatch.elapsedMilliseconds / 1000;
        print('📊 Analytics: ${avgTimePerEvent.toStringAsFixed(2)}ms per event');
      });

      test('Encryption should be performant', () async {
        await SecurityService.initialize();
        
        const testData = 'Performance test data for encryption benchmarking';
        final stopwatch = Stopwatch()..start();
        
        // Perform 1000 encryption operations
        for (int i = 0; i < 1000; i++) {
          final encrypted = SecurityService.encryptSensitiveData(testData);
          final decrypted = SecurityService.decryptSensitiveData(encrypted);
          expect(decrypted, testData);
        }
        
        stopwatch.stop();
        
        // Should complete within 500ms
        expect(stopwatch.elapsedMilliseconds, lessThan(500));
        
        final avgTimePerOperation = stopwatch.elapsedMilliseconds / 1000;
        print('🔐 Encryption: ${avgTimePerOperation.toStringAsFixed(2)}ms per operation');
      });

      test('Storage operations should be fast', () async {
        await StorageService.initialize();
        
        final stopwatch = Stopwatch()..start();
        
        // Perform 100 storage operations
        for (int i = 0; i < 100; i++) {
          await StorageService.saveData('test_key_$i', {'data': 'test_value_$i'});
          final data = await StorageService.getData('test_key_$i');
          expect(data, isNotNull);
        }
        
        stopwatch.stop();
        
        // Should complete within 2 seconds
        expect(stopwatch.elapsedMilliseconds, lessThan(2000));
        
        final avgTimePerOperation = stopwatch.elapsedMilliseconds / 100;
        print('💾 Storage: ${avgTimePerOperation.toStringAsFixed(2)}ms per operation');
        
        // Cleanup
        for (int i = 0; i < 100; i++) {
          await StorageService.removeData('test_key_$i');
        }
      });
    });

    // ==================== LOAD TESTS ====================
    group('🏋️ Load Tests', () {
      
      test('Concurrent analytics tracking', () async {
        await UsageAnalytics.initialize();
        
        final stopwatch = Stopwatch()..start();
        
        // Create 10 concurrent futures, each tracking 100 events
        final futures = List.generate(10, (index) async {
          for (int i = 0; i < 100; i++) {
            await UsageAnalytics.trackToolUsage('concurrent_test_$index');
            await UsageAnalytics.trackProviderUsage('provider_$index');
            await UsageAnalytics.trackTokenUsage(10, 'provider_$index');
          }
        });
        
        await Future.wait(futures);
        stopwatch.stop();
        
        // Should handle concurrent load within 5 seconds
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));
        
        // Verify data integrity
        final stats = await UsageAnalytics.getUsageStats();
        expect(stats.totalToolUsage, 1000); // 10 * 100
        expect(stats.totalTokensUsed, 10000); // 10 * 100 * 10
        
        print('🔄 Concurrent analytics: ${stopwatch.elapsedMilliseconds}ms for 1000 events');
      });

      test('Concurrent encryption operations', () async {
        await SecurityService.initialize();
        
        const testData = 'Concurrent encryption test data';
        final stopwatch = Stopwatch()..start();
        
        // Create 20 concurrent encryption tasks
        final futures = List.generate(20, (index) async {
          for (int i = 0; i < 50; i++) {
            final encrypted = SecurityService.encryptSensitiveData('$testData $index $i');
            final decrypted = SecurityService.decryptSensitiveData(encrypted);
            expect(decrypted, '$testData $index $i');
          }
        });
        
        await Future.wait(futures);
        stopwatch.stop();
        
        // Should handle concurrent encryption within 3 seconds
        expect(stopwatch.elapsedMilliseconds, lessThan(3000));
        
        print('🔐 Concurrent encryption: ${stopwatch.elapsedMilliseconds}ms for 1000 operations');
      });

      test('High-frequency storage operations', () async {
        await StorageService.initialize();
        
        final stopwatch = Stopwatch()..start();
        
        // Rapid storage operations
        final futures = <Future>[];
        for (int i = 0; i < 500; i++) {
          futures.add(StorageService.saveData('rapid_test_$i', {
            'timestamp': DateTime.now().millisecondsSinceEpoch,
            'data': 'test_data_$i',
            'index': i,
          }));
        }
        
        await Future.wait(futures);
        
        // Verify all data was saved
        for (int i = 0; i < 500; i++) {
          final data = await StorageService.getData('rapid_test_$i');
          expect(data, isNotNull);
          expect(data['index'], i);
        }
        
        stopwatch.stop();
        
        // Should handle high-frequency operations within 10 seconds
        expect(stopwatch.elapsedMilliseconds, lessThan(10000));
        
        print('💾 High-frequency storage: ${stopwatch.elapsedMilliseconds}ms for 500 operations');
        
        // Cleanup
        for (int i = 0; i < 500; i++) {
          await StorageService.removeData('rapid_test_$i');
        }
      });
    });

    // ==================== STRESS TESTS ====================
    group('💪 Stress Tests', () {
      
      test('Memory stress test with large data', () async {
        await PerformanceService.initialize();
        
        final initialMemory = PerformanceService.metrics.memoryUsageMB;
        
        // Create and process large amounts of data
        final largeDataSets = <String>[];
        
        for (int i = 0; i < 50; i++) {
          // Create 1MB of data
          final largeData = 'X' * (1024 * 1024);
          largeDataSets.add(largeData);
          
          // Process the data
          await _processLargeData(largeData);
          
          // Periodic cleanup
          if (i % 10 == 0) {
            await PerformanceService.optimizeMemory();
          }
        }
        
        // Final cleanup
        largeDataSets.clear();
        await PerformanceService.optimizeMemory();
        
        final finalMemory = PerformanceService.metrics.memoryUsageMB;
        final memoryIncrease = finalMemory - initialMemory;
        
        // Should not increase memory by more than 100MB
        expect(memoryIncrease, lessThan(100.0));
        
        print('💪 Stress test memory increase: ${memoryIncrease.toStringAsFixed(1)}MB');
      });

      test('Continuous operation stress test', () async {
        await UsageAnalytics.initialize();
        await SecurityService.initialize();
        
        final stopwatch = Stopwatch()..start();
        
        // Run continuous operations for 30 seconds
        final endTime = DateTime.now().add(const Duration(seconds: 30));
        int operationCount = 0;
        
        while (DateTime.now().isBefore(endTime)) {
          // Mix of different operations
          await UsageAnalytics.trackToolUsage('stress_test');
          
          final testData = 'stress_test_data_$operationCount';
          final encrypted = SecurityService.encryptSensitiveData(testData);
          final decrypted = SecurityService.decryptSensitiveData(encrypted);
          expect(decrypted, testData);
          
          await StorageService.saveData('stress_$operationCount', {
            'operation': operationCount,
            'timestamp': DateTime.now().millisecondsSinceEpoch,
          });
          
          operationCount++;
          
          // Brief pause to prevent overwhelming the system
          await Future.delayed(const Duration(milliseconds: 10));
        }
        
        stopwatch.stop();
        
        // Should complete without errors
        expect(operationCount, greaterThan(0));
        
        final operationsPerSecond = operationCount / (stopwatch.elapsedMilliseconds / 1000);
        print('💪 Stress test: $operationCount operations in ${stopwatch.elapsedSeconds}s');
        print('📊 Rate: ${operationsPerSecond.toStringAsFixed(1)} operations/second');
        
        // Cleanup
        for (int i = 0; i < operationCount; i++) {
          await StorageService.removeData('stress_$i');
        }
      });
    });

    // ==================== BENCHMARK TESTS ====================
    group('📏 Benchmark Tests', () {
      
      test('Encryption algorithm benchmark', () async {
        await SecurityService.initialize();
        
        final dataSizes = [100, 1000, 10000, 100000]; // Different data sizes
        final results = <String, double>{};
        
        for (final size in dataSizes) {
          final testData = 'A' * size;
          final stopwatch = Stopwatch()..start();
          
          // Perform 100 operations
          for (int i = 0; i < 100; i++) {
            final encrypted = SecurityService.encryptSensitiveData(testData);
            final decrypted = SecurityService.decryptSensitiveData(encrypted);
            expect(decrypted, testData);
          }
          
          stopwatch.stop();
          
          final avgTime = stopwatch.elapsedMilliseconds / 100;
          results['${size}B'] = avgTime;
          
          print('🔐 Encryption benchmark ${size}B: ${avgTime.toStringAsFixed(2)}ms avg');
        }
        
        // Verify performance scales reasonably
        expect(results['100B']!, lessThan(10.0)); // Should be very fast for small data
        expect(results['100000B']!, lessThan(100.0)); // Should still be reasonable for large data
      });

      test('Storage performance benchmark', () async {
        await StorageService.initialize();
        
        final operations = ['save', 'read', 'update', 'delete'];
        final results = <String, double>{};
        
        for (final operation in operations) {
          final stopwatch = Stopwatch()..start();
          
          switch (operation) {
            case 'save':
              for (int i = 0; i < 1000; i++) {
                await StorageService.saveData('benchmark_$i', {'data': 'value_$i'});
              }
              break;
              
            case 'read':
              for (int i = 0; i < 1000; i++) {
                final data = await StorageService.getData('benchmark_$i');
                expect(data, isNotNull);
              }
              break;
              
            case 'update':
              for (int i = 0; i < 1000; i++) {
                await StorageService.saveData('benchmark_$i', {'data': 'updated_value_$i'});
              }
              break;
              
            case 'delete':
              for (int i = 0; i < 1000; i++) {
                await StorageService.removeData('benchmark_$i');
              }
              break;
          }
          
          stopwatch.stop();
          
          final avgTime = stopwatch.elapsedMilliseconds / 1000;
          results[operation] = avgTime;
          
          print('💾 Storage $operation: ${avgTime.toStringAsFixed(2)}ms avg');
        }
        
        // Verify reasonable performance
        expect(results['save']!, lessThan(5.0));
        expect(results['read']!, lessThan(2.0));
        expect(results['update']!, lessThan(5.0));
        expect(results['delete']!, lessThan(3.0));
      });
    });
  });
}

// ==================== HELPER FUNCTIONS ====================

Future<void> _simulateHeavyOperation() async {
  // Simulate CPU-intensive work
  final random = Random();
  var sum = 0;
  for (int i = 0; i < 10000; i++) {
    sum += random.nextInt(1000);
  }
  
  // Simulate some async work
  await Future.delayed(const Duration(milliseconds: 1));
  
  // Create and discard some objects to test GC
  final tempList = List.generate(1000, (index) => 'temp_$index');
  tempList.clear();
}

Future<void> _processLargeData(String data) async {
  // Simulate processing large data
  var hash = 0;
  for (int i = 0; i < data.length; i += 1000) {
    hash ^= data.codeUnitAt(i);
  }
  
  // Simulate async processing
  await Future.delayed(const Duration(milliseconds: 5));
  
  // Return some result to prevent optimization
  expect(hash, isA<int>());
}

class PerformanceBenchmark {
  static Future<double> measureOperation(Future<void> Function() operation, {int iterations = 100}) async {
    final stopwatch = Stopwatch()..start();
    
    for (int i = 0; i < iterations; i++) {
      await operation();
    }
    
    stopwatch.stop();
    return stopwatch.elapsedMilliseconds / iterations;
  }
  
  static Future<Map<String, double>> compareOperations(Map<String, Future<void> Function()> operations) async {
    final results = <String, double>{};
    
    for (final entry in operations.entries) {
      final avgTime = await measureOperation(entry.value);
      results[entry.key] = avgTime;
    }
    
    return results;
  }
}
