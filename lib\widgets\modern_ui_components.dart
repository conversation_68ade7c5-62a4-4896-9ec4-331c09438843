import 'package:flutter/material.dart';
import 'dart:ui';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';

/// مكونات UI حديثة مستوحاة من التصميم الجديد
class ModernUIComponents {
  
  /// بطاقة زجاجية حديثة (Glassmorphism)
  static Widget glassCard({
    required Widget child,
    double? width,
    double? height,
    EdgeInsets? padding,
    BorderRadius? borderRadius,
    double blurIntensity = 10.0,
    Color? backgroundColor,
    Color? borderColor,
    List<BoxShadow>? shadows,
  }) {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        borderRadius: borderRadius ?? BorderRadius.circular(20),
        boxShadow: shadows ?? [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: blurIntensity, sigmaY: blurIntensity),
          child: Container(
            decoration: BoxDecoration(
              gradient: AppColors.glassGradient,
              borderRadius: borderRadius ?? BorderRadius.circular(20),
              border: Border.all(
                color: borderColor ?? AppColors.electricBlue.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            padding: padding ?? const EdgeInsets.all(20),
            child: child,
          ),
        ),
      ),
    );
  }

  /// زر حديث مع تأثيرات متقدمة
  static Widget modernButton({
    required String text,
    required VoidCallback onPressed,
    double? width,
    double? height,
    EdgeInsets? padding,
    BorderRadius? borderRadius,
    Gradient? gradient,
    Color? textColor,
    double? fontSize,
    FontWeight? fontWeight,
    IconData? icon,
    bool isLoading = false,
    bool isEnabled = true,
    List<BoxShadow>? shadows,
  }) {
    return Container(
      width: width,
      height: height ?? 56,
      decoration: BoxDecoration(
        gradient: gradient ?? AppColors.primaryGradient,
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        boxShadow: shadows ?? [
          BoxShadow(
            color: AppColors.primaryPurple.withValues(alpha: 0.3),
            blurRadius: 15,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: isEnabled && !isLoading ? onPressed : null,
          borderRadius: borderRadius ?? BorderRadius.circular(16),
          child: Container(
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              mainAxisSize: MainAxisSize.min,
              children: [
                if (isLoading) ...[
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        textColor ?? AppColors.white,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                ],
                if (icon != null && !isLoading) ...[
                  Icon(
                    icon,
                    color: textColor ?? AppColors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                ],
                Text(
                  text,
                  style: TextStyle(
                    color: textColor ?? AppColors.white,
                    fontSize: fontSize ?? 16,
                    fontWeight: fontWeight ?? FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// حقل إدخال حديث
  static Widget modernTextField({
    required TextEditingController controller,
    String? hintText,
    String? labelText,
    IconData? prefixIcon,
    IconData? suffixIcon,
    VoidCallback? onSuffixIconPressed,
    bool obscureText = false,
    TextInputType? keyboardType,
    int? maxLines,
    Function(String)? onChanged,
    String? Function(String?)? validator,
    bool enabled = true,
    EdgeInsets? contentPadding,
    BorderRadius? borderRadius,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: TextFormField(
        controller: controller,
        obscureText: obscureText,
        keyboardType: keyboardType,
        maxLines: maxLines ?? 1,
        onChanged: onChanged,
        validator: validator,
        enabled: enabled,
        style: TextStyle(
          color: AppColors.white,
          fontSize: 16,
        ),
        decoration: InputDecoration(
          hintText: hintText,
          labelText: labelText,
          hintStyle: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 14,
          ),
          labelStyle: TextStyle(
            color: AppColors.electricBlue,
            fontSize: 14,
          ),
          prefixIcon: prefixIcon != null
              ? Icon(prefixIcon, color: AppColors.electricBlue)
              : null,
          suffixIcon: suffixIcon != null
              ? IconButton(
                  icon: Icon(suffixIcon, color: AppColors.electricBlue),
                  onPressed: onSuffixIconPressed,
                )
              : null,
          filled: true,
          fillColor: AppColors.darkGrey.withValues(alpha: 0.8),
          contentPadding: contentPadding ?? 
              const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          border: OutlineInputBorder(
            borderRadius: borderRadius ?? BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: borderRadius ?? BorderRadius.circular(16),
            borderSide: BorderSide(
              color: AppColors.electricBlue.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: borderRadius ?? BorderRadius.circular(16),
            borderSide: BorderSide(
              color: AppColors.electricBlue,
              width: 2,
            ),
          ),
          errorBorder: OutlineInputBorder(
            borderRadius: borderRadius ?? BorderRadius.circular(16),
            borderSide: BorderSide(
              color: AppColors.error,
              width: 1,
            ),
          ),
        ),
      ),
    );
  }

  /// بطاقة أداة حديثة
  static Widget modernToolCard({
    required String title,
    required String description,
    required IconData icon,
    required VoidCallback onTap,
    Gradient? gradient,
    Color? iconColor,
    bool isActive = false,
    Widget? badge,
  }) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(20),
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: gradient ?? AppColors.modernCardGradient,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isActive 
                    ? AppColors.electricBlue 
                    : AppColors.electricBlue.withValues(alpha: 0.1),
                width: isActive ? 2 : 1,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 15,
                  offset: const Offset(0, 8),
                ),
                if (isActive)
                  BoxShadow(
                    color: AppColors.electricBlue.withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 4),
                  ),
              ],
            ),
            child: Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    gradient: AppColors.glowGradient,
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: (iconColor ?? AppColors.electricBlue).withValues(alpha: 0.3),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(
                    icon,
                    color: iconColor ?? AppColors.white,
                    size: 28,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              title,
                              style: TextStyle(
                                color: AppColors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          if (badge != null) badge,
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        description,
                        style: TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                Icon(
                  Icons.arrow_forward_ios,
                  color: AppColors.electricBlue,
                  size: 16,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// شريط تقدم حديث
  static Widget modernProgressBar({
    required double progress,
    double height = 8,
    Color? backgroundColor,
    Gradient? progressGradient,
    BorderRadius? borderRadius,
    bool showPercentage = false,
    TextStyle? percentageStyle,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (showPercentage)
          Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Text(
              '${(progress * 100).toInt()}%',
              style: percentageStyle ?? TextStyle(
                color: AppColors.electricBlue,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        Container(
          height: height,
          decoration: BoxDecoration(
            color: backgroundColor ?? AppColors.darkGrey,
            borderRadius: borderRadius ?? BorderRadius.circular(height / 2),
          ),
          child: ClipRRect(
            borderRadius: borderRadius ?? BorderRadius.circular(height / 2),
            child: LinearProgressIndicator(
              value: progress,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(
                AppColors.electricBlue,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// شارة حديثة
  static Widget modernBadge({
    required String text,
    Color? backgroundColor,
    Color? textColor,
    EdgeInsets? padding,
    BorderRadius? borderRadius,
    double? fontSize,
  }) {
    return Container(
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: backgroundColor ?? AppColors.electricBlue,
        borderRadius: borderRadius ?? BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: (backgroundColor ?? AppColors.electricBlue).withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        text,
        style: TextStyle(
          color: textColor ?? AppColors.white,
          fontSize: fontSize ?? 12,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// قائمة منسدلة حديثة
  static Widget modernDropdown<T>({
    required T? value,
    required List<DropdownMenuItem<T>> items,
    required Function(T?) onChanged,
    String? hintText,
    IconData? prefixIcon,
    EdgeInsets? contentPadding,
    BorderRadius? borderRadius,
  }) {
    return Container(
      decoration: BoxDecoration(
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: DropdownButtonFormField<T>(
        value: value,
        items: items,
        onChanged: onChanged,
        style: TextStyle(
          color: AppColors.white,
          fontSize: 16,
        ),
        dropdownColor: AppColors.darkGrey,
        decoration: InputDecoration(
          hintText: hintText,
          hintStyle: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 14,
          ),
          prefixIcon: prefixIcon != null
              ? Icon(prefixIcon, color: AppColors.electricBlue)
              : null,
          filled: true,
          fillColor: AppColors.darkGrey.withValues(alpha: 0.8),
          contentPadding: contentPadding ?? 
              const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
          border: OutlineInputBorder(
            borderRadius: borderRadius ?? BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          enabledBorder: OutlineInputBorder(
            borderRadius: borderRadius ?? BorderRadius.circular(16),
            borderSide: BorderSide(
              color: AppColors.electricBlue.withValues(alpha: 0.2),
              width: 1,
            ),
          ),
          focusedBorder: OutlineInputBorder(
            borderRadius: borderRadius ?? BorderRadius.circular(16),
            borderSide: BorderSide(
              color: AppColors.electricBlue,
              width: 2,
            ),
          ),
        ),
      ),
    );
  }

  /// مؤشر تحميل حديث
  static Widget modernLoadingIndicator({
    double size = 40,
    Color? color,
    double strokeWidth = 3,
  }) {
    return SizedBox(
      width: size,
      height: size,
      child: CircularProgressIndicator(
        strokeWidth: strokeWidth,
        valueColor: AlwaysStoppedAnimation<Color>(
          color ?? AppColors.electricBlue,
        ),
      ),
    );
  }

  /// تبديل حديث (Switch)
  static Widget modernSwitch({
    required bool value,
    required Function(bool) onChanged,
    Color? activeColor,
    Color? inactiveColor,
    double? width,
    double? height,
  }) {
    return GestureDetector(
      onTap: () => onChanged(!value),
      child: AnimatedContainer(
        duration: const Duration(milliseconds: 200),
        width: width ?? 50,
        height: height ?? 28,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(14),
          gradient: value 
              ? AppColors.primaryGradient
              : LinearGradient(
                  colors: [AppColors.darkGrey, AppColors.midGrey],
                ),
          boxShadow: [
            BoxShadow(
              color: value 
                  ? AppColors.electricBlue.withValues(alpha: 0.3)
                  : Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: AnimatedAlign(
          duration: const Duration(milliseconds: 200),
          alignment: value ? Alignment.centerRight : Alignment.centerLeft,
          child: Container(
            width: 24,
            height: 24,
            margin: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              color: AppColors.white,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
