import 'package:flutter_test/flutter_test.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';

// Import the services and widgets to test
import '../lib/core/services/enhanced_ai_service.dart';
import '../lib/core/auth/auth_service.dart';
import '../lib/core/analytics/usage_analytics.dart';
import '../lib/core/offline/offline_service.dart';
import '../lib/core/security/security_service.dart';
import '../lib/core/performance/performance_service.dart';
import '../lib/core/services/voice_service.dart';
import '../lib/screens/home_screen.dart';
import '../lib/screens/analytics_screen.dart';
import '../lib/screens/voice_chat_screen.dart';

// Generate mocks
@GenerateMocks([
  EnhancedAIService,
  AuthService,
  UsageAnalytics,
  OfflineService,
  SecurityService,
  PerformanceService,
  VoiceService,
])
import 'comprehensive_test_suite.mocks.dart';

void main() {
  group('🧪 DeepSeek AI - Comprehensive Test Suite', () {
    
    // ==================== UNIT TESTS ====================
    group('📦 Unit Tests', () {
      
      group('🔐 Authentication Service Tests', () {
        test('should login as guest successfully', () async {
          // Arrange
          await AuthService.initialize();
          
          // Act
          final user = await AuthService.loginAsGuest();
          
          // Assert
          expect(user.type, UserType.guest);
          expect(user.name, 'مستخدم ضيف');
          expect(user.email, '<EMAIL>');
          expect(AuthService.isLoggedIn, true);
          expect(AuthService.isGuest, true);
        });

        test('should validate email format correctly', () {
          // Test valid emails
          expect(AuthService.loginWithEmail('<EMAIL>', 'password123'), 
                 completes);
          
          // Test invalid emails - should throw exception
          expect(() => AuthService.loginWithEmail('invalid-email', 'password123'), 
                 throwsA(isA<AuthException>()));
        });

        test('should update user preferences', () async {
          // Arrange
          await AuthService.initialize();
          await AuthService.loginAsGuest();
          
          final newPreferences = UserPreferences(
            language: 'en',
            theme: 'light',
            notifications: false,
            defaultAIProvider: 'anthropic',
            customSettings: {'test': 'value'},
          );
          
          // Act
          await AuthService.updateUserPreferences(newPreferences);
          
          // Assert
          expect(AuthService.currentUser?.preferences.language, 'en');
          expect(AuthService.currentUser?.preferences.theme, 'light');
          expect(AuthService.currentUser?.preferences.notifications, false);
        });
      });

      group('📊 Analytics Service Tests', () {
        test('should track tool usage correctly', () async {
          // Arrange
          await UsageAnalytics.initialize();
          
          // Act
          await UsageAnalytics.trackToolUsage('chat');
          await UsageAnalytics.trackToolUsage('image_generation');
          await UsageAnalytics.trackToolUsage('chat'); // Second time
          
          // Assert
          final stats = await UsageAnalytics.getUsageStats();
          expect(stats.totalToolUsage, 3);
          expect(stats.toolsUsage['chat'], 2);
          expect(stats.toolsUsage['image_generation'], 1);
        });

        test('should track provider usage correctly', () async {
          // Arrange
          await UsageAnalytics.initialize();
          
          // Act
          await UsageAnalytics.trackProviderUsage('openai');
          await UsageAnalytics.trackProviderUsage('anthropic');
          await UsageAnalytics.trackProviderUsage('openai');
          
          // Assert
          final stats = await UsageAnalytics.getUsageStats();
          expect(stats.providersUsage['openai'], 2);
          expect(stats.providersUsage['anthropic'], 1);
        });

        test('should track token usage correctly', () async {
          // Arrange
          await UsageAnalytics.initialize();
          
          // Act
          await UsageAnalytics.trackTokenUsage(100, 'openai');
          await UsageAnalytics.trackTokenUsage(50, 'anthropic');
          await UsageAnalytics.trackTokenUsage(75, 'openai');
          
          // Assert
          final stats = await UsageAnalytics.getUsageStats();
          expect(stats.totalTokensUsed, 225);
          expect(stats.tokensUsed['openai'], 175);
          expect(stats.tokensUsed['anthropic'], 50);
        });
      });

      group('🔒 Security Service Tests', () {
        test('should encrypt and decrypt data correctly', () async {
          // Arrange
          await SecurityService.initialize();
          const testData = 'This is sensitive data';
          
          // Act
          final encrypted = SecurityService.encryptSensitiveData(testData);
          final decrypted = SecurityService.decryptSensitiveData(encrypted);
          
          // Assert
          expect(decrypted, testData);
          expect(encrypted, isNot(testData));
        });

        test('should validate password strength correctly', () {
          // Test weak password
          final weakResult = SecurityService.checkPasswordStrength('123');
          expect(weakResult.level, PasswordStrengthLevel.weak);
          expect(weakResult.issues.length, greaterThan(0));
          
          // Test medium password
          final mediumResult = SecurityService.checkPasswordStrength('Password123');
          expect(mediumResult.level, PasswordStrengthLevel.medium);
          
          // Test strong password
          final strongResult = SecurityService.checkPasswordStrength('MyStr0ng!P@ssw0rd');
          expect(strongResult.level, PasswordStrengthLevel.strong);
          expect(strongResult.issues.length, 0);
        });

        test('should validate API keys correctly', () {
          // Valid API key
          expect(SecurityService.validateApiKey('sk-1234567890abcdef1234567890abcdef'), true);
          
          // Invalid API keys
          expect(SecurityService.validateApiKey(''), false);
          expect(SecurityService.validateApiKey('short'), false);
          expect(SecurityService.validateApiKey('invalid@key!'), false);
        });

        test('should sanitize input correctly', () {
          const maliciousInput = '<script>alert("xss")</script>Hello World\'; DROP TABLE users;';
          final sanitized = SecurityService.sanitizeInput(maliciousInput);
          
          expect(sanitized, 'Hello World DROP TABLE users');
          expect(sanitized, isNot(contains('<script>')));
          expect(sanitized, isNot(contains('\'')));
        });
      });

      group('🌐 Offline Service Tests', () {
        test('should cache responses correctly', () async {
          // Arrange
          await OfflineService.initialize();
          const query = 'What is AI?';
          const response = 'AI is artificial intelligence';
          
          // Act
          await OfflineService.cacheResponse(query, response);
          final cachedResponse = OfflineService.getCachedResponse(query);
          
          // Assert
          expect(cachedResponse, response);
        });

        test('should handle offline responses', () {
          final response = OfflineService.getOfflineResponse('مرحبا');
          expect(response, contains('مرحباً بك'));
          
          final unknownResponse = OfflineService.getOfflineResponse('complex query');
          expect(unknownResponse, contains('بدون اتصال'));
        });

        test('should add pending tasks', () async {
          // Arrange
          await OfflineService.initialize();
          
          final task = OfflineTask(
            id: 'test_task_1',
            type: TaskType.chatMessage,
            data: {'message': 'Hello', 'conversationId': 'conv_1'},
            createdAt: DateTime.now(),
          );
          
          // Act
          await OfflineService.addPendingTask(task);
          final stats = OfflineService.getStats();
          
          // Assert
          expect(stats.pendingTasksCount, 1);
        });
      });

      group('⚡ Performance Service Tests', () {
        test('should initialize with default settings', () async {
          // Act
          await PerformanceService.initialize();
          
          // Assert
          final settings = PerformanceService.settings;
          expect(settings.enableMonitoring, true);
          expect(settings.enableAutoOptimization, true);
          expect(settings.monitoringInterval, 30);
        });

        test('should update settings correctly', () async {
          // Arrange
          await PerformanceService.initialize();
          
          final newSettings = PerformanceSettings(
            enableMonitoring: false,
            enableAutoOptimization: false,
            monitoringInterval: 60,
            memoryThresholdMB: 200,
            storageThresholdMB: 1000,
            networkLatencyThresholdMs: 2000,
          );
          
          // Act
          await PerformanceService.updateSettings(newSettings);
          
          // Assert
          final settings = PerformanceService.settings;
          expect(settings.enableMonitoring, false);
          expect(settings.monitoringInterval, 60);
          expect(settings.memoryThresholdMB, 200);
        });

        test('should perform optimization', () async {
          // Arrange
          await PerformanceService.initialize();
          
          // Act
          final result = await PerformanceService.performFullOptimization();
          
          // Assert
          expect(result.success, true);
          expect(result.memoryOptimized, true);
          expect(result.storageOptimized, true);
          expect(result.networkOptimized, true);
          expect(result.optimizationTimeMs, greaterThan(0));
        });
      });
    });

    // ==================== WIDGET TESTS ====================
    group('🎨 Widget Tests', () {
      
      testWidgets('HomeScreen should render correctly', (WidgetTester tester) async {
        // Arrange
        const app = ProviderScope(
          child: MaterialApp(
            home: HomeScreen(),
          ),
        );
        
        // Act
        await tester.pumpWidget(app);
        await tester.pumpAndSettle();
        
        // Assert
        expect(find.text('DeepSeek AI'), findsOneWidget);
        expect(find.byType(AppBar), findsOneWidget);
        expect(find.byType(GridView), findsOneWidget);
      });

      testWidgets('AnalyticsScreen should display stats', (WidgetTester tester) async {
        // Arrange
        await UsageAnalytics.initialize();
        await UsageAnalytics.trackToolUsage('chat');
        
        const app = ProviderScope(
          child: MaterialApp(
            home: AnalyticsScreen(),
          ),
        );
        
        // Act
        await tester.pumpWidget(app);
        await tester.pumpAndSettle();
        
        // Assert
        expect(find.text('الإحصائيات والتحليلات'), findsOneWidget);
        expect(find.byType(TabBar), findsOneWidget);
        expect(find.text('عام'), findsOneWidget);
        expect(find.text('الأدوات'), findsOneWidget);
        expect(find.text('المقدمين'), findsOneWidget);
      });

      testWidgets('VoiceChatScreen should render voice controls', (WidgetTester tester) async {
        // Arrange
        const app = ProviderScope(
          child: MaterialApp(
            home: VoiceChatScreen(),
          ),
        );
        
        // Act
        await tester.pumpWidget(app);
        await tester.pumpAndSettle();
        
        // Assert
        expect(find.text('الدردشة الصوتية'), findsOneWidget);
        expect(find.byIcon(Icons.mic_none), findsOneWidget);
        expect(find.text('اضغط للتحدث'), findsOneWidget);
      });
    });

    // ==================== INTEGRATION TESTS ====================
    group('🔗 Integration Tests', () {
      
      testWidgets('Complete user flow: Login -> Use Tool -> View Analytics', 
          (WidgetTester tester) async {
        // This would be a comprehensive integration test
        // covering the entire user journey
        
        // 1. Initialize services
        await AuthService.initialize();
        await UsageAnalytics.initialize();
        
        // 2. Login as guest
        await AuthService.loginAsGuest();
        expect(AuthService.isLoggedIn, true);
        
        // 3. Use a tool (simulate)
        await UsageAnalytics.trackToolUsage('chat');
        
        // 4. Check analytics
        final stats = await UsageAnalytics.getUsageStats();
        expect(stats.totalToolUsage, 1);
        expect(stats.toolsUsage['chat'], 1);
      });

      test('Offline mode integration test', () async {
        // 1. Initialize offline service
        await OfflineService.initialize();
        
        // 2. Add some cached responses
        await OfflineService.cacheResponse('Hello', 'Hi there!');
        await OfflineService.cacheResponse('مرحبا', 'مرحباً بك!');
        
        // 3. Test retrieval
        expect(OfflineService.getCachedResponse('Hello'), 'Hi there!');
        expect(OfflineService.getCachedResponse('مرحبا'), 'مرحباً بك!');
        
        // 4. Test offline responses
        final offlineResponse = OfflineService.getOfflineResponse('مرحبا');
        expect(offlineResponse, contains('مرحباً بك'));
      });

      test('Security integration test', () async {
        // 1. Initialize security service
        await SecurityService.initialize();
        
        // 2. Test encryption workflow
        const sensitiveData = 'user_api_key_12345';
        final encrypted = SecurityService.encryptSensitiveData(sensitiveData);
        final decrypted = SecurityService.decryptSensitiveData(encrypted);
        
        expect(decrypted, sensitiveData);
        expect(encrypted, isNot(sensitiveData));
        
        // 3. Test input sanitization
        const maliciousInput = '<script>alert("test")</script>Hello';
        final sanitized = SecurityService.sanitizeInput(maliciousInput);
        expect(sanitized, 'Hello');
        
        // 4. Test password validation
        final strongPassword = SecurityService.checkPasswordStrength('MyStr0ng!P@ssw0rd');
        expect(strongPassword.level, PasswordStrengthLevel.strong);
      });
    });

    // ==================== PERFORMANCE TESTS ====================
    group('⚡ Performance Tests', () {
      
      test('Service initialization performance', () async {
        final stopwatch = Stopwatch()..start();
        
        // Initialize all services
        await Future.wait([
          AuthService.initialize(),
          UsageAnalytics.initialize(),
          OfflineService.initialize(),
          SecurityService.initialize(),
          PerformanceService.initialize(),
        ]);
        
        stopwatch.stop();
        
        // Should initialize within reasonable time (5 seconds)
        expect(stopwatch.elapsedMilliseconds, lessThan(5000));
        print('🚀 Services initialized in ${stopwatch.elapsedMilliseconds}ms');
      });

      test('Encryption performance test', () async {
        await SecurityService.initialize();
        
        const testData = 'This is a test string for encryption performance testing. ' * 100;
        final stopwatch = Stopwatch()..start();
        
        // Perform 100 encryption/decryption cycles
        for (int i = 0; i < 100; i++) {
          final encrypted = SecurityService.encryptSensitiveData(testData);
          final decrypted = SecurityService.decryptSensitiveData(encrypted);
          expect(decrypted, testData);
        }
        
        stopwatch.stop();
        
        // Should complete within reasonable time
        expect(stopwatch.elapsedMilliseconds, lessThan(1000));
        print('🔐 100 encryption cycles completed in ${stopwatch.elapsedMilliseconds}ms');
      });

      test('Analytics tracking performance', () async {
        await UsageAnalytics.initialize();
        
        final stopwatch = Stopwatch()..start();
        
        // Track 1000 events
        for (int i = 0; i < 1000; i++) {
          await UsageAnalytics.trackToolUsage('performance_test');
          await UsageAnalytics.trackProviderUsage('test_provider');
          await UsageAnalytics.trackTokenUsage(10, 'test_provider');
        }
        
        stopwatch.stop();
        
        // Should complete within reasonable time
        expect(stopwatch.elapsedMilliseconds, lessThan(2000));
        print('📊 1000 analytics events tracked in ${stopwatch.elapsedMilliseconds}ms');
        
        // Verify data integrity
        final stats = await UsageAnalytics.getUsageStats();
        expect(stats.totalToolUsage, 1000);
        expect(stats.totalTokensUsed, 10000);
      });
    });

    // ==================== ERROR HANDLING TESTS ====================
    group('🚨 Error Handling Tests', () {
      
      test('should handle invalid encryption data gracefully', () async {
        await SecurityService.initialize();
        
        expect(
          () => SecurityService.decryptSensitiveData('invalid_encrypted_data'),
          throwsA(isA<SecurityException>()),
        );
      });

      test('should handle network errors in offline mode', () async {
        await OfflineService.initialize();
        
        // Test with no network connection
        final response = OfflineService.getOfflineResponse('complex query');
        expect(response, contains('بدون اتصال'));
      });

      test('should handle invalid authentication attempts', () async {
        await AuthService.initialize();
        
        expect(
          () => AuthService.loginWithEmail('', ''),
          throwsA(isA<AuthException>()),
        );
        
        expect(
          () => AuthService.loginWithEmail('invalid-email', 'password'),
          throwsA(isA<AuthException>()),
        );
      });
    });
  });
}

// ==================== TEST UTILITIES ====================
class TestUtils {
  static Future<void> initializeAllServices() async {
    await Future.wait([
      AuthService.initialize(),
      UsageAnalytics.initialize(),
      OfflineService.initialize(),
      SecurityService.initialize(),
      PerformanceService.initialize(),
    ]);
  }

  static Future<void> cleanupAllServices() async {
    await AuthService.logout();
    await UsageAnalytics.clearAnalytics();
    await OfflineService.clearCache();
    await SecurityService.clearSecurityLogs();
    PerformanceService.dispose();
  }

  static Widget createTestApp(Widget child) {
    return ProviderScope(
      child: MaterialApp(
        home: child,
        theme: ThemeData.dark(),
      ),
    );
  }
}

// ==================== MOCK DATA ====================
class MockData {
  static const sampleUser = {
    'id': 'test_user_123',
    'name': 'Test User',
    'email': '<EMAIL>',
    'type': 'UserType.registered',
  };

  static const sampleAnalytics = {
    'totalSessions': 10,
    'totalToolUsage': 50,
    'totalTokensUsed': 1000,
    'toolsUsage': {'chat': 20, 'image_generation': 15, 'text_summarization': 15},
    'providersUsage': {'openai': 30, 'anthropic': 20},
  };

  static const sampleSecurityLog = {
    'type': 'SecurityEventType.dataEncryption',
    'description': 'Test encryption event',
    'timestamp': '2024-01-01T00:00:00.000Z',
    'severity': 'SecuritySeverity.info',
  };
}
