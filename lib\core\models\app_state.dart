import 'package:equatable/equatable.dart';

/// حالة التطبيق الرئيسية
class AppState extends Equatable {
  final bool isFirstLaunch;
  final bool isLoggedIn;
  final bool isLoading;
  final String? error;
  final String currentRoute;

  const AppState({
    required this.isFirstLaunch,
    required this.isLoggedIn,
    required this.isLoading,
    this.error,
    required this.currentRoute,
  });

  factory AppState.initial() {
    return const AppState(
      isFirstLaunch: true,
      isLoggedIn: false,
      isLoading: true,
      error: null,
      currentRoute: '/',
    );
  }

  AppState copyWith({
    bool? isFirstLaunch,
    bool? isLoggedIn,
    bool? isLoading,
    String? error,
    String? currentRoute,
  }) {
    return AppState(
      isFirstLaunch: isFirstLaunch ?? this.isFirstLaunch,
      isLoggedIn: isLoggedIn ?? this.isLoggedIn,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      currentRoute: currentRoute ?? this.currentRoute,
    );
  }

  @override
  List<Object?> get props => [
        isFirstLaunch,
        isLoggedIn,
        isLoading,
        error,
        currentRoute,
      ];
}

/// إعدادات التطبيق
class AppSettings extends Equatable {
  final bool isDarkMode;
  final String language;
  final double fontSize;
  final bool enableNotifications;
  final bool enableAnimations;
  final bool autoSave;
  final int maxTokens;
  final double temperature;

  const AppSettings({
    required this.isDarkMode,
    required this.language,
    required this.fontSize,
    required this.enableNotifications,
    required this.enableAnimations,
    required this.autoSave,
    required this.maxTokens,
    required this.temperature,
  });

  factory AppSettings.defaultSettings() {
    return const AppSettings(
      isDarkMode: true,
      language: 'ar',
      fontSize: 16.0,
      enableNotifications: true,
      enableAnimations: true,
      autoSave: true,
      maxTokens: 2048,
      temperature: 0.7,
    );
  }

  AppSettings copyWith({
    bool? isDarkMode,
    String? language,
    double? fontSize,
    bool? enableNotifications,
    bool? enableAnimations,
    bool? autoSave,
    int? maxTokens,
    double? temperature,
  }) {
    return AppSettings(
      isDarkMode: isDarkMode ?? this.isDarkMode,
      language: language ?? this.language,
      fontSize: fontSize ?? this.fontSize,
      enableNotifications: enableNotifications ?? this.enableNotifications,
      enableAnimations: enableAnimations ?? this.enableAnimations,
      autoSave: autoSave ?? this.autoSave,
      maxTokens: maxTokens ?? this.maxTokens,
      temperature: temperature ?? this.temperature,
    );
  }

  @override
  List<Object?> get props => [
        isDarkMode,
        language,
        fontSize,
        enableNotifications,
        enableAnimations,
        autoSave,
        maxTokens,
        temperature,
      ];
}
