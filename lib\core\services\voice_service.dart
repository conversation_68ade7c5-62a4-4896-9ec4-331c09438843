import 'package:flutter/foundation.dart';
import 'package:speech_to_text/speech_to_text.dart' as stt;
import 'package:flutter_tts/flutter_tts.dart';

/// خدمة الصوت - تحويل الكلام إلى نص والعكس
class VoiceService {
  static stt.SpeechToText? _speechToText;
  static FlutterTts? _flutterTts;
  static bool _isInitialized = false;
  static bool _isListening = false;
  static bool _isSpeaking = false;

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة Speech to Text
      _speechToText = stt.SpeechToText();
      final sttAvailable = await _speechToText!.initialize(
        onError: (error) => debugPrint('❌ خطأ في Speech to Text: $error'),
        onStatus: (status) => debugPrint('📢 حالة Speech to Text: $status'),
      );

      // تهيئة Text to Speech
      _flutterTts = FlutterTts();
      await _configureTts();

      _isInitialized = sttAvailable;
      debugPrint('✅ تم تهيئة خدمة الصوت: $_isInitialized');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الصوت: $e');
      _isInitialized = false;
    }
  }

  /// تكوين Text to Speech
  static Future<void> _configureTts() async {
    if (_flutterTts == null) return;

    await _flutterTts!.setLanguage('ar-SA'); // العربية السعودية
    await _flutterTts!.setSpeechRate(0.5); // سرعة متوسطة
    await _flutterTts!.setVolume(1.0); // أقصى صوت
    await _flutterTts!.setPitch(1.0); // نبرة طبيعية

    // إعداد callbacks
    _flutterTts!.setStartHandler(() {
      _isSpeaking = true;
      debugPrint('🔊 بدء التحدث');
    });

    _flutterTts!.setCompletionHandler(() {
      _isSpeaking = false;
      debugPrint('✅ انتهاء التحدث');
    });

    _flutterTts!.setErrorHandler((message) {
      _isSpeaking = false;
      debugPrint('❌ خطأ في التحدث: $message');
    });
  }

  /// التحقق من توفر الخدمة
  static bool get isAvailable => _isInitialized;

  /// التحقق من حالة الاستماع
  static bool get isListening => _isListening;

  /// التحقق من حالة التحدث
  static bool get isSpeaking => _isSpeaking;

  /// بدء الاستماع وتحويل الكلام إلى نص
  static Future<String?> startListening({
    String language = 'ar-SA',
    Duration timeout = const Duration(seconds: 30),
  }) async {
    if (!_isInitialized || _speechToText == null) {
      throw Exception('خدمة الصوت غير مهيأة');
    }

    if (_isListening) {
      throw Exception('الاستماع قيد التشغيل بالفعل');
    }

    try {
      String recognizedText = '';
      bool isCompleted = false;

      await _speechToText!.listen(
        onResult: (result) {
          recognizedText = result.recognizedWords;
          isCompleted = result.finalResult;
          debugPrint('🎤 النص المتعرف عليه: $recognizedText');
        },
        listenFor: timeout,
        pauseFor: const Duration(seconds: 3),
        partialResults: true,
        localeId: language,
        cancelOnError: true,
        listenMode: stt.ListenMode.confirmation,
      );

      _isListening = true;

      // انتظار اكتمال التعرف أو انتهاء المهلة
      final startTime = DateTime.now();
      while (!isCompleted && _isListening) {
        await Future.delayed(const Duration(milliseconds: 100));
        
        // التحقق من انتهاء المهلة
        if (DateTime.now().difference(startTime) > timeout) {
          break;
        }
      }

      await stopListening();
      return recognizedText.isNotEmpty ? recognizedText : null;
    } catch (e) {
      debugPrint('❌ خطأ في الاستماع: $e');
      await stopListening();
      throw Exception('فشل في تحويل الكلام إلى نص: $e');
    }
  }

  /// إيقاف الاستماع
  static Future<void> stopListening() async {
    if (_speechToText != null && _isListening) {
      await _speechToText!.stop();
      _isListening = false;
      debugPrint('⏹️ تم إيقاف الاستماع');
    }
  }

  /// تحويل النص إلى كلام
  static Future<void> speak(
    String text, {
    String language = 'ar-SA',
    double rate = 0.5,
    double volume = 1.0,
    double pitch = 1.0,
  }) async {
    if (!_isInitialized || _flutterTts == null) {
      throw Exception('خدمة الصوت غير مهيأة');
    }

    if (_isSpeaking) {
      await stopSpeaking();
    }

    try {
      await _flutterTts!.setLanguage(language);
      await _flutterTts!.setSpeechRate(rate);
      await _flutterTts!.setVolume(volume);
      await _flutterTts!.setPitch(pitch);

      await _flutterTts!.speak(text);
      debugPrint('🔊 بدء تحويل النص إلى كلام: ${text.substring(0, text.length > 50 ? 50 : text.length)}...');
    } catch (e) {
      debugPrint('❌ خطأ في تحويل النص إلى كلام: $e');
      throw Exception('فشل في تحويل النص إلى كلام: $e');
    }
  }

  /// إيقاف التحدث
  static Future<void> stopSpeaking() async {
    if (_flutterTts != null && _isSpeaking) {
      await _flutterTts!.stop();
      _isSpeaking = false;
      debugPrint('⏹️ تم إيقاف التحدث');
    }
  }

  /// إيقاف مؤقت للتحدث
  static Future<void> pauseSpeaking() async {
    if (_flutterTts != null && _isSpeaking) {
      await _flutterTts!.pause();
      debugPrint('⏸️ تم إيقاف التحدث مؤقتاً');
    }
  }

  /// الحصول على اللغات المتاحة للتعرف على الكلام
  static Future<List<String>> getAvailableLanguages() async {
    if (!_isInitialized || _speechToText == null) {
      return [];
    }

    try {
      final locales = await _speechToText!.locales();
      return locales.map((locale) => locale.localeId).toList();
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على اللغات: $e');
      return [];
    }
  }

  /// الحصول على الأصوات المتاحة للتحدث
  static Future<List<Map<String, String>>> getAvailableVoices() async {
    if (!_isInitialized || _flutterTts == null) {
      return [];
    }

    try {
      final voices = await _flutterTts!.getVoices;
      if (voices is List) {
        return voices.cast<Map<String, String>>();
      }
      return [];
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على الأصوات: $e');
      return [];
    }
  }

  /// تعيين صوت محدد
  static Future<void> setVoice(Map<String, String> voice) async {
    if (!_isInitialized || _flutterTts == null) {
      throw Exception('خدمة الصوت غير مهيأة');
    }

    try {
      await _flutterTts!.setVoice(voice);
      debugPrint('🎵 تم تعيين الصوت: ${voice['name']}');
    } catch (e) {
      debugPrint('❌ خطأ في تعيين الصوت: $e');
      throw Exception('فشل في تعيين الصوت: $e');
    }
  }

  /// التحقق من دعم اللغة
  static Future<bool> isLanguageSupported(String language) async {
    final availableLanguages = await getAvailableLanguages();
    return availableLanguages.contains(language);
  }

  /// تنظيف الموارد
  static Future<void> dispose() async {
    await stopListening();
    await stopSpeaking();
    
    _speechToText = null;
    _flutterTts = null;
    _isInitialized = false;
    _isListening = false;
    _isSpeaking = false;
    
    debugPrint('🧹 تم تنظيف موارد خدمة الصوت');
  }

  /// تحويل نص طويل إلى كلام مع تقسيم
  static Future<void> speakLongText(
    String text, {
    String language = 'ar-SA',
    int maxChunkLength = 200,
    Duration pauseBetweenChunks = const Duration(milliseconds: 500),
  }) async {
    if (text.isEmpty) return;

    // تقسيم النص إلى أجزاء
    final chunks = _splitTextIntoChunks(text, maxChunkLength);
    
    for (int i = 0; i < chunks.length; i++) {
      if (!_isSpeaking && i > 0) break; // إذا تم إيقاف التحدث
      
      await speak(chunks[i], language: language);
      
      // انتظار انتهاء الجزء الحالي
      while (_isSpeaking) {
        await Future.delayed(const Duration(milliseconds: 100));
      }
      
      // توقف قصير بين الأجزاء
      if (i < chunks.length - 1) {
        await Future.delayed(pauseBetweenChunks);
      }
    }
  }

  /// تقسيم النص إلى أجزاء
  static List<String> _splitTextIntoChunks(String text, int maxLength) {
    if (text.length <= maxLength) return [text];

    final chunks = <String>[];
    final sentences = text.split(RegExp(r'[.!?؟]'));
    
    String currentChunk = '';
    
    for (final sentence in sentences) {
      final trimmedSentence = sentence.trim();
      if (trimmedSentence.isEmpty) continue;
      
      if (currentChunk.length + trimmedSentence.length <= maxLength) {
        currentChunk += (currentChunk.isEmpty ? '' : '. ') + trimmedSentence;
      } else {
        if (currentChunk.isNotEmpty) {
          chunks.add(currentChunk);
          currentChunk = trimmedSentence;
        } else {
          // الجملة طويلة جداً، قسمها بالكلمات
          final words = trimmedSentence.split(' ');
          String wordChunk = '';
          
          for (final word in words) {
            if (wordChunk.length + word.length <= maxLength) {
              wordChunk += (wordChunk.isEmpty ? '' : ' ') + word;
            } else {
              if (wordChunk.isNotEmpty) {
                chunks.add(wordChunk);
                wordChunk = word;
              } else {
                chunks.add(word); // كلمة واحدة طويلة جداً
              }
            }
          }
          
          if (wordChunk.isNotEmpty) {
            currentChunk = wordChunk;
          }
        }
      }
    }
    
    if (currentChunk.isNotEmpty) {
      chunks.add(currentChunk);
    }
    
    return chunks;
  }
}
