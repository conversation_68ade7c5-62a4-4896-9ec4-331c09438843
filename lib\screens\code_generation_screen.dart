import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../core/services/enhanced_ai_service.dart';

class CodeGenerationScreen extends StatefulWidget {
  const CodeGenerationScreen({super.key});

  @override
  State<CodeGenerationScreen> createState() => _CodeGenerationScreenState();
}

class _CodeGenerationScreenState extends State<CodeGenerationScreen>
    with TickerProviderStateMixin {
  final TextEditingController _promptController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  String? _generatedCode;
  bool _isGenerating = false;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<String> _programmingLanguages = [
    'Python',
    'JavaScript',
    'Java',
    'C++',
    'C#',
    'PHP',
    'Swift',
    'Kotlin',
    'Go',
    'Rust',
    'TypeScript',
    'Dart',
  ];
  String _selectedLanguage = 'Python';

  final List<String> _codeTypes = [
    'دالة',
    'كلاس',
    'API',
    'خوارزمية',
    'واجهة مستخدم',
    'قاعدة بيانات',
    'اختبار',
    'مشروع كامل',
  ];
  String _selectedCodeType = 'دالة';

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _promptController.dispose();
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _generateCode() async {
    final prompt = _promptController.text.trim();
    if (prompt.isEmpty) {
      _showErrorSnackBar('يرجى كتابة وصف للكود المطلوب');
      return;
    }

    setState(() {
      _isGenerating = true;
      _generatedCode = null;
    });

    try {
      final enhancedPrompt = _buildEnhancedPrompt(prompt);

      final result = await EnhancedAIService.generateCode(
        prompt: enhancedPrompt,
        language: _selectedLanguage,
        codeType: _selectedCodeType,
      );

      setState(() {
        _generatedCode = result;
        _isGenerating = false;
      });

      _scrollToBottom();
    } catch (e) {
      setState(() {
        _isGenerating = false;
      });

      String errorMessage = 'فشل في توليد الكود';
      if (e.toString().contains('API')) {
        errorMessage = 'يرجى إعداد مفتاح API في الإعدادات المتقدمة';
      } else if (e.toString().contains('إنترنت')) {
        errorMessage = 'لا يوجد اتصال بالإنترنت';
      }

      _showErrorSnackBar(errorMessage);
    }
  }

  String _buildEnhancedPrompt(String userPrompt) {
    return '''
اكتب كود ${_selectedCodeType} بلغة ${_selectedLanguage} بناءً على الوصف التالي:

${userPrompt}

متطلبات:
1. الكود يجب أن يكون واضح ومفهوم
2. أضف تعليقات باللغة العربية لشرح الكود
3. اتبع أفضل الممارسات في البرمجة
4. تأكد من أن الكود قابل للتشغيل
5. أضف معالجة للأخطاء إذا لزم الأمر

يرجى تقديم الكود فقط بدون شرح إضافي.
''';
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  void _copyCode() {
    if (_generatedCode != null) {
      Clipboard.setData(ClipboardData(text: _generatedCode!));
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم نسخ الكود'),
          duration: Duration(seconds: 2),
        ),
      );
    }
  }

  void _clearCode() {
    setState(() {
      _generatedCode = null;
      _promptController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.darkPurple.withValues(alpha: 0.9),
              AppColors.background,
              AppColors.primaryPurple.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                _buildHeader(),
                _buildLanguageSelector(),
                _buildCodeTypeSelector(),
                Expanded(child: _buildMainContent()),
                _buildPromptInput(),
                _buildActionButtons(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.electricBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => Navigator.pop(context),
                borderRadius: BorderRadius.circular(12),
                child: const Padding(
                  padding: EdgeInsets.all(12),
                  child: Icon(Icons.arrow_back, color: Colors.white, size: 20),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.code_outlined,
                      color: AppColors.electricBlue,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'توليد الكود',
                      style: AppTextStyles.heading2.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Text(
                  'اكتب كود برمجي بالذكاء الاصطناعي',
                  style: AppTextStyles.body.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
          Row(
            children: [
              Container(
                decoration: BoxDecoration(
                  color: AppColors.darkGrey.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppColors.electricBlue.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      Navigator.pushNamed(context, '/advanced_api_management');
                    },
                    borderRadius: BorderRadius.circular(12),
                    child: const Padding(
                      padding: EdgeInsets.all(12),
                      child: Icon(Icons.settings, color: Colors.white, size: 20),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Container(
                decoration: BoxDecoration(
                  color: AppColors.darkGrey.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: AppColors.electricBlue.withValues(alpha: 0.3),
                    width: 1,
                  ),
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _clearCode,
                    borderRadius: BorderRadius.circular(12),
                    child: const Padding(
                      padding: EdgeInsets.all(12),
                      child: Icon(Icons.refresh, color: Colors.white, size: 20),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildLanguageSelector() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _programmingLanguages.length,
        itemBuilder: (context, index) {
          final language = _programmingLanguages[index];
          final isSelected = language == _selectedLanguage;

          return Container(
            margin: const EdgeInsets.only(right: 12),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  setState(() {
                    _selectedLanguage = language;
                  });
                },
                borderRadius: BorderRadius.circular(25),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 20,
                    vertical: 12,
                  ),
                  decoration: BoxDecoration(
                    gradient: isSelected
                        ? LinearGradient(
                            colors: [
                              AppColors.primaryPurple,
                              AppColors.electricBlue,
                            ],
                          )
                        : LinearGradient(
                            colors: [
                              AppColors.darkGrey.withValues(alpha: 0.5),
                              AppColors.darkGrey.withValues(alpha: 0.3),
                            ],
                          ),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: isSelected
                          ? AppColors.electricBlue
                          : AppColors.electricBlue.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    language,
                    style: AppTextStyles.body.copyWith(
                      color: Colors.white,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildCodeTypeSelector() {
    return Container(
      height: 50,
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _codeTypes.length,
        itemBuilder: (context, index) {
          final type = _codeTypes[index];
          final isSelected = type == _selectedCodeType;

          return Container(
            margin: const EdgeInsets.only(right: 12),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  setState(() {
                    _selectedCodeType = type;
                  });
                },
                borderRadius: BorderRadius.circular(20),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    color: isSelected
                        ? AppColors.electricBlue.withValues(alpha: 0.3)
                        : AppColors.darkGrey.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected
                          ? AppColors.electricBlue
                          : AppColors.electricBlue.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    type,
                    style: AppTextStyles.body.copyWith(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMainContent() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.electricBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          if (_generatedCode == null && !_isGenerating) _buildWelcomeMessage(),
          if (_isGenerating) _buildGeneratingIndicator(),
          if (_generatedCode != null) Expanded(child: _buildCodeResult()),
        ],
      ),
    );
  }

  Widget _buildWelcomeMessage() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.code,
            size: 64,
            color: AppColors.electricBlue,
          ),
          const SizedBox(height: 16),
          Text(
            'مولد الكود الذكي',
            style: AppTextStyles.heading2.copyWith(
              color: Colors.white,
              fontSize: 20,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اكتب وصفاً للكود الذي تريده وسيقوم الذكاء الاصطناعي بكتابته لك',
            style: AppTextStyles.body.copyWith(
              color: AppColors.textSecondary,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildGeneratingIndicator() {
    return Container(
      padding: const EdgeInsets.all(32),
      child: Column(
        children: [
          SizedBox(
            width: 60,
            height: 60,
            child: CircularProgressIndicator(
              strokeWidth: 4,
              valueColor: AlwaysStoppedAnimation<Color>(
                AppColors.electricBlue,
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'جاري كتابة الكود...',
            style: AppTextStyles.body.copyWith(
              color: AppColors.electricBlue,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCodeResult() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.8),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.electricBlue.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.code,
                  color: AppColors.electricBlue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'الكود المولد - $_selectedLanguage',
                  style: AppTextStyles.heading2.copyWith(
                    color: Colors.white,
                    fontSize: 16,
                  ),
                ),
                const Spacer(),
                IconButton(
                  onPressed: _copyCode,
                  icon: Icon(
                    Icons.copy,
                    color: AppColors.electricBlue,
                    size: 20,
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              controller: _scrollController,
              padding: const EdgeInsets.all(16),
              child: SelectableText(
                _generatedCode!,
                style: AppTextStyles.body.copyWith(
                  color: Colors.white,
                  fontFamily: 'monospace',
                  fontSize: 14,
                  height: 1.4,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPromptInput() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.electricBlue.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: TextField(
        controller: _promptController,
        style: AppTextStyles.body.copyWith(
          color: Colors.white,
          fontSize: 16,
        ),
        maxLines: 4,
        minLines: 2,
        decoration: InputDecoration(
          hintText: 'اكتب وصفاً للكود الذي تريده...\nمثال: اكتب دالة لحساب المتوسط الحسابي لقائمة من الأرقام',
          hintStyle: AppTextStyles.body.copyWith(
            color: AppColors.textSecondary,
            fontSize: 16,
          ),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _clearCode,
              icon: const Icon(Icons.clear),
              label: const Text('مسح'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.darkGrey,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            flex: 2,
            child: ElevatedButton.icon(
              onPressed: _isGenerating ? null : _generateCode,
              icon: _isGenerating
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Icon(Icons.code),
              label: Text(_isGenerating ? 'جاري الكتابة...' : 'توليد الكود'),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryPurple,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
