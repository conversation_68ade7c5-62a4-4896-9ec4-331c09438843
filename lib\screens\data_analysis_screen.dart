import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../core/services/enhanced_ai_service.dart';
import '../widgets/modern_ui_components.dart';

class DataAnalysisScreen extends StatefulWidget {
  const DataAnalysisScreen({super.key});

  @override
  State<DataAnalysisScreen> createState() => _DataAnalysisScreenState();
}

class _DataAnalysisScreenState extends State<DataAnalysisScreen>
    with TickerProviderStateMixin {
  final TextEditingController _dataController = TextEditingController();
  String? _analysisResult;
  bool _loading = false;
  String _selectedAnalysisType = 'شامل';
  String _selectedChartType = 'bar';

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<String> _analysisTypes = [
    'شامل',
    'إحصائي',
    'اتجاهات',
    'مقارنة',
    'تنبؤات',
  ];

  final List<Map<String, dynamic>> _chartTypes = [
    {'id': 'bar', 'name': 'أعمدة', 'icon': Icons.bar_chart},
    {'id': 'line', 'name': 'خطي', 'icon': Icons.show_chart},
    {'id': 'pie', 'name': 'دائري', 'icon': Icons.pie_chart},
    {'id': 'scatter', 'name': 'نقطي', 'icon': Icons.scatter_plot},
  ];

  final List<Map<String, dynamic>> _quickExamples = [
    {
      'icon': Icons.trending_up,
      'title': 'بيانات المبيعات الشهرية',
      'data': '''يناير: 15000
فبراير: 18000
مارس: 22000
أبريل: 19000
مايو: 25000
يونيو: 28000''',
      'color': Colors.green,
    },
    {
      'icon': Icons.people,
      'title': 'إحصائيات المستخدمين',
      'data': '''المستخدمون الجدد: 1250
المستخدمون النشطون: 3400
معدل الاحتفاظ: 78%
متوسط الجلسة: 12 دقيقة''',
      'color': Colors.blue,
    },
    {
      'icon': Icons.school,
      'title': 'درجات الطلاب',
      'data': '''الرياضيات: 85, 92, 78, 95, 88
العلوم: 90, 87, 93, 82, 96
اللغة العربية: 88, 91, 85, 89, 94
الإنجليزية: 82, 88, 90, 86, 92''',
      'color': Colors.orange,
    },
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _dataController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _analyzeData() async {
    if (_dataController.text.trim().isEmpty) {
      _showErrorMessage('يرجى إدخال البيانات المراد تحليلها');
      return;
    }

    setState(() {
      _loading = true;
      _analysisResult = null;
    });

    try {
      final result = await EnhancedAIService.analyzeData(
        data: _dataController.text,
        analysisType: _selectedAnalysisType.toLowerCase(),
        chartType: _selectedChartType,
      );

      setState(() {
        _analysisResult = result;
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _loading = false;
      });
      _showErrorMessage('حدث خطأ أثناء التحليل: ${e.toString()}');
    }
  }

  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.error,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.darkPurple.withValues(alpha: 0.9),
              AppColors.background,
              AppColors.primaryPurple.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                _buildHeader(),
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildDataInputSection(),
                        const SizedBox(height: 24),
                        _buildAnalysisTypeSelection(),
                        const SizedBox(height: 24),
                        _buildChartTypeSelection(),
                        const SizedBox(height: 24),
                        _buildQuickExamples(),
                        const SizedBox(height: 32),
                        _buildAnalyzeButton(),
                        if (_analysisResult != null) ...[
                          const SizedBox(height: 24),
                          _buildResult(),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.electricBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => Navigator.pop(context),
                borderRadius: BorderRadius.circular(12),
                child: const Padding(
                  padding: EdgeInsets.all(12),
                  child: Icon(Icons.arrow_back, color: Colors.white, size: 20),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.analytics_outlined,
                      color: AppColors.electricBlue,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'تحليل البيانات الذكي',
                      style: AppTextStyles.heading2.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Text(
                  'اكتشف الأنماط والرؤى في بياناتك',
                  style: AppTextStyles.body.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDataInputSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.darkGrey.withValues(alpha: 0.8),
            AppColors.darkGrey.withValues(alpha: 0.6),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.electricBlue.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.electricBlue.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.electricBlue.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.data_array,
                  color: AppColors.electricBlue,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'البيانات المراد تحليلها',
                style: AppTextStyles.heading3.copyWith(color: Colors.white),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _dataController,
            maxLines: 8,
            style: AppTextStyles.body.copyWith(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'أدخل البيانات هنا (أرقام، نصوص، جداول...)...',
              hintStyle: AppTextStyles.body.copyWith(
                color: AppColors.textSecondary,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: AppColors.electricBlue.withValues(alpha: 0.3),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: AppColors.electricBlue.withValues(alpha: 0.3),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.electricBlue, width: 2),
              ),
              filled: true,
              fillColor: AppColors.darkGrey.withValues(alpha: 0.3),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAnalysisTypeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع التحليل',
          style: AppTextStyles.heading3.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 50,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _analysisTypes.length,
            itemBuilder: (context, index) {
              final type = _analysisTypes[index];
              final isSelected = type == _selectedAnalysisType;

              return Container(
                margin: const EdgeInsets.only(right: 12),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        _selectedAnalysisType = type;
                      });
                    },
                    borderRadius: BorderRadius.circular(25),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        gradient: isSelected
                            ? LinearGradient(
                                colors: [
                                  AppColors.primaryPurple,
                                  AppColors.electricBlue,
                                ],
                              )
                            : LinearGradient(
                                colors: [
                                  AppColors.darkGrey.withValues(alpha: 0.5),
                                  AppColors.darkGrey.withValues(alpha: 0.3),
                                ],
                              ),
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                          color: isSelected
                              ? AppColors.electricBlue
                              : AppColors.electricBlue.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        type,
                        style: AppTextStyles.body.copyWith(
                          color: Colors.white,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildChartTypeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع الرسم البياني',
          style: AppTextStyles.heading3.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 16),
        Row(
          children: _chartTypes.map((chart) {
            final isSelected = chart['id'] == _selectedChartType;
            return Expanded(
              child: Container(
                margin: const EdgeInsets.only(right: 8),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        _selectedChartType = chart['id'];
                      });
                    },
                    borderRadius: BorderRadius.circular(12),
                    child: Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        gradient: isSelected
                            ? LinearGradient(
                                colors: [
                                  AppColors.primaryPurple.withValues(alpha: 0.3),
                                  AppColors.electricBlue.withValues(alpha: 0.3),
                                ],
                              )
                            : LinearGradient(
                                colors: [
                                  AppColors.darkGrey.withValues(alpha: 0.5),
                                  AppColors.darkGrey.withValues(alpha: 0.3),
                                ],
                              ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: isSelected
                              ? AppColors.electricBlue
                              : AppColors.electricBlue.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Column(
                        children: [
                          Icon(
                            chart['icon'],
                            color: isSelected ? AppColors.electricBlue : Colors.white,
                            size: 24,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            chart['name'],
                            style: AppTextStyles.caption.copyWith(
                              color: Colors.white,
                              fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildQuickExamples() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أمثلة سريعة',
          style: AppTextStyles.heading3.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 16),
        ...List.generate(_quickExamples.length, (index) {
          final example = _quickExamples[index];
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  _dataController.text = example['data'];
                },
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.darkGrey.withValues(alpha: 0.6),
                        AppColors.darkGrey.withValues(alpha: 0.4),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: (example['color'] as Color).withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: (example['color'] as Color).withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          example['icon'],
                          color: example['color'],
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          example['title'],
                          style: AppTextStyles.body.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildAnalyzeButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primaryPurple, AppColors.electricBlue],
        ),
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryPurple.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _loading ? null : _analyzeData,
          borderRadius: BorderRadius.circular(28),
          child: Center(
            child: _loading
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.analytics, color: Colors.white, size: 24),
                      const SizedBox(width: 8),
                      Text(
                        'تحليل البيانات',
                        style: AppTextStyles.button.copyWith(
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildResult() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.darkGrey.withValues(alpha: 0.8),
            AppColors.darkGrey.withValues(alpha: 0.6),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.glowPink.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.glowPink.withValues(alpha: 0.2),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.glowPink.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.insights,
                  color: AppColors.glowPink,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'نتائج التحليل',
                style: AppTextStyles.heading3.copyWith(color: Colors.white),
              ),
              const Spacer(),
              IconButton(
                onPressed: () {
                  Clipboard.setData(ClipboardData(text: _analysisResult!));
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('تم نسخ التحليل'),
                      backgroundColor: AppColors.primaryPurple,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                },
                icon: const Icon(Icons.copy, color: Colors.white),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.glowPink.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              _analysisResult!,
              style: AppTextStyles.body.copyWith(
                color: Colors.white,
                height: 1.6,
              ),
            ),
          ),
        ],
      ),
    );
  }
}