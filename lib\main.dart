import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// import 'package:flutter_local_notifications/flutter_local_notifications.dart';

// Screens
import 'screens/onboarding_screen.dart';
import 'screens/auth_screen.dart';

import 'screens/chat_screen.dart';
import 'screens/side_menu.dart';
import 'screens/create_image_screen.dart';
import 'screens/summarize_text_screen.dart';
import 'screens/analyze_data_screen.dart';
import 'screens/plan_screen.dart';
import 'screens/write_assist_screen.dart';
import 'screens/browse_screen.dart';
import 'screens/enhanced_home_screen.dart';
import 'screens/enhanced_chat_screen.dart';
import 'screens/enhanced_settings_screen.dart';
import 'screens/unified_api_management_screen.dart';
import 'screens/advanced_api_management_screen.dart';
import 'screens/data_analysis_screen.dart';
import 'screens/plan_creation_screen.dart';
import 'screens/system_status_screen.dart';
import 'screens/image_analysis_screen.dart';
import 'screens/smart_translation_screen.dart';
import 'screens/analytics_screen.dart';
import 'screens/voice_chat_screen.dart';
import 'screens/subscription_screen.dart';
import 'screens/modern_home_screen.dart';
import 'screens/cloud_management_screen.dart';
import 'screens/collaboration_screen.dart';
import 'screens/ai_analytics_screen.dart';
import 'screens/platform_management_screen.dart';
import 'screens/performance_monitor_screen.dart';
import 'screens/testing_documentation_screen.dart';
import 'screens/smart_chat_screen.dart';
import 'screens/text_summarization_screen.dart';
import 'screens/writing_assistance_screen.dart';
import 'screens/smart_translation_screen.dart';
import 'screens/smart_browsing_screen.dart';

// Core Services
import 'core/services/enhanced_ai_service.dart';
import 'core/services/notification_service.dart';
import 'core/notifications/advanced_notification_service.dart';
import 'core/auth/auth_service.dart';
import 'core/analytics/usage_analytics.dart';
import 'core/offline/offline_service.dart';
import 'core/security/security_service.dart';
import 'core/performance/performance_service.dart';
import 'core/services/voice_service.dart';
import 'core/payment/payment_service.dart';
import 'core/cloud/cloud_storage_service.dart';
import 'core/cloud/cloud_database_service.dart';
import 'core/cloud/push_notification_service.dart';
import 'core/collaboration/user_management_service.dart';
import 'core/collaboration/workspace_service.dart';
import 'core/collaboration/team_chat_service.dart';
import 'core/analytics/ai_analytics_engine.dart';
import 'core/analytics/dashboard_service.dart';
import 'core/platform/platform_support_service.dart';
import 'core/platform/companion_app_service.dart';
import 'core/performance/performance_optimizer.dart';
import 'core/performance/memory_manager.dart';
import 'core/testing/test_suite_manager.dart';
import 'core/documentation/documentation_generator.dart';
import 'tools/real_ai_tools_hub.dart';
import 'core/services/storage_service.dart';
import 'core/services/api_provider_service.dart';
import 'core/services/feature_api_service.dart';

import 'utils/app_colors.dart';

// Global notification instance (معطل مؤقتاً)
// final FlutterLocalNotificationsPlugin flutterLocalNotificationsPlugin =
//     FlutterLocalNotificationsPlugin();

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة جميع الخدمات
  await _initializeServices();

  runApp(const ProviderScope(child: MyApp()));
}

/// تهيئة جميع خدمات التطبيق
Future<void> _initializeServices() async {
  try {
    // تهيئة التخزين المحلي
    await StorageService.initialize();

    // تهيئة الإشعارات
    await NotificationService.initialize();
    await AdvancedNotificationService.initialize();

    // تهيئة خدمة المصادقة
    await AuthService.initialize();

    // تهيئة خدمة التحليلات
    await UsageAnalytics.initialize();

    // تهيئة خدمة العمل بدون إنترنت
    await OfflineService.initialize();

    // تهيئة خدمة الأمان
    await SecurityService.initialize();

    // تهيئة خدمة الأداء
    await PerformanceService.initialize();

    // تهيئة خدمة الصوت
    await VoiceService.initialize();

    // تهيئة خدمة الدفع
    await PaymentService.initialize();

    // تهيئة الخدمات السحابية
    await CloudStorageService.initialize();
    await CloudDatabaseService.initialize();
    await PushNotificationService.initialize();

    // تهيئة خدمات التعاون
    await UserManagementService.initialize();
    await WorkspaceService.initialize();
    await TeamChatService.initialize();

    // تهيئة خدمات التحليلات
    await AIAnalyticsEngine.initialize();
    await DashboardService.initialize();

    // تهيئة خدمات المنصات
    await PlatformSupportService.initialize();
    await CompanionAppService.initialize();

    // تهيئة خدمات الأداء
    await PerformanceOptimizer.initialize();
    await MemoryManager.initialize();

    // تهيئة خدمات الاختبار والتوثيق
    await TestSuiteManager.initialize();
    await DocumentationGenerator.initialize();

    // تهيئة خدمة إدارة API
    await ApiProviderService.initialize();

    // تهيئة خدمة APIs الميزات
    await FeatureApiService.initialize();

    // تهيئة خدمة الذكاء الاصطناعي
    await EnhancedAIService.initialize();

    // تهيئة نظام الأدوات الحقيقية
    await RealAIToolsHub.initialize();

    // تنظيف قاعدة البيانات
    await StorageService.clearExpiredCache();

    debugPrint('✅ تم تهيئة جميع الخدمات بنجاح');
  } catch (e) {
    debugPrint('❌ خطأ في تهيئة الخدمات: $e');
  }
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'DeepSeek AI',
      theme: ThemeData(
        // fontFamily: 'Urbanist',
        scaffoldBackgroundColor: AppColors.background,
        colorScheme: ColorScheme(
          brightness: Brightness.dark,
          primary: AppColors.primaryPurple,
          onPrimary: AppColors.white,
          secondary: AppColors.lightPurple,
          onSecondary: AppColors.white,
          error: AppColors.error,
          onError: AppColors.white,
          surface: AppColors.darkGrey,
          onSurface: AppColors.white,
        ),
        useMaterial3: true,
        appBarTheme: const AppBarTheme(
          backgroundColor: AppColors.darkPurple,
          elevation: 0,
          iconTheme: IconThemeData(color: AppColors.white),
          titleTextStyle: TextStyle(
            // fontFamily: 'Urbanist',
            fontWeight: FontWeight.bold,
            fontSize: 20,
            color: AppColors.white,
          ),
        ),
        elevatedButtonTheme: ElevatedButtonThemeData(
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryPurple,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24),
            ),
            textStyle: const TextStyle(
              // fontFamily: 'Urbanist',
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 32),
            elevation: 4,
            shadowColor: AppColors.primaryPurple,
          ),
        ),
        outlinedButtonTheme: OutlinedButtonThemeData(
          style: OutlinedButton.styleFrom(
            side: const BorderSide(color: AppColors.primaryPurple),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(24),
            ),
            textStyle: const TextStyle(
              // fontFamily: 'Urbanist',
              fontWeight: FontWeight.w600,
              fontSize: 16,
            ),
            padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 32),
          ),
        ),
        inputDecorationTheme: InputDecorationTheme(
          filled: true,
          fillColor: AppColors.darkGrey,
          border: OutlineInputBorder(
            borderRadius: BorderRadius.circular(16),
            borderSide: BorderSide.none,
          ),
          hintStyle: const TextStyle(
            color: Colors.white54,
            // fontFamily: 'Urbanist',
          ),
          labelStyle: const TextStyle(
            color: AppColors.lightPurple,
            // fontFamily: 'Urbanist',
          ),
        ),
        textTheme: const TextTheme(
          displayLarge: TextStyle(
            // fontFamily: 'Urbanist',
            fontWeight: FontWeight.bold,
            fontSize: 32,
            color: AppColors.white,
          ),
          displayMedium: TextStyle(
            // fontFamily: 'Urbanist',
            fontWeight: FontWeight.bold,
            fontSize: 28,
            color: AppColors.white,
          ),
          displaySmall: TextStyle(
            // fontFamily: 'Urbanist',
            fontWeight: FontWeight.w600,
            fontSize: 22,
            color: AppColors.white,
          ),
          titleLarge: TextStyle(
            // fontFamily: 'Urbanist',
            fontWeight: FontWeight.w600,
            fontSize: 20,
            color: AppColors.white,
          ),
          bodyLarge: TextStyle(
            // fontFamily: 'Urbanist',
            fontWeight: FontWeight.normal,
            fontSize: 16,
            color: AppColors.white,
          ),
          bodyMedium: TextStyle(
            // fontFamily: 'Urbanist',
            fontWeight: FontWeight.normal,
            fontSize: 14,
            color: Colors.white70,
          ),
          labelLarge: TextStyle(
            // fontFamily: 'Urbanist',
            fontWeight: FontWeight.w600,
            fontSize: 16,
            color: AppColors.white,
          ),
        ),
      ),
      initialRoute: '/',
      routes: {
        '/': (context) => const OnboardingScreen(),
        '/auth': (context) => const AuthScreen(),
        '/home': (context) => const EnhancedHomeScreen(),
        '/chat': (context) => const ChatScreen(),
        '/enhanced_chat': (context) => const EnhancedChatScreen(),
        '/enhanced_settings': (context) => const EnhancedSettingsScreen(),
        '/side_menu': (context) => const SideMenu(),
        '/create_image': (context) => const CreateImageScreen(),
        '/summarize_text': (context) => const SummarizeTextScreen(),
        '/analyze_data': (context) => const AnalyzeDataScreen(),
        '/plan': (context) => const PlanScreen(),
        '/write_assist': (context) => const WriteAssistScreen(),
        '/browse': (context) => const BrowseScreen(),
        '/unified_api_management':
            (context) => const UnifiedApiManagementScreen(),
        '/advanced_api_management':
            (context) => const AdvancedApiManagementScreen(),
        '/data_analysis': (context) => const DataAnalysisScreen(),
        '/plan_creation': (context) => const PlanCreationScreen(),
        '/system_status': (context) => const SystemStatusScreen(),
        '/image_analysis': (context) => const ImageAnalysisScreen(),
        '/analytics': (context) => const AnalyticsScreen(),
        '/voice_chat': (context) => const VoiceChatScreen(),
        '/subscription': (context) => const SubscriptionScreen(),
        '/modern_home': (context) => const ModernHomeScreen(),
        '/cloud_management': (context) => const CloudManagementScreen(),
        '/collaboration': (context) => const CollaborationScreen(),
        '/ai_analytics': (context) => const AIAnalyticsScreen(),
        '/platform_management': (context) => const PlatformManagementScreen(),
        '/performance_monitor': (context) => const PerformanceMonitorScreen(),
        '/testing_documentation': (context) => const TestingDocumentationScreen(),
        '/smart_chat': (context) => const SmartChatScreen(),
        '/text_summarization': (context) => const TextSummarizationScreen(),
        '/writing_assistance': (context) => const WritingAssistanceScreen(),
        '/smart_translation': (context) => const SmartTranslationScreen(),
        '/smart_browsing': (context) => const SmartBrowsingScreen(),
      },
    );
  }
}
