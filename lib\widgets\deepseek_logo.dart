import 'package:flutter/material.dart';

class DeepSeekLogo extends StatefulWidget {
  final double size;
  const DeepSeekLogo({Key? key, this.size = 120}) : super(key: key);

  @override
  State<DeepSeekLogo> createState() => _DeepSeekLogoState();
}

class _DeepSeekLogoState extends State<DeepSeekLogo>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnim;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 2),
    )..repeat(reverse: true);
    _scaleAnim = Tween<double>(
      begin: 0.92,
      end: 1.08,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return ScaleTransition(
      scale: _scaleAnim,
      child: CustomPaint(
        size: Size.square(widget.size),
        painter: _DeepSeekLogoPainter(),
      ),
    );
  }
}

class _DeepSeekLogoPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final purple = const Color(0xFF8F5BFF);
    final darkPurple = const Color(0xFF2B204A);
    final paint =
        Paint()
          ..color = purple
          ..style = PaintingStyle.fill;

    // Body (circle with tail cut)
    final bodyRect = Rect.fromCircle(
      center: size.center(Offset.zero),
      radius: size.width * 0.42,
    );
    canvas.drawArc(bodyRect, 0.7, 4.5, true, paint);

    // Tail
    final tailPath = Path();
    tailPath.moveTo(size.width * 0.82, size.height * 0.38);
    tailPath.quadraticBezierTo(
      size.width * 0.97,
      size.height * 0.22,
      size.width * 0.82,
      size.height * 0.18,
    );
    tailPath.quadraticBezierTo(
      size.width * 0.88,
      size.height * 0.32,
      size.width * 0.82,
      size.height * 0.38,
    );
    canvas.drawPath(tailPath, paint);

    // Fin
    final finPath = Path();
    finPath.moveTo(size.width * 0.38, size.height * 0.68);
    finPath.quadraticBezierTo(
      size.width * 0.32,
      size.height * 0.82,
      size.width * 0.48,
      size.height * 0.78,
    );
    finPath.quadraticBezierTo(
      size.width * 0.42,
      size.height * 0.72,
      size.width * 0.38,
      size.height * 0.68,
    );
    canvas.drawPath(finPath, paint);

    // Eye
    final eyePaint = Paint()..color = darkPurple;
    canvas.drawCircle(
      Offset(size.width * 0.62, size.height * 0.48),
      size.width * 0.045,
      eyePaint,
    );
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
