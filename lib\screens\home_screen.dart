import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../utils/app_animations.dart';
import '../widgets/deepseek_logo.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({Key? key}) : super(key: key);

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with TickerProviderStateMixin {
  late AnimationController _backgroundController;
  late AnimationController _logoController;
  late Animation<double> _backgroundAnimation;
  late Animation<double> _logoAnimation;

  final functions = [
    {
      'label': 'محادثة ذكية',
      'route': '/smart_chat',
      'icon': Icons.chat_bubble_outline,
      'colors': [const Color(0xFF9B6EF3), const Color(0xFF7D39EB)],
    },
    {
      'label': 'تلخيص النص',
      'route': '/text_summarization',
      'icon': Icons.summarize_outlined,
      'colors': [const Color(0xFF6B7AED), const Color(0xFF5865F2)],
    },
    {
      'label': 'تحليل البيانات',
      'route': '/data_analysis',
      'icon': Icons.analytics_outlined,
      'colors': [const Color(0xFF58D68D), const Color(0xFF27AE60)],
    },
    {
      'label': 'إنشاء خطة',
      'route': '/plan_creation',
      'icon': Icons.calendar_today_outlined,
      'colors': [const Color(0xFFF39C12), const Color(0xFFE67E22)],
    },
    {
      'label': 'مساعدة الكتابة',
      'route': '/writing_assistance',
      'icon': Icons.edit_outlined,
      'colors': [const Color(0xFF5DADE2), const Color(0xFF3498DB)],
    },
    {
      'label': 'إنشاء صورة',
      'route': '/create_image',
      'icon': Icons.image_outlined,
      'colors': [const Color(0xFFEC7063), const Color(0xFFE74C3C)],
    },
    {
      'label': 'الترجمة الذكية',
      'route': '/smart_translation',
      'icon': Icons.translate_outlined,
      'colors': [const Color(0xFF58D68D), const Color(0xFF27AE60)],
    },
    {
      'label': 'التصفح الذكي',
      'route': '/smart_browsing',
      'icon': Icons.web_outlined,
      'colors': [const Color(0xFFAB47BC), const Color(0xFF8E24AA)],
    },
    {
      'label': 'تحليل الصور',
      'route': '/vision_analysis',
      'icon': Icons.image_search_outlined,
      'colors': [const Color(0xFFFF6B6B), const Color(0xFFEE5A24)],
    },
    {
      'label': 'الدردشة الصوتية',
      'route': '/voice_chat',
      'icon': Icons.mic_outlined,
      'colors': [const Color(0xFF4ECDC4), const Color(0xFF44A08D)],
    },
    {
      'label': 'توليد الكود',
      'route': '/code_generation',
      'icon': Icons.code_outlined,
      'colors': [const Color(0xFF667EEA), const Color(0xFF764BA2)],
    },
  ];

  @override
  void initState() {
    super.initState();
    _backgroundController = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat();

    _logoController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _backgroundAnimation = Tween<double>(
      begin: 0,
      end: 2 * math.pi,
    ).animate(_backgroundController);

    _logoAnimation = Tween<double>(begin: 0.95, end: 1.05).animate(
      CurvedAnimation(parent: _logoController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _backgroundController.dispose();
    _logoController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Stack(
        children: [
          // خلفية متحركة
          AnimatedBuilder(
            animation: _backgroundAnimation,
            builder: (context, child) {
              return CustomPaint(
                painter: BackgroundPainter(_backgroundAnimation.value),
                size: Size.infinite,
              );
            },
          ),
          SafeArea(
            child: Column(
              children: [
                // شريط علوي مخصص
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                  child: Row(
                    children: [
                      AnimatedBuilder(
                        animation: _logoAnimation,
                        builder: (context, child) {
                          return Transform.scale(
                            scale: _logoAnimation.value,
                            child: Container(
                              padding: const EdgeInsets.all(8),
                              decoration: BoxDecoration(
                                shape: BoxShape.circle,
                                gradient: const LinearGradient(
                                  colors: [
                                    AppColors.primaryPurple,
                                    AppColors.lightPurple,
                                  ],
                                ),
                                boxShadow: [
                                  BoxShadow(
                                    color: AppColors.primaryPurple.withOpacity(
                                      0.5,
                                    ),
                                    blurRadius: 20,
                                    spreadRadius: 2,
                                  ),
                                ],
                              ),
                              child: const DeepSeekLogo(size: 36),
                            ),
                          );
                        },
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            AppAnimations.fadeIn(
                              child: Text(
                                'DeepSeek AI',
                                style: AppTextStyles.heading2.copyWith(
                                  fontWeight: FontWeight.bold,
                                  letterSpacing: 1.2,
                                ),
                              ),
                            ),
                            AppAnimations.fadeIn(
                              delay: 0.2,
                              child: Text(
                                'مساعدك الذكي',
                                style: AppTextStyles.caption.copyWith(
                                  color: AppColors.lightPurple,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      Row(
                        children: [
                          AnimatedButton(
                            onTap: () {
                              Navigator.pushNamed(context, '/system_status');
                            },
                            gradient: const LinearGradient(
                              colors: [AppColors.accentGreen, Colors.green],
                            ),
                            borderRadius: BorderRadius.circular(12),
                            padding: const EdgeInsets.all(12),
                            child: const Icon(
                              Icons.health_and_safety,
                              color: Colors.white,
                            ),
                          ),
                          const SizedBox(width: 8),
                          AnimatedButton(
                            onTap: () {
                              Navigator.pushNamed(
                                context,
                                '/advanced_api_management',
                              );
                            },
                            gradient: const LinearGradient(
                              colors: [
                                AppColors.primaryPurple,
                                AppColors.lightPurple,
                              ],
                            ),
                            borderRadius: BorderRadius.circular(12),
                            padding: const EdgeInsets.all(12),
                            child: const Icon(Icons.api, color: Colors.white),
                          ),
                          const SizedBox(width: 8),
                          AnimatedButton(
                            onTap: () {},
                            gradient: const LinearGradient(
                              colors: [AppColors.darkGrey, AppColors.midGrey],
                            ),
                            borderRadius: BorderRadius.circular(12),
                            padding: const EdgeInsets.all(12),
                            child: const Icon(Icons.menu, color: Colors.white),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),

                // المحتوى الرئيسي
                Expanded(
                  child: Column(
                    children: [
                      // رسالة الترحيب
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 24.0,
                          vertical: 8,
                        ),
                        child: AppAnimations.fadeIn(
                          child: Container(
                            padding: const EdgeInsets.all(20),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  AppColors.darkPurple.withOpacity(0.3),
                                  AppColors.primaryPurple.withOpacity(0.1),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(20),
                              border: Border.all(
                                color: AppColors.primaryPurple.withOpacity(0.3),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              children: [
                                Icon(
                                  Icons.auto_awesome,
                                  color: AppColors.lightPurple,
                                  size: 32,
                                ),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Text(
                                    "مرحباً! كيف يمكنني مساعدتك اليوم؟",
                                    style: AppTextStyles.heading2.copyWith(
                                      fontSize: 20,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      // شبكة الوظائف
                      Expanded(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(horizontal: 12.0),
                          child: GridView.builder(
                            itemCount: functions.length,
                            gridDelegate:
                                const SliverGridDelegateWithFixedCrossAxisCount(
                                  crossAxisCount: 2,
                                  mainAxisSpacing: 12,
                                  crossAxisSpacing: 12,
                                  childAspectRatio: 1.1,
                                ),
                            itemBuilder: (context, index) {
                              final function = functions[index];
                              final isEnabled = function['route'] != null;
                              return AnimatedListItem(
                                index: index,
                                child: Opacity(
                                  opacity: isEnabled ? 1.0 : 0.55,
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(24),
                                    onTap:
                                        isEnabled
                                            ? () async {
                                              try {
                                                await Navigator.pushNamed(
                                                  context,
                                                  function['route'] as String,
                                                );
                                              } catch (e) {
                                                showDialog(
                                                  context: context,
                                                  builder:
                                                      (ctx) => AlertDialog(
                                                        title: const Text(
                                                          'خطأ في التنقل',
                                                        ),
                                                        content: Text(
                                                          'تعذر فتح الصفحة المطلوبة.\n${e.toString()}',
                                                        ),
                                                        actions: [
                                                          TextButton(
                                                            onPressed:
                                                                () =>
                                                                    Navigator.of(
                                                                      ctx,
                                                                    ).pop(),
                                                            child: const Text(
                                                              'حسناً',
                                                            ),
                                                          ),
                                                        ],
                                                      ),
                                                );
                                              }
                                            }
                                            : () {
                                              ScaffoldMessenger.of(
                                                context,
                                              ).showSnackBar(
                                                const SnackBar(
                                                  content: Text(
                                                    'هذه الميزة ستتوفر قريبًا',
                                                  ),
                                                  duration: Duration(
                                                    seconds: 2,
                                                  ),
                                                ),
                                              );
                                            },
                                    child: GlassContainer(
                                      blur: 18,
                                      opacity: 0.18,
                                      borderRadius: BorderRadius.circular(24),
                                      gradient: LinearGradient(
                                        colors: [
                                          (function['colors'] as List<Color>)[0]
                                              .withOpacity(0.25),
                                          (function['colors'] as List<Color>)[1]
                                              .withOpacity(0.18),
                                        ],
                                        begin: Alignment.topLeft,
                                        end: Alignment.bottomRight,
                                      ),
                                      border: Border.all(
                                        color: (function['colors']
                                                as List<Color>)[0]
                                            .withOpacity(0.25),
                                        width: 1.2,
                                      ),
                                      boxShadow: [
                                        BoxShadow(
                                          color: (function['colors']
                                                  as List<Color>)[0]
                                              .withOpacity(0.13),
                                          blurRadius: 18,
                                          offset: const Offset(0, 6),
                                        ),
                                      ],
                                      padding: const EdgeInsets.all(0),
                                      child: Column(
                                        mainAxisAlignment:
                                            MainAxisAlignment.center,
                                        children: [
                                          Icon(
                                            function['icon'] as IconData,
                                            size: 40,
                                            color: Colors.white,
                                          ),
                                          const SizedBox(height: 12),
                                          Padding(
                                            padding: const EdgeInsets.symmetric(
                                              horizontal: 8.0,
                                            ),
                                            child: Text(
                                              function['label'] as String,
                                              style: AppTextStyles.heading2
                                                  .copyWith(
                                                    color: Colors.white,
                                                    fontSize: 14,
                                                  ),
                                              textAlign: TextAlign.center,
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis,
                                            ),
                                          ),
                                          if (!isEnabled)
                                            Padding(
                                              padding: const EdgeInsets.only(
                                                top: 4.0,
                                              ),
                                              child: Text(
                                                'قريبًا',
                                                style: AppTextStyles.caption
                                                    .copyWith(
                                                      color: Colors.white70,
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      fontSize: 11,
                                                    ),
                                              ),
                                            ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                              );
                            },
                          ),
                        ),
                      ),
                      const SizedBox(height: 16),
                      // شريط البحث
                      Padding(
                        padding: const EdgeInsets.symmetric(horizontal: 8.0),
                        child: AppAnimations.fadeIn(
                          child: Container(
                            height: 60,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  AppColors.darkGrey,
                                  AppColors.darkGrey.withOpacity(0.8),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(30),
                              border: Border.all(
                                color: AppColors.primaryPurple.withOpacity(0.3),
                                width: 1,
                              ),
                              boxShadow: [
                                BoxShadow(
                                  color: AppColors.primaryPurple.withOpacity(
                                    0.2,
                                  ),
                                  blurRadius: 20,
                                  offset: const Offset(0, 5),
                                ),
                              ],
                            ),
                            child: Row(
                              children: [
                                const SizedBox(width: 8),
                                AnimatedButton(
                                  onTap: () {},
                                  gradient: const LinearGradient(
                                    colors: [
                                      AppColors.primaryPurple,
                                      AppColors.lightPurple,
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(25),
                                  padding: const EdgeInsets.all(12),
                                  child: const Icon(
                                    Icons.add,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                                const Expanded(
                                  child: TextField(
                                    decoration: InputDecoration(
                                      hintText: 'ابحث أو اكتب رسالة...',
                                      border: InputBorder.none,
                                      hintStyle: TextStyle(
                                        color: Colors.white54,
                                      ),
                                      contentPadding: EdgeInsets.symmetric(
                                        horizontal: 16,
                                      ),
                                    ),
                                    style: TextStyle(color: Colors.white),
                                  ),
                                ),
                                AnimatedButton(
                                  onTap: () {},
                                  gradient: LinearGradient(
                                    colors: [
                                      AppColors.darkGrey.withOpacity(0.5),
                                      AppColors.midGrey.withOpacity(0.5),
                                    ],
                                  ),
                                  borderRadius: BorderRadius.circular(25),
                                  padding: const EdgeInsets.all(12),
                                  child: const Icon(
                                    Icons.mic,
                                    color: Colors.white,
                                    size: 20,
                                  ),
                                ),
                                const SizedBox(width: 8),
                              ],
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(height: 8),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// رسام الخلفية المتحركة
class BackgroundPainter extends CustomPainter {
  final double animationValue;

  BackgroundPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..style = PaintingStyle.fill
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 50);

    // دوائر متحركة
    for (int i = 0; i < 3; i++) {
      final offset = Offset(
        size.width * (0.2 + i * 0.3) + math.sin(animationValue + i) * 50,
        size.height * (0.3 + i * 0.2) + math.cos(animationValue + i) * 50,
      );

      paint.shader = RadialGradient(
        colors: [
          AppColors.primaryPurple.withOpacity(0.3),
          AppColors.primaryPurple.withOpacity(0.0),
        ],
      ).createShader(Rect.fromCircle(center: offset, radius: 150));

      canvas.drawCircle(offset, 150, paint);
    }
  }

  @override
  bool shouldRepaint(BackgroundPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}

// GlassContainer widget (if not already imported from app_animations.dart)
// يمكنك حذف هذا التعريف إذا كان موجوداً في app_animations.dart
class GlassContainer extends StatelessWidget {
  final Widget child;
  final double blur;
  final double opacity;
  final BorderRadius borderRadius;
  final Gradient? gradient;
  final BoxBorder? border;
  final List<BoxShadow>? boxShadow;
  final EdgeInsetsGeometry? padding;
  final Color? color;

  const GlassContainer({
    Key? key,
    required this.child,
    this.blur = 18,
    this.opacity = 0.25,
    this.borderRadius = const BorderRadius.all(Radius.circular(24)),
    this.gradient,
    this.border,
    this.boxShadow,
    this.padding,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: borderRadius,
      child: Stack(
        children: [
          BackdropFilter(
            filter: ui.ImageFilter.blur(sigmaX: blur, sigmaY: blur),
            child: Container(),
          ),
          Container(
            decoration: BoxDecoration(
              color: color?.withOpacity(opacity) ?? AppColors.glassBackground,
              gradient: gradient,
              borderRadius: borderRadius,
              border: border,
              boxShadow: boxShadow,
            ),
            padding: padding,
            child: child,
          ),
        ],
      ),
    );
  }
}
