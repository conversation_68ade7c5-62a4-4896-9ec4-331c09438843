import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import '../storage/storage_service.dart';

/// خدمة الأمان المتقدمة
class SecurityService {
  static const String _securitySettingsKey = 'security_settings';
  static const String _encryptionKeyKey = 'encryption_key';
  static const String _sessionTokenKey = 'session_token';
  static const String _securityLogsKey = 'security_logs';
  
  static SecuritySettings _settings = SecuritySettings.defaultSettings();
  static String? _encryptionKey;
  static String? _currentSessionToken;
  static List<SecurityLog> _securityLogs = [];
  static bool _isInitialized = false;

  /// تهيئة خدمة الأمان
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadSettings();
      await _loadOrGenerateEncryptionKey();
      await _loadSecurityLogs();
      await _generateSessionToken();

      _isInitialized = true;
      await _logSecurityEvent(SecurityEventType.systemStart, 'تم تهيئة نظام الأمان');
      debugPrint('🔒 تم تهيئة خدمة الأمان المتقدمة');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الأمان: $e');
    }
  }

  /// تشفير البيانات الحساسة
  static String encryptSensitiveData(String data) {
    if (_encryptionKey == null) {
      throw SecurityException('مفتاح التشفير غير متاح');
    }

    try {
      // تشفير بسيط باستخدام XOR (في التطبيق الحقيقي يجب استخدام AES)
      final key = _encryptionKey!;
      final encrypted = _xorEncrypt(data, key);
      final base64Encrypted = base64Encode(utf8.encode(encrypted));
      
      _logSecurityEvent(SecurityEventType.dataEncryption, 'تم تشفير البيانات');
      return base64Encrypted;
    } catch (e) {
      _logSecurityEvent(SecurityEventType.encryptionError, 'خطأ في التشفير: $e');
      throw SecurityException('فشل في تشفير البيانات: $e');
    }
  }

  /// فك تشفير البيانات
  static String decryptSensitiveData(String encryptedData) {
    if (_encryptionKey == null) {
      throw SecurityException('مفتاح التشفير غير متاح');
    }

    try {
      final decodedData = utf8.decode(base64Decode(encryptedData));
      final key = _encryptionKey!;
      final decrypted = _xorDecrypt(decodedData, key);
      
      _logSecurityEvent(SecurityEventType.dataDecryption, 'تم فك تشفير البيانات');
      return decrypted;
    } catch (e) {
      _logSecurityEvent(SecurityEventType.decryptionError, 'خطأ في فك التشفير: $e');
      throw SecurityException('فشل في فك تشفير البيانات: $e');
    }
  }

  /// تشفير XOR بسيط
  static String _xorEncrypt(String data, String key) {
    final result = StringBuffer();
    for (int i = 0; i < data.length; i++) {
      final dataChar = data.codeUnitAt(i);
      final keyChar = key.codeUnitAt(i % key.length);
      result.writeCharCode(dataChar ^ keyChar);
    }
    return result.toString();
  }

  /// فك تشفير XOR
  static String _xorDecrypt(String encryptedData, String key) {
    return _xorEncrypt(encryptedData, key); // XOR هو عملية عكسية
  }

  /// توليد hash آمن للكلمات السرية
  static String hashPassword(String password, String salt) {
    final bytes = utf8.encode(password + salt);
    final digest = sha256.convert(bytes);
    return digest.toString();
  }

  /// توليد salt عشوائي
  static String generateSalt() {
    final random = Random.secure();
    final saltBytes = List<int>.generate(32, (i) => random.nextInt(256));
    return base64Encode(saltBytes);
  }

  /// التحقق من قوة كلمة المرور
  static PasswordStrength checkPasswordStrength(String password) {
    int score = 0;
    List<String> issues = [];

    // طول كلمة المرور
    if (password.length >= 8) {
      score += 1;
    } else {
      issues.add('يجب أن تكون 8 أحرف على الأقل');
    }

    if (password.length >= 12) {
      score += 1;
    }

    // أحرف كبيرة
    if (password.contains(RegExp(r'[A-Z]'))) {
      score += 1;
    } else {
      issues.add('يجب أن تحتوي على أحرف كبيرة');
    }

    // أحرف صغيرة
    if (password.contains(RegExp(r'[a-z]'))) {
      score += 1;
    } else {
      issues.add('يجب أن تحتوي على أحرف صغيرة');
    }

    // أرقام
    if (password.contains(RegExp(r'[0-9]'))) {
      score += 1;
    } else {
      issues.add('يجب أن تحتوي على أرقام');
    }

    // رموز خاصة
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      score += 1;
    } else {
      issues.add('يجب أن تحتوي على رموز خاصة');
    }

    // تحديد القوة
    PasswordStrengthLevel level;
    if (score <= 2) {
      level = PasswordStrengthLevel.weak;
    } else if (score <= 4) {
      level = PasswordStrengthLevel.medium;
    } else {
      level = PasswordStrengthLevel.strong;
    }

    return PasswordStrength(
      level: level,
      score: score,
      maxScore: 6,
      issues: issues,
    );
  }

  /// التحقق من صحة مفتاح API
  static bool validateApiKey(String apiKey) {
    if (apiKey.isEmpty) return false;
    
    // فحص أساسي لطول المفتاح
    if (apiKey.length < 20) {
      _logSecurityEvent(SecurityEventType.invalidApiKey, 'مفتاح API قصير جداً');
      return false;
    }

    // فحص تنسيق المفتاح
    if (!RegExp(r'^[a-zA-Z0-9\-_]+$').hasMatch(apiKey)) {
      _logSecurityEvent(SecurityEventType.invalidApiKey, 'تنسيق مفتاح API غير صحيح');
      return false;
    }

    _logSecurityEvent(SecurityEventType.validApiKey, 'تم التحقق من صحة مفتاح API');
    return true;
  }

  /// فحص البيانات المدخلة للحماية من الحقن
  static String sanitizeInput(String input) {
    // إزالة الرموز الخطيرة
    String sanitized = input
        .replaceAll(RegExp(r'<script[^>]*>.*?</script>', caseSensitive: false), '')
        .replaceAll(RegExp(r'<[^>]*>'), '') // إزالة HTML tags
        .replaceAll(RegExp(r'[\'";]'), '') // إزالة رموز SQL injection
        .trim();

    if (sanitized != input) {
      _logSecurityEvent(SecurityEventType.inputSanitized, 'تم تنظيف المدخلات');
    }

    return sanitized;
  }

  /// توليد رمز جلسة آمن
  static Future<void> _generateSessionToken() async {
    final random = Random.secure();
    final tokenBytes = List<int>.generate(32, (i) => random.nextInt(256));
    _currentSessionToken = base64Encode(tokenBytes);
    
    await StorageService.saveData(_sessionTokenKey, _currentSessionToken);
    _logSecurityEvent(SecurityEventType.sessionCreated, 'تم إنشاء رمز جلسة جديد');
  }

  /// التحقق من صحة رمز الجلسة
  static bool validateSessionToken(String token) {
    if (_currentSessionToken == null) return false;
    
    final isValid = _currentSessionToken == token;
    if (!isValid) {
      _logSecurityEvent(SecurityEventType.invalidSession, 'رمز جلسة غير صحيح');
    }
    
    return isValid;
  }

  /// تسجيل حدث أمني
  static Future<void> _logSecurityEvent(SecurityEventType type, String description) async {
    final log = SecurityLog(
      type: type,
      description: description,
      timestamp: DateTime.now(),
      severity: _getEventSeverity(type),
    );

    _securityLogs.add(log);
    
    // الاحتفاظ بآخر 1000 حدث فقط
    if (_securityLogs.length > 1000) {
      _securityLogs.removeRange(0, _securityLogs.length - 1000);
    }

    await _saveSecurityLogs();
    
    // تنبيه للأحداث الحرجة
    if (log.severity == SecuritySeverity.critical) {
      debugPrint('🚨 حدث أمني حرج: $description');
    }
  }

  /// تحديد خطورة الحدث
  static SecuritySeverity _getEventSeverity(SecurityEventType type) {
    switch (type) {
      case SecurityEventType.systemStart:
      case SecurityEventType.dataEncryption:
      case SecurityEventType.dataDecryption:
      case SecurityEventType.validApiKey:
        return SecuritySeverity.info;
      
      case SecurityEventType.inputSanitized:
      case SecurityEventType.sessionCreated:
        return SecuritySeverity.warning;
      
      case SecurityEventType.invalidApiKey:
      case SecurityEventType.invalidSession:
      case SecurityEventType.encryptionError:
      case SecurityEventType.decryptionError:
        return SecuritySeverity.critical;
    }
  }

  /// تحميل أو توليد مفتاح التشفير
  static Future<void> _loadOrGenerateEncryptionKey() async {
    try {
      _encryptionKey = await StorageService.getData(_encryptionKeyKey);
      
      if (_encryptionKey == null) {
        // توليد مفتاح جديد
        final random = Random.secure();
        final keyBytes = List<int>.generate(32, (i) => random.nextInt(256));
        _encryptionKey = base64Encode(keyBytes);
        
        await StorageService.saveData(_encryptionKeyKey, _encryptionKey);
        debugPrint('🔑 تم توليد مفتاح تشفير جديد');
      } else {
        debugPrint('🔑 تم تحميل مفتاح التشفير الموجود');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل/توليد مفتاح التشفير: $e');
    }
  }

  /// حفظ الإعدادات
  static Future<void> _saveSettings() async {
    await StorageService.saveData(_securitySettingsKey, _settings.toMap());
  }

  /// تحميل الإعدادات
  static Future<void> _loadSettings() async {
    try {
      final data = await StorageService.getData(_securitySettingsKey);
      if (data != null) {
        _settings = SecuritySettings.fromMap(data);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إعدادات الأمان: $e');
    }
  }

  /// حفظ سجلات الأمان
  static Future<void> _saveSecurityLogs() async {
    final logsData = _securityLogs.map((log) => log.toMap()).toList();
    await StorageService.saveData(_securityLogsKey, logsData);
  }

  /// تحميل سجلات الأمان
  static Future<void> _loadSecurityLogs() async {
    try {
      final data = await StorageService.getData(_securityLogsKey);
      if (data != null && data is List) {
        _securityLogs = data.map((logData) => SecurityLog.fromMap(logData)).toList();
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل سجلات الأمان: $e');
    }
  }

  /// تحديث إعدادات الأمان
  static Future<void> updateSettings(SecuritySettings newSettings) async {
    _settings = newSettings;
    await _saveSettings();
    await _logSecurityEvent(SecurityEventType.systemStart, 'تم تحديث إعدادات الأمان');
  }

  /// الحصول على الإعدادات
  static SecuritySettings get settings => _settings;

  /// الحصول على سجلات الأمان
  static List<SecurityLog> get securityLogs => List.unmodifiable(_securityLogs);

  /// مسح سجلات الأمان
  static Future<void> clearSecurityLogs() async {
    _securityLogs.clear();
    await _saveSecurityLogs();
    await _logSecurityEvent(SecurityEventType.systemStart, 'تم مسح سجلات الأمان');
  }

  /// الحصول على رمز الجلسة الحالي
  static String? get currentSessionToken => _currentSessionToken;
}

/// إعدادات الأمان
class SecuritySettings {
  final bool encryptSensitiveData;
  final bool logSecurityEvents;
  final bool requireStrongPasswords;
  final bool validateApiKeys;
  final bool sanitizeInputs;

  SecuritySettings({
    required this.encryptSensitiveData,
    required this.logSecurityEvents,
    required this.requireStrongPasswords,
    required this.validateApiKeys,
    required this.sanitizeInputs,
  });

  static SecuritySettings defaultSettings() {
    return SecuritySettings(
      encryptSensitiveData: true,
      logSecurityEvents: true,
      requireStrongPasswords: true,
      validateApiKeys: true,
      sanitizeInputs: true,
    );
  }

  SecuritySettings copyWith({
    bool? encryptSensitiveData,
    bool? logSecurityEvents,
    bool? requireStrongPasswords,
    bool? validateApiKeys,
    bool? sanitizeInputs,
  }) {
    return SecuritySettings(
      encryptSensitiveData: encryptSensitiveData ?? this.encryptSensitiveData,
      logSecurityEvents: logSecurityEvents ?? this.logSecurityEvents,
      requireStrongPasswords: requireStrongPasswords ?? this.requireStrongPasswords,
      validateApiKeys: validateApiKeys ?? this.validateApiKeys,
      sanitizeInputs: sanitizeInputs ?? this.sanitizeInputs,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'encryptSensitiveData': encryptSensitiveData,
      'logSecurityEvents': logSecurityEvents,
      'requireStrongPasswords': requireStrongPasswords,
      'validateApiKeys': validateApiKeys,
      'sanitizeInputs': sanitizeInputs,
    };
  }

  factory SecuritySettings.fromMap(Map<String, dynamic> map) {
    return SecuritySettings(
      encryptSensitiveData: map['encryptSensitiveData'] ?? true,
      logSecurityEvents: map['logSecurityEvents'] ?? true,
      requireStrongPasswords: map['requireStrongPasswords'] ?? true,
      validateApiKeys: map['validateApiKeys'] ?? true,
      sanitizeInputs: map['sanitizeInputs'] ?? true,
    );
  }
}

/// سجل الأمان
class SecurityLog {
  final SecurityEventType type;
  final String description;
  final DateTime timestamp;
  final SecuritySeverity severity;

  SecurityLog({
    required this.type,
    required this.description,
    required this.timestamp,
    required this.severity,
  });

  Map<String, dynamic> toMap() {
    return {
      'type': type.toString(),
      'description': description,
      'timestamp': timestamp.toIso8601String(),
      'severity': severity.toString(),
    };
  }

  factory SecurityLog.fromMap(Map<String, dynamic> map) {
    return SecurityLog(
      type: SecurityEventType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => SecurityEventType.systemStart,
      ),
      description: map['description'] ?? '',
      timestamp: DateTime.parse(map['timestamp']),
      severity: SecuritySeverity.values.firstWhere(
        (e) => e.toString() == map['severity'],
        orElse: () => SecuritySeverity.info,
      ),
    );
  }
}

/// قوة كلمة المرور
class PasswordStrength {
  final PasswordStrengthLevel level;
  final int score;
  final int maxScore;
  final List<String> issues;

  PasswordStrength({
    required this.level,
    required this.score,
    required this.maxScore,
    required this.issues,
  });

  double get percentage => score / maxScore;
}

/// أنواع الأحداث الأمنية
enum SecurityEventType {
  systemStart,
  dataEncryption,
  dataDecryption,
  encryptionError,
  decryptionError,
  validApiKey,
  invalidApiKey,
  inputSanitized,
  sessionCreated,
  invalidSession,
}

/// مستويات خطورة الأحداث
enum SecuritySeverity {
  info,
  warning,
  critical,
}

/// مستويات قوة كلمة المرور
enum PasswordStrengthLevel {
  weak,
  medium,
  strong,
}

/// استثناء الأمان
class SecurityException implements Exception {
  final String message;
  SecurityException(this.message);

  @override
  String toString() => 'SecurityException: $message';
}
