import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../utils/app_colors.dart';

// أيقونة متحركة مع تأثير النبض
class PulsingIcon extends StatefulWidget {
  final IconData icon;
  final double size;
  final Color color;
  final Duration duration;

  const PulsingIcon({
    Key? key,
    required this.icon,
    this.size = 24,
    this.color = AppColors.primaryPurple,
    this.duration = const Duration(seconds: 2),
  }) : super(key: key);

  @override
  State<PulsingIcon> createState() => _PulsingIconState();
}

class _PulsingIconState extends State<PulsingIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this)
      ..repeat();

    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.2,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));

    _opacityAnimation = Tween<double>(
      begin: 1.0,
      end: 0.3,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Stack(
          alignment: Alignment.center,
          children: [
            // حلقة النبض
            Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                width: widget.size * 2,
                height: widget.size * 2,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: widget.color.withOpacity(
                    0.3 * _opacityAnimation.value,
                  ),
                ),
              ),
            ),
            // الأيقونة
            Icon(widget.icon, size: widget.size, color: widget.color),
          ],
        );
      },
    );
  }
}

// أيقونة دوارة
class SpinningIcon extends StatefulWidget {
  final IconData icon;
  final double size;
  final Color color;
  final Duration duration;

  const SpinningIcon({
    Key? key,
    required this.icon,
    this.size = 24,
    this.color = AppColors.primaryPurple,
    this.duration = const Duration(seconds: 3),
  }) : super(key: key);

  @override
  State<SpinningIcon> createState() => _SpinningIconState();
}

class _SpinningIconState extends State<SpinningIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this)
      ..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.rotate(
          angle: _controller.value * 2 * math.pi,
          child: Icon(widget.icon, size: widget.size, color: widget.color),
        );
      },
    );
  }
}

// أيقونة متموجة
class WavingIcon extends StatefulWidget {
  final IconData icon;
  final double size;
  final Color color;
  final Duration duration;

  const WavingIcon({
    Key? key,
    required this.icon,
    this.size = 24,
    this.color = AppColors.primaryPurple,
    this.duration = const Duration(seconds: 2),
  }) : super(key: key);

  @override
  State<WavingIcon> createState() => _WavingIconState();
}

class _WavingIconState extends State<WavingIcon>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(duration: widget.duration, vsync: this)
      ..repeat(reverse: true);

    _animation = Tween<double>(
      begin: -0.1,
      end: 0.1,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeInOut));
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Transform.rotate(
          angle: _animation.value,
          child: Icon(widget.icon, size: widget.size, color: widget.color),
        );
      },
    );
  }
}

// مؤشر تحميل مخصص
class CustomLoadingIndicator extends StatefulWidget {
  final double size;
  final Color color;

  const CustomLoadingIndicator({
    Key? key,
    this.size = 50,
    this.color = AppColors.primaryPurple,
  }) : super(key: key);

  @override
  State<CustomLoadingIndicator> createState() => _CustomLoadingIndicatorState();
}

class _CustomLoadingIndicatorState extends State<CustomLoadingIndicator>
    with TickerProviderStateMixin {
  late AnimationController _rotationController;
  late AnimationController _scaleController;

  @override
  void initState() {
    super.initState();
    _rotationController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    _scaleController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _rotationController.dispose();
    _scaleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([_rotationController, _scaleController]),
      builder: (context, child) {
        return Transform.rotate(
          angle: _rotationController.value * 2 * math.pi,
          child: CustomPaint(
            size: Size(widget.size, widget.size),
            painter: _LoadingPainter(
              color: widget.color,
              scale: 0.8 + (_scaleController.value * 0.2),
            ),
          ),
        );
      },
    );
  }
}

class _LoadingPainter extends CustomPainter {
  final Color color;
  final double scale;

  _LoadingPainter({required this.color, required this.scale});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width / 2) * scale;

    // رسم 3 دوائر
    for (int i = 0; i < 3; i++) {
      final angle = (i * 120) * (math.pi / 180);
      final offset = Offset(
        center.dx + math.cos(angle) * radius * 0.5,
        center.dy + math.sin(angle) * radius * 0.5,
      );

      canvas.drawCircle(offset, radius * 0.3, paint);
    }
  }

  @override
  bool shouldRepaint(_LoadingPainter oldDelegate) {
    return oldDelegate.scale != scale;
  }
}

// تأثير الموجة للخلفية
class WaveBackground extends StatefulWidget {
  final Color color;
  final double height;

  const WaveBackground({
    Key? key,
    this.color = AppColors.primaryPurple,
    this.height = 200,
  }) : super(key: key);

  @override
  State<WaveBackground> createState() => _WaveBackgroundState();
}

class _WaveBackgroundState extends State<WaveBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return CustomPaint(
          size: Size(double.infinity, widget.height),
          painter: _WavePainter(
            color: widget.color,
            animationValue: _controller.value,
          ),
        );
      },
    );
  }
}

class _WavePainter extends CustomPainter {
  final Color color;
  final double animationValue;

  _WavePainter({required this.color, required this.animationValue});

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color.withOpacity(0.3)
          ..style = PaintingStyle.fill;

    final path = Path();
    final waveHeight = 20.0;
    final waveLength = size.width / 3;

    path.moveTo(0, size.height);

    for (double x = 0; x <= size.width; x++) {
      final y =
          size.height / 2 +
          math.sin((x / waveLength + animationValue * 2 * math.pi)) *
              waveHeight;
      path.lineTo(x, y);
    }

    path.lineTo(size.width, size.height);
    path.close();

    canvas.drawPath(path, paint);

    // موجة ثانية
    paint.color = color.withOpacity(0.2);
    final path2 = Path();

    path2.moveTo(0, size.height);

    for (double x = 0; x <= size.width; x++) {
      final y =
          size.height / 2 +
          math.sin((x / waveLength + animationValue * 2 * math.pi + math.pi)) *
              waveHeight *
              0.8;
      path2.lineTo(x, y);
    }

    path2.lineTo(size.width, size.height);
    path2.close();

    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(_WavePainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}

// تأثير البريق المتحرك
class SparkleEffect extends StatefulWidget {
  final Widget child;
  final Color sparkleColor;

  const SparkleEffect({
    Key? key,
    required this.child,
    this.sparkleColor = AppColors.lightPurple,
  }) : super(key: key);

  @override
  State<SparkleEffect> createState() => _SparkleEffectState();
}

class _SparkleEffectState extends State<SparkleEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  final List<_Sparkle> _sparkles = [];

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat();

    // إنشاء بريق عشوائي
    for (int i = 0; i < 5; i++) {
      _sparkles.add(
        _Sparkle(
          position: Offset(
            math.Random().nextDouble(),
            math.Random().nextDouble(),
          ),
          delay: math.Random().nextDouble(),
          size: 2 + math.Random().nextDouble() * 4,
        ),
      );
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تعطيل تأثير البريق مؤقتاً
    return widget.child;
  }
}

class _Sparkle {
  final Offset position;
  final double delay;
  final double size;

  _Sparkle({required this.position, required this.delay, required this.size});
}

class _SparklePainter extends CustomPainter {
  final List<_Sparkle> sparkles;
  final double animationValue;
  final Color color;

  _SparklePainter({
    required this.sparkles,
    required this.animationValue,
    required this.color,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = color
          ..style = PaintingStyle.fill;

    for (final sparkle in sparkles) {
      final progress = ((animationValue + sparkle.delay) % 1.0);
      final opacity = math.sin(progress * math.pi);

      paint.color = color.withOpacity(opacity * 0.8);

      final position = Offset(
        sparkle.position.dx * size.width,
        sparkle.position.dy * size.height,
      );

      // رسم نجمة صغيرة
      final path = Path();
      final points = 4;
      final outerRadius = sparkle.size * (1 + progress * 0.5);
      final innerRadius = outerRadius * 0.4;

      for (int i = 0; i < points * 2; i++) {
        final angle = (i * math.pi) / points;
        final radius = i.isEven ? outerRadius : innerRadius;
        final x = position.dx + radius * math.cos(angle);
        final y = position.dy + radius * math.sin(angle);

        if (i == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }

      path.close();
      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(_SparklePainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}
