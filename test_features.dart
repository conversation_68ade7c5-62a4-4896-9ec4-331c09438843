import 'package:flutter/material.dart';

void main() {
  runApp(MyApp());
}

class MyApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'اختبار الميزات',
      theme: ThemeData(
        primarySwatch: Colors.blue,
      ),
      home: FeaturesTestScreen(),
    );
  }
}

class FeaturesTestScreen extends StatelessWidget {
  final functions = [
    {
      'label': 'محادثة ذكية',
      'route': '/smart_chat',
      'icon': Icons.chat_bubble_outline,
      'colors': [const Color(0xFF9B6EF3), const Color(0xFF7D39EB)],
    },
    {
      'label': 'تلخيص النص',
      'route': '/text_summarization',
      'icon': Icons.summarize_outlined,
      'colors': [const Color(0xFF6B7AED), const Color(0xFF5865F2)],
    },
    {
      'label': 'تحليل البيانات',
      'route': '/data_analysis',
      'icon': Icons.analytics_outlined,
      'colors': [const Color(0xFF58D68D), const Color(0xFF27AE60)],
    },
    {
      'label': 'إنشاء خطة',
      'route': '/plan_creation',
      'icon': Icons.calendar_today_outlined,
      'colors': [const Color(0xFFF39C12), const Color(0xFFE67E22)],
    },
    {
      'label': 'مساعدة الكتابة',
      'route': '/writing_assistance',
      'icon': Icons.edit_outlined,
      'colors': [const Color(0xFF5DADE2), const Color(0xFF3498DB)],
    },
    {
      'label': 'إنشاء صورة',
      'route': '/create_image',
      'icon': Icons.image_outlined,
      'colors': [const Color(0xFFEC7063), const Color(0xFFE74C3C)],
    },
    {
      'label': 'الترجمة الذكية',
      'route': '/smart_translation',
      'icon': Icons.translate_outlined,
      'colors': [const Color(0xFF58D68D), const Color(0xFF27AE60)],
    },
    {
      'label': 'التصفح الذكي',
      'route': '/smart_browsing',
      'icon': Icons.web_outlined,
      'colors': [const Color(0xFFAB47BC), const Color(0xFF8E24AA)],
    },
    {
      'label': 'تحليل الصور',
      'route': '/vision_analysis',
      'icon': Icons.image_search_outlined,
      'colors': [const Color(0xFFFF6B6B), const Color(0xFFEE5A24)],
    },
    {
      'label': 'الدردشة الصوتية',
      'route': '/voice_chat',
      'icon': Icons.mic_outlined,
      'colors': [const Color(0xFF4ECDC4), const Color(0xFF44A08D)],
    },
    {
      'label': 'توليد الكود',
      'route': '/code_generation',
      'icon': Icons.code_outlined,
      'colors': [const Color(0xFF667EEA), const Color(0xFF764BA2)],
    },
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        title: Text('اختبار الميزات - العدد: ${functions.length}'),
        backgroundColor: Colors.purple,
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Container(
              padding: EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.purple.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                'إجمالي الميزات المتاحة: ${functions.length}',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            SizedBox(height: 16),
            Expanded(
              child: GridView.builder(
                itemCount: functions.length,
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2,
                  mainAxisSpacing: 12,
                  crossAxisSpacing: 12,
                  childAspectRatio: 1.0,
                ),
                itemBuilder: (context, index) {
                  final function = functions[index];
                  return Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: function['colors'] as List<Color>,
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          function['icon'] as IconData,
                          size: 40,
                          color: Colors.white,
                        ),
                        SizedBox(height: 8),
                        Text(
                          function['label'] as String,
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 4),
                        Text(
                          'رقم ${index + 1}',
                          style: TextStyle(
                            color: Colors.white70,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}
