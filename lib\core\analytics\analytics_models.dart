/// نماذج البيانات للتحليلات الذكية

/// حدث تحليلي
class AnalyticsEvent {
  final String id;
  final String name;
  final String category;
  final Map<String, dynamic> properties;
  final String? userId;
  final DateTime timestamp;

  AnalyticsEvent({
    required this.id,
    required this.name,
    required this.category,
    required this.properties,
    this.userId,
    required this.timestamp,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'properties': properties,
      'userId': userId,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory AnalyticsEvent.fromMap(Map<String, dynamic> map) {
    return AnalyticsEvent(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      category: map['category'] ?? '',
      properties: Map<String, dynamic>.from(map['properties'] ?? {}),
      userId: map['userId'],
      timestamp: DateTime.parse(map['timestamp']),
    );
  }
}

/// بيانات التحليلات
class AnalyticsData {
  final String id;
  final String category;
  final String name;
  final List<AnalyticsEvent> events;
  AnalyticsMetrics metrics;
  final DateTime createdAt;
  DateTime updatedAt;

  AnalyticsData({
    required this.id,
    required this.category,
    required this.name,
    required this.events,
    required this.metrics,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'category': category,
      'name': name,
      'events': events.map((event) => event.toMap()).toList(),
      'metrics': metrics.toMap(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory AnalyticsData.fromMap(Map<String, dynamic> map) {
    return AnalyticsData(
      id: map['id'] ?? '',
      category: map['category'] ?? '',
      name: map['name'] ?? '',
      events: (map['events'] as List? ?? [])
          .map((item) => AnalyticsEvent.fromMap(item))
          .toList(),
      metrics: AnalyticsMetrics.fromMap(map['metrics'] ?? {}),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }
}

/// مقاييس التحليلات
class AnalyticsMetrics {
  final int totalEvents;
  final int todayEvents;
  final int weekEvents;
  final double averagePerDay;
  final DateTime? lastEventTime;

  AnalyticsMetrics({
    this.totalEvents = 0,
    this.todayEvents = 0,
    this.weekEvents = 0,
    this.averagePerDay = 0.0,
    this.lastEventTime,
  });

  Map<String, dynamic> toMap() {
    return {
      'totalEvents': totalEvents,
      'todayEvents': todayEvents,
      'weekEvents': weekEvents,
      'averagePerDay': averagePerDay,
      'lastEventTime': lastEventTime?.toIso8601String(),
    };
  }

  factory AnalyticsMetrics.fromMap(Map<String, dynamic> map) {
    return AnalyticsMetrics(
      totalEvents: map['totalEvents'] ?? 0,
      todayEvents: map['todayEvents'] ?? 0,
      weekEvents: map['weekEvents'] ?? 0,
      averagePerDay: map['averagePerDay']?.toDouble() ?? 0.0,
      lastEventTime: map['lastEventTime'] != null 
          ? DateTime.parse(map['lastEventTime']) 
          : null,
    );
  }
}

/// رؤية ذكية
class AIInsight {
  final String id;
  final InsightType type;
  final String title;
  final String description;
  final double confidence;
  final InsightImpact impact;
  final Map<String, dynamic> data;
  final DateTime createdAt;

  AIInsight({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.confidence,
    required this.impact,
    required this.data,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.toString(),
      'title': title,
      'description': description,
      'confidence': confidence,
      'impact': impact.toString(),
      'data': data,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory AIInsight.fromMap(Map<String, dynamic> map) {
    return AIInsight(
      id: map['id'] ?? '',
      type: InsightType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => InsightType.general,
      ),
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      confidence: map['confidence']?.toDouble() ?? 0.0,
      impact: InsightImpact.values.firstWhere(
        (e) => e.toString() == map['impact'],
        orElse: () => InsightImpact.low,
      ),
      data: Map<String, dynamic>.from(map['data'] ?? {}),
      createdAt: DateTime.parse(map['createdAt']),
    );
  }
}

/// توقع ذكي
class AIPrediction {
  final String id;
  final PredictionType type;
  final String title;
  final String description;
  final double confidence;
  final PredictionTimeframe timeframe;
  final Map<String, dynamic> data;
  final DateTime createdAt;

  AIPrediction({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.confidence,
    required this.timeframe,
    required this.data,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.toString(),
      'title': title,
      'description': description,
      'confidence': confidence,
      'timeframe': timeframe.toString(),
      'data': data,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory AIPrediction.fromMap(Map<String, dynamic> map) {
    return AIPrediction(
      id: map['id'] ?? '',
      type: PredictionType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => PredictionType.general,
      ),
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      confidence: map['confidence']?.toDouble() ?? 0.0,
      timeframe: PredictionTimeframe.values.firstWhere(
        (e) => e.toString() == map['timeframe'],
        orElse: () => PredictionTimeframe.week,
      ),
      data: Map<String, dynamic>.from(map['data'] ?? {}),
      createdAt: DateTime.parse(map['createdAt']),
    );
  }
}

/// ملخص التحليلات
class AnalyticsSummary {
  final int totalEvents;
  final int totalCategories;
  final int totalInsights;
  final int totalPredictions;
  final List<String> topCategories;
  final List<String> topEvents;
  final DateTime? lastAnalysis;

  AnalyticsSummary({
    required this.totalEvents,
    required this.totalCategories,
    required this.totalInsights,
    required this.totalPredictions,
    required this.topCategories,
    required this.topEvents,
    this.lastAnalysis,
  });
}

/// أنواع الرؤى
enum InsightType {
  usage,
  performance,
  behavior,
  trend,
  anomaly,
  optimization,
  general,
}

/// تأثير الرؤية
enum InsightImpact {
  low,
  medium,
  high,
  critical,
}

/// أنواع التوقعات
enum PredictionType {
  usage,
  performance,
  trend,
  behavior,
  revenue,
  growth,
  general,
}

/// إطار زمني للتوقع
enum PredictionTimeframe {
  day,
  week,
  month,
  quarter,
  year,
}
