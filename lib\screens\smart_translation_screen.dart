import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../core/services/enhanced_ai_service.dart';
import '../widgets/modern_ui_components.dart';

class SmartTranslationScreen extends StatefulWidget {
  const SmartTranslationScreen({super.key});

  @override
  State<SmartTranslationScreen> createState() => _SmartTranslationScreenState();
}

class _SmartTranslationScreenState extends State<SmartTranslationScreen>
    with TickerProviderStateMixin {
  final TextEditingController _sourceController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  String _translatedText = '';
  bool _isLoading = false;
  String _sourceLanguage = 'ar';
  String _targetLanguage = 'en';
  String _translationStyle = 'standard';

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<Map<String, String>> _languages = [
    {'name': '🇸🇦 العربية', 'code': 'ar'},
    {'name': '🇺🇸 English', 'code': 'en'},
    {'name': '🇫🇷 Français', 'code': 'fr'},
    {'name': '🇩🇪 Deutsch', 'code': 'de'},
    {'name': '🇪🇸 Español', 'code': 'es'},
    {'name': '🇮🇹 Italiano', 'code': 'it'},
    {'name': '🇵🇹 Português', 'code': 'pt'},
    {'name': '🇷🇺 Русский', 'code': 'ru'},
    {'name': '🇯🇵 日本語', 'code': 'ja'},
    {'name': '🇰🇷 한국어', 'code': 'ko'},
    {'name': '🇨🇳 中文', 'code': 'zh'},
    {'name': '🇮🇳 हिन्दी', 'code': 'hi'},
    {'name': '🇹🇷 Türkçe', 'code': 'tr'},
    {'name': '🇮🇷 فارسی', 'code': 'fa'},
    {'name': '🇵🇰 اردو', 'code': 'ur'},
  ];

  final List<Map<String, String>> _translationStyles = [
    {'name': 'ترجمة عادية', 'code': 'standard'},
    {'name': 'ترجمة رسمية', 'code': 'formal'},
    {'name': 'ترجمة غير رسمية', 'code': 'casual'},
    {'name': 'ترجمة تقنية', 'code': 'technical'},
    {'name': 'ترجمة أدبية', 'code': 'literary'},
    {'name': 'ترجمة تجارية', 'code': 'business'},
  ];

  final List<Map<String, dynamic>> _quickActions = [
    {
      'icon': Icons.swap_horiz,
      'title': 'تبديل اللغات',
      'subtitle': 'عكس الاتجاه',
      'color': Colors.blue,
    },
    {
      'icon': Icons.file_upload_outlined,
      'title': 'رفع ملف',
      'subtitle': 'ترجمة ملف',
      'color': Colors.green,
    },
    {
      'icon': Icons.content_paste,
      'title': 'لصق النص',
      'subtitle': 'من الحافظة',
      'color': Colors.orange,
    },
    {
      'icon': Icons.clear_all,
      'title': 'مسح الكل',
      'subtitle': 'بدء جديد',
      'color': Colors.red,
    },
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _sourceController.dispose();
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _translateText() async {
    if (_sourceController.text.trim().isEmpty) {
      _showSnackBar('يرجى إدخال النص المراد ترجمته', isError: true);
      return;
    }

    if (_sourceLanguage == _targetLanguage) {
      _showSnackBar('يرجى اختيار لغات مختلفة للترجمة', isError: true);
      return;
    }

    setState(() {
      _isLoading = true;
      _translatedText = '';
    });

    try {
      final result = await EnhancedAIService.translateText(
        text: _sourceController.text.trim(),
        sourceLanguage: _sourceLanguage,
        targetLanguage: _targetLanguage,
        style: _translationStyle,
      );

      setState(() {
        _translatedText = result;
        _isLoading = false;
      });

      _showSnackBar('تمت الترجمة بنجاح!');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showSnackBar('حدث خطأ أثناء الترجمة: ${e.toString()}', isError: true);
    }
  }

  void _swapLanguages() {
    setState(() {
      final temp = _sourceLanguage;
      _sourceLanguage = _targetLanguage;
      _targetLanguage = temp;

      // تبديل النصوص أيضاً
      if (_translatedText.isNotEmpty) {
        final tempText = _sourceController.text;
        _sourceController.text = _translatedText;
        _translatedText = tempText;
      }
    });

    _showSnackBar('تم تبديل اللغات والنصوص');
  }

  Future<void> _pickFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['txt', 'md', 'doc', 'docx'],
      );

      if (result != null) {
        File file = File(result.files.single.path!);
        String content = await file.readAsString();

        setState(() {
          _sourceController.text = content;
        });

        _showSnackBar('تم تحميل الملف بنجاح');
      }
    } catch (e) {
      _showSnackBar('فشل في تحميل الملف: ${e.toString()}', isError: true);
    }
  }

  Future<void> _pasteFromClipboard() async {
    try {
      ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
      if (data != null && data.text != null) {
        setState(() {
          _sourceController.text = data.text!;
        });
        _showSnackBar('تم لصق النص من الحافظة');
      } else {
        _showSnackBar('لا يوجد نص في الحافظة', isError: true);
      }
    } catch (e) {
      _showSnackBar('فشل في لصق النص: ${e.toString()}', isError: true);
    }
  }

  void _clearAll() {
    setState(() {
      _sourceController.clear();
      _translatedText = '';
    });
    _showSnackBar('تم مسح جميع البيانات');
  }

  void _handleQuickAction(int index) {
    switch (index) {
      case 0:
        _swapLanguages();
        break;
      case 1:
        _pickFile();
        break;
      case 2:
        _pasteFromClipboard();
        break;
      case 3:
        _clearAll();
        break;
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : AppColors.primaryPurple,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.darkPurple.withValues(alpha: 0.9),
              AppColors.background,
              AppColors.primaryPurple.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                _buildHeader(),
                _buildQuickActions(),
                _buildLanguageSelector(),
                Expanded(
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        _buildInputSection(),
                        const SizedBox(height: 20),
                        _buildStyleSelector(),
                        const SizedBox(height: 20),
                        _buildTranslateButton(),
                        if (_translatedText.isNotEmpty || _isLoading) ...[
                          const SizedBox(height: 20),
                          _buildResultSection(),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.electricBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => Navigator.pop(context),
                borderRadius: BorderRadius.circular(12),
                child: const Padding(
                  padding: EdgeInsets.all(12),
                  child: Icon(Icons.arrow_back, color: Colors.white, size: 20),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.translate_outlined,
                      color: AppColors.electricBlue,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'الترجمة الذكية',
                      style: AppTextStyles.heading2.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Text(
                  'ترجم النصوص بدقة واحترافية',
                  style: AppTextStyles.body.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Container(
      height: 100,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _quickActions.length,
        itemBuilder: (context, index) {
          final action = _quickActions[index];
          return Container(
            width: 120,
            margin: const EdgeInsets.only(right: 12),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _handleQuickAction(index),
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        action['color'].withValues(alpha: 0.2),
                        action['color'].withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: action['color'].withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        action['icon'],
                        color: action['color'],
                        size: 24,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        action['title'],
                        style: AppTextStyles.caption.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        action['subtitle'],
                        style: AppTextStyles.caption.copyWith(
                          color: AppColors.textSecondary,
                          fontSize: 10,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildLanguageSelector() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.electricBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          // اللغة المصدر
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'من',
                    style: AppTextStyles.body.copyWith(
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: AppColors.darkGrey.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.electricBlue.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: _sourceLanguage,
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _sourceLanguage = value;
                            });
                          }
                        },
                        dropdownColor: AppColors.darkGrey,
                        style: AppTextStyles.body.copyWith(color: Colors.white),
                        icon: Icon(
                          Icons.keyboard_arrow_down,
                          color: AppColors.electricBlue,
                        ),
                        items: _languages.map((lang) {
                          return DropdownMenuItem<String>(
                            value: lang['code'],
                            child: Text(lang['name']!),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),

          // زر التبديل
          Container(
            margin: const EdgeInsets.symmetric(horizontal: 8),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: _swapLanguages,
                borderRadius: BorderRadius.circular(25),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.primaryPurple.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(25),
                    border: Border.all(
                      color: AppColors.electricBlue.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.swap_horiz,
                    color: AppColors.electricBlue,
                    size: 20,
                  ),
                ),
              ),
            ),
          ),

          // اللغة الهدف
          Expanded(
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'إلى',
                    style: AppTextStyles.body.copyWith(
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
                    decoration: BoxDecoration(
                      color: AppColors.darkGrey.withValues(alpha: 0.5),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: AppColors.electricBlue.withValues(alpha: 0.3),
                        width: 1,
                      ),
                    ),
                    child: DropdownButtonHideUnderline(
                      child: DropdownButton<String>(
                        value: _targetLanguage,
                        onChanged: (value) {
                          if (value != null) {
                            setState(() {
                              _targetLanguage = value;
                            });
                          }
                        },
                        dropdownColor: AppColors.darkGrey,
                        style: AppTextStyles.body.copyWith(color: Colors.white),
                        icon: Icon(
                          Icons.keyboard_arrow_down,
                          color: AppColors.electricBlue,
                        ),
                        items: _languages.map((lang) {
                          return DropdownMenuItem<String>(
                            value: lang['code'],
                            child: Text(lang['name']!),
                          );
                        }).toList(),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputSection() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.electricBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.text_fields,
                  color: AppColors.electricBlue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'النص المراد ترجمته',
                  style: AppTextStyles.body.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.electricBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: TextField(
              controller: _sourceController,
              style: AppTextStyles.body.copyWith(color: Colors.white),
              maxLines: 6,
              decoration: InputDecoration(
                hintText: 'اكتب أو الصق النص هنا...',
                hintStyle: AppTextStyles.body.copyWith(
                  color: AppColors.textSecondary,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStyleSelector() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.electricBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.style_outlined,
                color: AppColors.electricBlue,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'أسلوب الترجمة',
                style: AppTextStyles.body.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.electricBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: DropdownButtonHideUnderline(
              child: DropdownButton<String>(
                value: _translationStyle,
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _translationStyle = value;
                    });
                  }
                },
                dropdownColor: AppColors.darkGrey,
                style: AppTextStyles.body.copyWith(color: Colors.white),
                icon: Icon(
                  Icons.keyboard_arrow_down,
                  color: AppColors.electricBlue,
                ),
                items: _translationStyles.map((style) {
                  return DropdownMenuItem<String>(
                    value: style['code'],
                    child: Text(style['name']!),
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTranslateButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryPurple,
            AppColors.electricBlue,
          ],
        ),
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryPurple.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _isLoading ? null : _translateText,
          borderRadius: BorderRadius.circular(28),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (_isLoading) ...[
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'جاري الترجمة...',
                    style: AppTextStyles.body.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ] else ...[
                  Icon(
                    Icons.translate,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'ترجمة النص',
                    style: AppTextStyles.body.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildResultSection() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.electricBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.translate,
                  color: AppColors.electricBlue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'النص المترجم',
                  style: AppTextStyles.body.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (_translatedText.isNotEmpty)
                  GestureDetector(
                    onTap: () {
                      Clipboard.setData(ClipboardData(text: _translatedText));
                      _showSnackBar('تم نسخ الترجمة');
                    },
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.primaryPurple.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.copy,
                        color: AppColors.electricBlue,
                        size: 16,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.electricBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: _isLoading
                ? Center(
                    child: Column(
                      children: [
                        CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColors.electricBlue,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'جاري ترجمة النص...',
                          style: AppTextStyles.body.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  )
                : SelectableText(
                    _translatedText,
                    style: AppTextStyles.body.copyWith(
                      color: Colors.white,
                      height: 1.6,
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}