import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../core/services/unified_api_gateway.dart';
import '../services/text_to_speech_service.dart';
import '../widgets/enhanced_widgets.dart';
import '../widgets/glassmorphism_widgets.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';

/// شاشة الترجمة الذكية
class SmartTranslationScreen extends StatefulWidget {
  const SmartTranslationScreen({super.key});

  @override
  State<SmartTranslationScreen> createState() => _SmartTranslationScreenState();
}

class _SmartTranslationScreenState extends State<SmartTranslationScreen>
    with TickerProviderStateMixin {
  final TextEditingController _sourceController = TextEditingController();
  String _translatedText = '';
  bool _isTranslating = false;
  String _sourceLanguage = 'ar';
  String _targetLanguage = 'en';
  String _translationStyle = 'standard';

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final Map<String, String> _languages = {
    'ar': '🇸🇦 العربية',
    'en': '🇺🇸 English',
    'fr': '🇫🇷 Français',
    'de': '🇩🇪 Deutsch',
    'es': '🇪🇸 Español',
    'it': '🇮🇹 Italiano',
    'pt': '🇵🇹 Português',
    'ru': '🇷🇺 Русский',
    'ja': '🇯🇵 日本語',
    'ko': '🇰🇷 한국어',
    'zh': '🇨🇳 中文',
    'hi': '🇮🇳 हिन्दी',
    'tr': '🇹🇷 Türkçe',
    'nl': '🇳🇱 Nederlands',
    'sv': '🇸🇪 Svenska',
  };

  final Map<String, String> _translationStyles = {
    'standard': 'ترجمة عادية',
    'formal': 'ترجمة رسمية',
    'casual': 'ترجمة غير رسمية',
    'technical': 'ترجمة تقنية',
    'literary': 'ترجمة أدبية',
    'business': 'ترجمة تجارية',
  };

  @override
  void initState() {
    super.initState();
    _initAnimations();
    _initTextToSpeech();
  }

  void _initAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOut));

    _fadeController.forward();
    _slideController.forward();
  }

  void _initTextToSpeech() async {
    await TextToSpeechService.initialize();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _sourceController.dispose();
    super.dispose();
  }

  Future<void> _translateText() async {
    if (_sourceController.text.trim().isEmpty) {
      _showErrorSnackBar('يرجى إدخال النص المراد ترجمته');
      return;
    }

    setState(() {
      _isTranslating = true;
      _translatedText = '';
    });

    try {
      final prompt = _buildTranslationPrompt();
      
      final response = await UnifiedApiGateway.sendChatRequest(
        message: prompt,
        temperature: 0.3,
        maxTokens: 1000,
      );

      setState(() {
        _translatedText = response['content'] ?? 'لم يتم الحصول على ترجمة';
      });

      _showSuccessSnackBar('تمت الترجمة بنجاح!');
    } catch (e) {
      _showErrorSnackBar('خطأ في الترجمة: $e');
    } finally {
      setState(() {
        _isTranslating = false;
      });
    }
  }

  String _buildTranslationPrompt() {
    final sourceLanguageName = _getLanguageName(_sourceLanguage);
    final targetLanguageName = _getLanguageName(_targetLanguage);
    final styleName = _translationStyles[_translationStyle] ?? 'عادية';

    return '''
قم بترجمة النص التالي من $sourceLanguageName إلى $targetLanguageName بأسلوب $styleName:

النص الأصلي:
"${_sourceController.text}"

متطلبات الترجمة:
- احتفظ بالمعنى الأصلي بدقة
- استخدم أسلوب $styleName
- تأكد من الطبيعية في اللغة المستهدفة
- احتفظ بأي مصطلحات تقنية إذا كانت موجودة

الترجمة:''';
  }

  String _getLanguageName(String code) {
    switch (code) {
      case 'ar': return 'العربية';
      case 'en': return 'الإنجليزية';
      case 'fr': return 'الفرنسية';
      case 'de': return 'الألمانية';
      case 'es': return 'الإسبانية';
      case 'it': return 'الإيطالية';
      case 'pt': return 'البرتغالية';
      case 'ru': return 'الروسية';
      case 'ja': return 'اليابانية';
      case 'ko': return 'الكورية';
      case 'zh': return 'الصينية';
      case 'hi': return 'الهندية';
      case 'tr': return 'التركية';
      case 'nl': return 'الهولندية';
      case 'sv': return 'السويدية';
      default: return code;
    }
  }

  void _swapLanguages() {
    setState(() {
      final temp = _sourceLanguage;
      _sourceLanguage = _targetLanguage;
      _targetLanguage = temp;
      
      // تبديل النصوص أيضاً
      if (_translatedText.isNotEmpty) {
        _sourceController.text = _translatedText;
        _translatedText = '';
      }
    });
  }

  void _copyToClipboard(String text) {
    Clipboard.setData(ClipboardData(text: text));
    _showSuccessSnackBar('تم نسخ النص إلى الحافظة');
  }

  void _speakText(String text, String languageCode) async {
    try {
      await TextToSpeechService.setLanguage('$languageCode-SA');
      await TextToSpeechService.speak(text);
    } catch (e) {
      _showErrorSnackBar('خطأ في تشغيل النص: $e');
    }
  }

  void _clearText() {
    setState(() {
      _sourceController.clear();
      _translatedText = '';
    });
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('🌐 الترجمة الذكية'),
        backgroundColor: AppColors.darkPurple,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _clearText,
            icon: const Icon(Icons.clear_all),
            tooltip: 'مسح الكل',
          ),
        ],
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildLanguageSelectionSection(),
                const SizedBox(height: 20),
                _buildTranslationStyleSection(),
                const SizedBox(height: 20),
                _buildSourceTextSection(),
                const SizedBox(height: 20),
                _buildTranslateButton(),
                const SizedBox(height: 20),
                _buildTranslatedTextSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildLanguageSelectionSection() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '🌍 اختيار اللغات',
            style: AppTextStyles.heading.copyWith(
              color: AppColors.primaryPurple,
            ),
          ),
          const SizedBox(height: 15),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'من:',
                      style: AppTextStyles.body.copyWith(
                        color: AppColors.lightPurple,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _sourceLanguage,
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: AppColors.darkGrey,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                      ),
                      dropdownColor: AppColors.darkGrey,
                      style: AppTextStyles.body.copyWith(color: AppColors.white),
                      items: _languages.entries.map((entry) {
                        return DropdownMenuItem<String>(
                          value: entry.key,
                          child: Text(entry.value),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _sourceLanguage = value;
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
              const SizedBox(width: 10),
              IconButton(
                onPressed: _swapLanguages,
                icon: const Icon(
                  Icons.swap_horiz,
                  color: AppColors.primaryPurple,
                  size: 32,
                ),
                tooltip: 'تبديل اللغات',
              ),
              const SizedBox(width: 10),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'إلى:',
                      style: AppTextStyles.body.copyWith(
                        color: AppColors.lightPurple,
                      ),
                    ),
                    const SizedBox(height: 8),
                    DropdownButtonFormField<String>(
                      value: _targetLanguage,
                      decoration: InputDecoration(
                        filled: true,
                        fillColor: AppColors.darkGrey,
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(12),
                          borderSide: BorderSide.none,
                        ),
                      ),
                      dropdownColor: AppColors.darkGrey,
                      style: AppTextStyles.body.copyWith(color: AppColors.white),
                      items: _languages.entries.map((entry) {
                        return DropdownMenuItem<String>(
                          value: entry.key,
                          child: Text(entry.value),
                        );
                      }).toList(),
                      onChanged: (value) {
                        if (value != null) {
                          setState(() {
                            _targetLanguage = value;
                          });
                        }
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTranslationStyleSection() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '🎨 أسلوب الترجمة',
            style: AppTextStyles.heading.copyWith(
              color: AppColors.primaryPurple,
            ),
          ),
          const SizedBox(height: 15),
          DropdownButtonFormField<String>(
            value: _translationStyle,
            decoration: InputDecoration(
              filled: true,
              fillColor: AppColors.darkGrey,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
            ),
            dropdownColor: AppColors.darkGrey,
            style: AppTextStyles.body.copyWith(color: AppColors.white),
            items: _translationStyles.entries.map((entry) {
              return DropdownMenuItem<String>(
                value: entry.key,
                child: Text(entry.value),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _translationStyle = value;
                });
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSourceTextSection() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '📝 النص الأصلي',
                style: AppTextStyles.heading.copyWith(
                  color: AppColors.primaryPurple,
                ),
              ),
              const Spacer(),
              if (_sourceController.text.isNotEmpty)
                IconButton(
                  onPressed: () => _speakText(_sourceController.text, _sourceLanguage),
                  icon: const Icon(Icons.volume_up, color: AppColors.lightPurple),
                  tooltip: 'تشغيل النص',
                ),
            ],
          ),
          const SizedBox(height: 15),
          TextField(
            controller: _sourceController,
            decoration: InputDecoration(
              hintText: 'أدخل النص المراد ترجمته...',
              filled: true,
              fillColor: AppColors.darkGrey,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              hintStyle: AppTextStyles.body.copyWith(
                color: AppColors.white.withOpacity(0.5),
              ),
            ),
            style: AppTextStyles.body.copyWith(color: AppColors.white),
            maxLines: 6,
            textDirection: _sourceLanguage == 'ar' ? TextDirection.rtl : TextDirection.ltr,
          ),
        ],
      ),
    );
  }

  Widget _buildTranslateButton() {
    return EnhancedButton(
      text: _isTranslating ? 'جاري الترجمة...' : '🌐 ترجمة',
      onPressed: _isTranslating ? null : _translateText,
      isLoading: _isTranslating,
      size: ButtonSize.large,
    );
  }

  Widget _buildTranslatedTextSection() {
    if (_translatedText.isEmpty && !_isTranslating) {
      return const SizedBox.shrink();
    }

    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                '✨ النص المترجم',
                style: AppTextStyles.heading.copyWith(
                  color: AppColors.primaryPurple,
                ),
              ),
              const Spacer(),
              if (_translatedText.isNotEmpty) ...[
                IconButton(
                  onPressed: () => _speakText(_translatedText, _targetLanguage),
                  icon: const Icon(Icons.volume_up, color: AppColors.lightPurple),
                  tooltip: 'تشغيل الترجمة',
                ),
                IconButton(
                  onPressed: () => _copyToClipboard(_translatedText),
                  icon: const Icon(Icons.copy, color: AppColors.lightPurple),
                  tooltip: 'نسخ الترجمة',
                ),
              ],
            ],
          ),
          const SizedBox(height: 15),
          if (_isTranslating)
            const Center(
              child: EnhancedLoadingIndicator(
                message: 'جاري الترجمة...',
              ),
            )
          else
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.darkGrey.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.primaryPurple.withOpacity(0.3),
                ),
              ),
              child: Text(
                _translatedText,
                style: AppTextStyles.body.copyWith(
                  height: 1.6,
                  fontSize: 16,
                ),
                textDirection: _targetLanguage == 'ar' ? TextDirection.rtl : TextDirection.ltr,
              ),
            ),
        ],
      ),
    );
  }
}
