import 'lib/tools/real_ai_tools_hub.dart';

/// اختبار سريع لجميع أدوات الذكاء الاصطناعي
void main() async {
  print('🚀 بدء اختبار أدوات الذكاء الاصطناعي...\n');

  // اختبار 1: إنشاء الصور
  print('🎨 اختبار إنشاء الصور...');
  try {
    final imageUrl = await RealAIToolsHub.generateImage(
      prompt: 'منظر طبيعي خلاب لغروب الشمس على البحر',
      size: '1024x1024',
      quality: 'standard',
    );
    print('✅ نجح إنشاء الصورة: $imageUrl');
  } catch (e) {
    print('❌ فشل إنشاء الصورة: $e');
  }

  print('\n' + '='*50 + '\n');

  // اختبار 2: تلخيص النصوص
  print('📝 اختبار تلخيص النصوص...');
  try {
    const text = '''
    الذكاء الاصطناعي هو محاكاة الذكاء البشري في الآلات المبرمجة للتفكير والتعلم مثل البشر.
    يمكن تطبيق هذا المصطلح أيضًا على أي آلة تظهر سمات مرتبطة بالعقل البشري مثل التعلم وحل المشكلات.
    الخاصية المثالية للذكاء الاصطناعي هي قدرته على ترشيد واتخاذ الإجراءات التي لديها أفضل فرصة لتحقيق هدف محدد.
    ''';

    final summary = await RealAIToolsHub.summarizeText(
      text: text,
      style: 'comprehensive',
      maxLength: 100,
    );
    print('✅ نجح تلخيص النص: $summary');
  } catch (e) {
    print('❌ فشل تلخيص النص: $e');
  }

  print('\n' + '='*50 + '\n');

  // اختبار 3: تحليل البيانات
  print('📊 اختبار تحليل البيانات...');
  try {
    const data = '''
    الشهر,المبيعات,العملاء
    يناير,15000,120
    فبراير,18000,145
    مارس,22000,180
    أبريل,25000,210
    ''';

    final analysis = await RealAIToolsHub.analyzeData(
      data: data,
      analysisType: 'statistical',
    );
    print('✅ نجح تحليل البيانات: ${analysis['analysis']}');
  } catch (e) {
    print('❌ فشل تحليل البيانات: $e');
  }

  print('\n' + '='*50 + '\n');

  // اختبار 4: إنشاء الخطط
  print('📅 اختبار إنشاء الخطط...');
  try {
    final plan = await RealAIToolsHub.createPlan(
      goal: 'تعلم البرمجة بلغة Flutter',
      timeframe: '30 يوم',
      difficulty: 'متوسط',
      constraints: ['وقت محدود'],
      resources: ['كمبيوتر', 'إنترنت'],
    );
    print('✅ نجح إنشاء الخطة: ${plan['plan']}');
  } catch (e) {
    print('❌ فشل إنشاء الخطة: $e');
  }

  print('\n' + '='*50 + '\n');

  // اختبار 5: مساعدة الكتابة
  print('✍️ اختبار مساعدة الكتابة...');
  try {
    final content = await RealAIToolsHub.assistWriting(
      topic: 'أهمية التكنولوجيا في التعليم',
      style: 'academic',
      length: 200,
    );
    print('✅ نجحت مساعدة الكتابة: ${content['content']}');
  } catch (e) {
    print('❌ فشلت مساعدة الكتابة: $e');
  }

  print('\n' + '='*50 + '\n');

  // اختبار 6: التحقق من توفر الأدوات
  print('🔍 اختبار توفر الأدوات...');
  try {
    final availability = await RealAIToolsHub.checkToolsAvailability();
    print('✅ حالة الأدوات:');
    availability.forEach((tool, isAvailable) {
      print('  $tool: ${isAvailable ? "✅ متاح" : "❌ غير متاح"}');
    });
  } catch (e) {
    print('❌ فشل في التحقق من الأدوات: $e');
  }

  print('\n🎉 انتهى اختبار جميع الأدوات!');
}
