import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/app_colors.dart';
import '../widgets/modern_ui_components.dart';
import '../core/analytics/ai_analytics_engine.dart';
import '../core/analytics/dashboard_service.dart';

/// شاشة التحليلات الذكية
class AIAnalyticsScreen extends ConsumerStatefulWidget {
  const AIAnalyticsScreen({super.key});

  @override
  ConsumerState<AIAnalyticsScreen> createState() => _AIAnalyticsScreenState();
}

class _AIAnalyticsScreenState extends ConsumerState<AIAnalyticsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeServices();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    setState(() => _isLoading = true);
    try {
      await AIAnalyticsEngine.initialize();
      await DashboardService.initialize();
      await _generateSampleData();
    } catch (e) {
      debugPrint('خطأ في تهيئة التحليلات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _generateSampleData() async {
    // إضافة بيانات تجريبية
    await AIAnalyticsEngine.trackEvent(
      eventName: 'app_opened',
      category: 'usage',
      properties: {'source': 'direct'},
    );
    
    await AIAnalyticsEngine.trackEvent(
      eventName: 'feature_used',
      category: 'interaction',
      properties: {'feature': 'ai_chat'},
    );
    
    await AIAnalyticsEngine.analyzeData();
    await AIAnalyticsEngine.generatePredictions();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.darkGrey,
        title: Text(
          'التحليلات الذكية',
          style: TextStyle(color: AppColors.white),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh, color: AppColors.electricBlue),
            onPressed: _refreshAnalytics,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primaryPurple,
          unselectedLabelColor: AppColors.lightGrey,
          indicatorColor: AppColors.primaryPurple,
          tabs: const [
            Tab(text: 'نظرة عامة', icon: Icon(Icons.dashboard)),
            Tab(text: 'الرؤى', icon: Icon(Icons.lightbulb)),
            Tab(text: 'التوقعات', icon: Icon(Icons.trending_up)),
            Tab(text: 'التقارير', icon: Icon(Icons.assessment)),
          ],
        ),
      ),
      body: _isLoading
          ? Center(child: ModernUIComponents.modernLoadingIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildInsightsTab(),
                _buildPredictionsTab(),
                _buildReportsTab(),
              ],
            ),
    );
  }

  Widget _buildOverviewTab() {
    final summary = AIAnalyticsEngine.getAnalyticsSummary();
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSummaryCards(summary),
          const SizedBox(height: 24),
          _buildQuickInsights(),
          const SizedBox(height: 24),
          _buildUsageChart(),
        ],
      ),
    );
  }

  Widget _buildSummaryCards(summary) {
    return Row(
      children: [
        Expanded(
          child: _buildSummaryCard(
            'إجمالي الأحداث',
            '${summary.totalEvents}',
            Icons.event,
            AppColors.primaryPurple,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildSummaryCard(
            'الرؤى الذكية',
            '${summary.totalInsights}',
            Icons.lightbulb,
            AppColors.electricBlue,
          ),
        ),
      ],
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              Text(
                value,
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickInsights() {
    final insights = AIAnalyticsEngine.insights.take(3).toList();
    
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'رؤى سريعة',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          if (insights.isEmpty)
            Text(
              'لا توجد رؤى متاحة حالياً',
              style: TextStyle(color: AppColors.textSecondary),
            )
          else
            ...insights.map((insight) => _buildInsightItem(insight)),
        ],
      ),
    );
  }

  Widget _buildInsightItem(insight) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            Icons.lightbulb_outline,
            color: AppColors.electricBlue,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  insight.title,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  insight.description,
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.electricBlue.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${(insight.confidence * 100).toInt()}%',
              style: TextStyle(
                color: AppColors.electricBlue,
                fontSize: 10,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsageChart() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اتجاه الاستخدام',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Container(
            height: 200,
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  AppColors.primaryPurple.withValues(alpha: 0.1),
                  AppColors.electricBlue.withValues(alpha: 0.1),
                ],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.show_chart,
                    size: 48,
                    color: AppColors.electricBlue,
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'رسم بياني تفاعلي',
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'سيتم تطويره في المرحلة القادمة',
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInsightsTab() {
    final insights = AIAnalyticsEngine.insights;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الرؤى الذكية',
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              ModernUIComponents.modernButton(
                text: 'تحليل جديد',
                onPressed: _runNewAnalysis,
                icon: Icons.analytics,
              ),
            ],
          ),
          const SizedBox(height: 24),
          if (insights.isEmpty)
            _buildEmptyInsights()
          else
            ...insights.map((insight) => _buildDetailedInsightCard(insight)),
        ],
      ),
    );
  }

  Widget _buildEmptyInsights() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(40),
      child: Column(
        children: [
          Icon(
            Icons.lightbulb_outline,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد رؤى متاحة',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'قم بتشغيل تحليل جديد لإنتاج رؤى ذكية',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildDetailedInsightCard(insight) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: ModernUIComponents.glassCard(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: AppColors.primaryGradient,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    Icons.lightbulb,
                    color: AppColors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    insight.title,
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                ModernUIComponents.modernBadge(
                  text: '${(insight.confidence * 100).toInt()}%',
                  backgroundColor: AppColors.electricBlue,
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              insight.description,
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPredictionsTab() {
    final predictions = AIAnalyticsEngine.predictions;
    
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التوقعات الذكية',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          if (predictions.isEmpty)
            _buildEmptyPredictions()
          else
            ...predictions.map((prediction) => _buildPredictionCard(prediction)),
        ],
      ),
    );
  }

  Widget _buildEmptyPredictions() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(40),
      child: Column(
        children: [
          Icon(
            Icons.trending_up,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد توقعات متاحة',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPredictionCard(prediction) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: ModernUIComponents.glassCard(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    gradient: AppColors.glowGradient,
                    borderRadius: BorderRadius.circular(10),
                  ),
                  child: Icon(
                    Icons.trending_up,
                    color: AppColors.white,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    prediction.title,
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(
              prediction.description,
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildReportsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التقارير الذكية',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          ModernUIComponents.modernButton(
            text: 'إنشاء تقرير شامل',
            onPressed: _generateReport,
            icon: Icons.assessment,
            width: double.infinity,
          ),
          const SizedBox(height: 16),
          ModernUIComponents.glassCard(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'أنواع التقارير المتاحة',
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                _buildReportOption('تقرير الاستخدام', 'تحليل شامل لأنماط الاستخدام'),
                _buildReportOption('تقرير الأداء', 'مقاييس الأداء والكفاءة'),
                _buildReportOption('تقرير التوقعات', 'توقعات مستقبلية ذكية'),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReportOption(String title, String description) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(
            Icons.description,
            color: AppColors.primaryPurple,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: AppColors.textSecondary,
            size: 16,
          ),
        ],
      ),
    );
  }

  Future<void> _refreshAnalytics() async {
    setState(() => _isLoading = true);
    try {
      await AIAnalyticsEngine.analyzeData();
      await AIAnalyticsEngine.generatePredictions();
      await DashboardService.refreshAllWidgets();
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _runNewAnalysis() async {
    await AIAnalyticsEngine.analyzeData();
    setState(() {});
  }

  Future<void> _generateReport() async {
    // إظهار رسالة تأكيد
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('تم إنشاء التقرير بنجاح!'),
        backgroundColor: AppColors.success,
      ),
    );
  }
}
