import 'package:flutter/material.dart';
import '../models/feature_api_model.dart';
import 'storage_service.dart';

/// خدمة إدارة APIs الميزات
class FeatureApiService {
  static const String _storageKey = 'feature_apis';
  static Map<String, FeatureApiConfig> _configs = {};
  static bool _isInitialized = false;

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadConfigs();
      await _initializeDefaultConfigs();
      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة APIs الميزات');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة APIs الميزات: $e');
    }
  }

  /// تحميل الإعدادات المحفوظة
  static Future<void> _loadConfigs() async {
    try {
      final data = StorageService.getMapData(_storageKey);
      if (data != null) {
        _configs = data.map((key, value) => 
          MapEntry(key, FeatureApiConfig.fromMap(Map<String, dynamic>.from(value))));
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إعدادات APIs: $e');
    }
  }

  /// تهيئة الإعدادات الافتراضية
  static Future<void> _initializeDefaultConfigs() async {
    final defaultConfigs = [
      FeatureApiConfig(
        featureId: 'text_summarization',
        featureName: 'Text Summarization',
        featureNameAr: 'تلخيص النصوص',
        icon: Icons.summarize_outlined,
        color: Colors.blue,
        customSettings: FeatureSettings.getDefaultSettings(FeatureType.textSummarization),
      ),
      FeatureApiConfig(
        featureId: 'image_generation',
        featureName: 'Image Generation',
        featureNameAr: 'إنشاء الصور',
        icon: Icons.image_outlined,
        color: Colors.purple,
        customSettings: FeatureSettings.getDefaultSettings(FeatureType.imageGeneration),
      ),
      FeatureApiConfig(
        featureId: 'data_analysis',
        featureName: 'Data Analysis',
        featureNameAr: 'تحليل البيانات',
        icon: Icons.analytics_outlined,
        color: Colors.green,
        customSettings: FeatureSettings.getDefaultSettings(FeatureType.dataAnalysis),
      ),
      FeatureApiConfig(
        featureId: 'plan_creation',
        featureName: 'Plan Creation',
        featureNameAr: 'إنشاء الخطط',
        icon: Icons.calendar_today_outlined,
        color: Colors.orange,
        customSettings: FeatureSettings.getDefaultSettings(FeatureType.planCreation),
      ),
      FeatureApiConfig(
        featureId: 'writing_assistance',
        featureName: 'Writing Assistance',
        featureNameAr: 'مساعدة الكتابة',
        icon: Icons.edit_outlined,
        color: Colors.teal,
        customSettings: FeatureSettings.getDefaultSettings(FeatureType.writingAssistance),
      ),
      FeatureApiConfig(
        featureId: 'smart_translation',
        featureName: 'Smart Translation',
        featureNameAr: 'الترجمة الذكية',
        icon: Icons.translate_outlined,
        color: Colors.indigo,
        customSettings: FeatureSettings.getDefaultSettings(FeatureType.smartTranslation),
      ),
      FeatureApiConfig(
        featureId: 'image_analysis',
        featureName: 'Image Analysis',
        featureNameAr: 'تحليل الصور',
        icon: Icons.image_search_outlined,
        color: Colors.pink,
        customSettings: FeatureSettings.getDefaultSettings(FeatureType.imageAnalysis),
      ),
      FeatureApiConfig(
        featureId: 'voice_chat',
        featureName: 'Voice Chat',
        featureNameAr: 'الدردشة الصوتية',
        icon: Icons.mic_outlined,
        color: Colors.red,
        customSettings: FeatureSettings.getDefaultSettings(FeatureType.voiceChat),
      ),
      FeatureApiConfig(
        featureId: 'smart_browsing',
        featureName: 'Smart Browsing',
        featureNameAr: 'التصفح الذكي',
        icon: Icons.web_outlined,
        color: Colors.cyan,
        customSettings: FeatureSettings.getDefaultSettings(FeatureType.smartBrowsing),
      ),
      FeatureApiConfig(
        featureId: 'code_generation',
        featureName: 'Code Generation',
        featureNameAr: 'توليد الكود',
        icon: Icons.code_outlined,
        color: Colors.deepPurple,
        customSettings: FeatureSettings.getDefaultSettings(FeatureType.codeGeneration),
      ),
    ];

    for (final config in defaultConfigs) {
      if (!_configs.containsKey(config.featureId)) {
        _configs[config.featureId] = config;
      }
    }

    await _saveConfigs();
  }

  /// حفظ الإعدادات
  static Future<void> _saveConfigs() async {
    try {
      final data = _configs.map((key, value) => MapEntry(key, value.toMap()));
      await StorageService.saveData(_storageKey, data);
    } catch (e) {
      debugPrint('❌ خطأ في حفظ إعدادات APIs: $e');
    }
  }

  /// الحصول على جميع إعدادات الميزات
  static List<FeatureApiConfig> getAllConfigs() {
    return _configs.values.toList();
  }

  /// الحصول على إعدادات ميزة معينة
  static FeatureApiConfig? getConfig(String featureId) {
    return _configs[featureId];
  }

  /// تحديث إعدادات ميزة
  static Future<void> updateConfig(FeatureApiConfig config) async {
    _configs[config.featureId] = config.copyWith(lastUpdated: DateTime.now());
    await _saveConfigs();
    debugPrint('✅ تم تحديث إعدادات ${config.featureNameAr}');
  }

  /// تفعيل/إلغاء تفعيل ميزة
  static Future<void> toggleFeature(String featureId, bool enabled) async {
    final config = _configs[featureId];
    if (config != null) {
      await updateConfig(config.copyWith(isEnabled: enabled));
    }
  }

  /// التحقق من توفر ميزة
  static bool isFeatureAvailable(String featureId) {
    final config = _configs[featureId];
    return config != null && config.isConfigured && config.isEnabled;
  }

  /// الحصول على API key لميزة معينة
  static String? getApiKey(String featureId) {
    final config = _configs[featureId];
    return config?.apiKey;
  }

  /// الحصول على base URL لميزة معينة
  static String? getBaseUrl(String featureId) {
    final config = _configs[featureId];
    return config?.baseUrl;
  }

  /// الحصول على النموذج المحدد لميزة معينة
  static String? getModel(String featureId) {
    final config = _configs[featureId];
    return config?.model;
  }

  /// الحصول على مقدم الخدمة لميزة معينة
  static String? getProvider(String featureId) {
    final config = _configs[featureId];
    return config?.provider;
  }

  /// الحصول على الإعدادات المخصصة لميزة معينة
  static Map<String, dynamic> getCustomSettings(String featureId) {
    final config = _configs[featureId];
    return config?.customSettings ?? {};
  }

  /// تحديث الإعدادات المخصصة لميزة معينة
  static Future<void> updateCustomSettings(String featureId, Map<String, dynamic> settings) async {
    final config = _configs[featureId];
    if (config != null) {
      await updateConfig(config.copyWith(customSettings: settings));
    }
  }

  /// الحصول على إحصائيات الميزات
  static Map<String, int> getFeatureStats() {
    final total = _configs.length;
    final configured = _configs.values.where((c) => c.isConfigured).length;
    final enabled = _configs.values.where((c) => c.isEnabled).length;
    final active = _configs.values.where((c) => c.isConfigured && c.isEnabled).length;

    return {
      'total': total,
      'configured': configured,
      'enabled': enabled,
      'active': active,
    };
  }

  /// إعادة تعيين جميع الإعدادات
  static Future<void> resetAllConfigs() async {
    _configs.clear();
    await _initializeDefaultConfigs();
    debugPrint('✅ تم إعادة تعيين جميع إعدادات APIs');
  }

  /// تصدير الإعدادات
  static Map<String, dynamic> exportConfigs() {
    return _configs.map((key, value) => MapEntry(key, value.toMap()));
  }

  /// استيراد الإعدادات
  static Future<void> importConfigs(Map<String, dynamic> data) async {
    try {
      _configs = data.map((key, value) => 
        MapEntry(key, FeatureApiConfig.fromMap(Map<String, dynamic>.from(value))));
      await _saveConfigs();
      debugPrint('✅ تم استيراد إعدادات APIs بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في استيراد إعدادات APIs: $e');
      throw Exception('فشل في استيراد الإعدادات');
    }
  }
}
