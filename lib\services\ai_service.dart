import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;
import 'package:flutter/foundation.dart';
import '../config/api_config.dart';
import 'error_handler.dart';

// نموذج للرسالة
class AIMessage {
  final String content;
  final String role; // 'user' أو 'assistant' أو 'system'
  final DateTime timestamp;
  final Map<String, dynamic>? metadata;

  AIMessage({
    required this.content,
    required this.role,
    DateTime? timestamp,
    this.metadata,
  }) : timestamp = timestamp ?? DateTime.now();

  Map<String, dynamic> toJson() => {
    'content': content,
    'role': role,
    'timestamp': timestamp.toIso8601String(),
    if (metadata != null) 'metadata': metadata,
  };

  factory AIMessage.fromJson(Map<String, dynamic> json) => AIMessage(
    content: json['content'],
    role: json['role'],
    timestamp: DateTime.parse(json['timestamp']),
    metadata: json['metadata'],
  );
}

// نموذج للإعدادات
class AISettings {
  final double temperature;
  final int maxTokens;
  final double topP;
  final int topK;
  final List<String>? stopSequences;
  final String? systemPrompt;

  const AISettings({
    this.temperature = 0.7,
    this.maxTokens = 2048,
    this.topP = 0.95,
    this.topK = 40,
    this.stopSequences,
    this.systemPrompt,
  });

  Map<String, dynamic> toJson() => {
    'temperature': temperature,
    'maxTokens': maxTokens,
    'topP': topP,
    'topK': topK,
    if (stopSequences != null) 'stopSequences': stopSequences,
    if (systemPrompt != null) 'systemPrompt': systemPrompt,
  };
}

// نموذج للنتيجة
class AIResult {
  final String content;
  final int? tokensUsed;
  final Duration? processingTime;
  final Map<String, dynamic>? metadata;

  AIResult({
    required this.content,
    this.tokensUsed,
    this.processingTime,
    this.metadata,
  });
}

// الخدمة الرئيسية
class AIService {
  // إخفاء API key - يجب نقله إلى متغيرات البيئة في الإنتاج
  static const String _apiKey = 'AIzaSyDEEPSEEK_YOUR_API_KEY_HERE';
  static const String _geminiBaseUrl =
      'https://generativelanguage.googleapis.com/v1beta/models/';
  static const String _defaultModel = 'gemini-pro';

  // ذاكرة تخزين مؤقت للطلبات
  static final Map<String, AIResult> _cache = {};
  static Duration get _cacheExpiration => ApiConfig.cacheExpiration;
  static final Map<String, DateTime> _cacheTimestamps = {};

  // محاولات إعادة الطلب
  static const int _maxRetries = 3;
  static const Duration _retryDelay = Duration(seconds: 2);

  // سجل المحادثات
  static final Map<String, List<AIMessage>> _conversations = {};

  // تلخيص النص مع إعدادات محسنة
  static Future<AIResult> summarizeText(
    String text, {
    AISettings? settings,
    String? language = 'ar',
  }) async {
    final prompt = '''
قم بتلخيص النص التالي بشكل واضح ومختصر باللغة ${language == 'ar' ? 'العربية' : 'الإنجليزية'}.
احتفظ بالنقاط الرئيسية والمعلومات المهمة.

النص:
$text

الملخص:
''';

    return await _generateContent(
      prompt,
      settings: settings ?? const AISettings(temperature: 0.3, maxTokens: 500),
      cacheKey: 'summarize_${text.hashCode}',
    );
  }

  // مساعدة في الكتابة مع أنماط مختلفة
  static Future<AIResult> helpMeWrite(
    String topic, {
    String style = 'professional',
    String? language = 'ar',
    AISettings? settings,
  }) async {
    final stylePrompts = {
      'professional': 'احترافي ورسمي',
      'creative': 'إبداعي وخيالي',
      'academic': 'أكاديمي وعلمي',
      'casual': 'عادي وودود',
      'technical': 'تقني ومفصل',
    };

    final prompt = '''
ساعدني في كتابة محتوى ${stylePrompts[style] ?? style} باللغة ${language == 'ar' ? 'العربية' : 'الإنجليزية'} حول:
$topic

المحتوى يجب أن يكون:
- منظم وواضح
- مناسب للأسلوب المطلوب
- شامل ومفيد

المحتوى:
''';

    return await _generateContent(
      prompt,
      settings: settings ?? const AISettings(temperature: 0.8, maxTokens: 1000),
    );
  }

  // إنشاء خطة مفصلة
  static Future<AIResult> makeAPlan(
    String goal, {
    int? timeframe,
    String? constraints,
    AISettings? settings,
  }) async {
    final prompt = '''
قم بإنشاء خطة تفصيلية لتحقيق الهدف التالي:
$goal

${timeframe != null ? 'الإطار الزمني: $timeframe يوم' : ''}
${constraints != null ? 'القيود: $constraints' : ''}

الخطة يجب أن تتضمن:
1. الأهداف الفرعية
2. الخطوات التفصيلية
3. الجدول الزمني
4. الموارد المطلوبة
5. مؤشرات النجاح

الخطة:
''';

    return await _generateContent(
      prompt,
      settings: settings ?? const AISettings(temperature: 0.5, maxTokens: 1500),
    );
  }

  // تحليل البيانات المتقدم
  static Future<AIResult> analyzeData(
    String data, {
    String? analysisType,
    bool includeVisualization = false,
    AISettings? settings,
  }) async {
    final analysisTypes = {
      'statistical': 'إحصائي',
      'trend': 'اتجاهات',
      'predictive': 'تنبؤي',
      'comparative': 'مقارن',
      'descriptive': 'وصفي',
    };

    final prompt = '''
قم بإجراء تحليل ${analysisTypes[analysisType] ?? 'شامل'} للبيانات التالية:
$data

التحليل يجب أن يتضمن:
1. ملخص البيانات
2. النتائج الرئيسية
3. الأنماط المكتشفة
4. التوصيات
${includeVisualization ? '5. اقتراحات للتصور البصري' : ''}

التحليل:
''';

    return await _generateContent(
      prompt,
      settings: settings ?? const AISettings(temperature: 0.3, maxTokens: 2000),
    );
  }

  // محادثة متقدمة مع سياق
  static Future<AIResult> chat(
    String message, {
    String? conversationId,
    AISettings? settings,
    bool streamResponse = false,
  }) async {
    conversationId ??= DateTime.now().millisecondsSinceEpoch.toString();

    // إضافة الرسالة إلى السجل
    _conversations[conversationId] ??= [];
    _conversations[conversationId]!.add(
      AIMessage(content: message, role: 'user'),
    );

    // بناء السياق من المحادثات السابقة
    final context = _buildConversationContext(conversationId);

    final result = await _generateContent(
      context,
      settings: settings ?? const AISettings(temperature: 0.7),
      stream: streamResponse,
    );

    // إضافة الرد إلى السجل
    _conversations[conversationId]!.add(
      AIMessage(content: result.content, role: 'assistant'),
    );

    return result;
  }

  // إنشاء صورة (محاكاة)
  static Future<AIResult> createImage(
    String description, {
    String size = '1024x1024',
    String style = 'realistic',
  }) async {
    // في الإنتاج، يجب استخدام API لتوليد الصور مثل DALL-E أو Stable Diffusion
    await Future.delayed(const Duration(seconds: 2));

    return AIResult(
      content: 'تم إنشاء الصورة بنجاح: $description',
      metadata: {
        'imageUrl': 'https://placeholder.com/$size',
        'style': style,
        'description': description,
      },
    );
  }

  // تحليل الصور (محاكاة)
  static Future<AIResult> analyzeImage(
    File imageFile, {
    String? analysisType,
  }) async {
    // في الإنتاج، يجب استخدام Vision API
    await Future.delayed(const Duration(seconds: 2));

    return AIResult(
      content: '''
تحليل الصورة:
- النوع: صورة فوتوغرافية
- المحتوى: منظر طبيعي
- الألوان السائدة: أزرق، أخضر، أبيض
- الجودة: عالية الدقة
- العناصر المكتشفة: سماء، أشجار، جبال
''',
      metadata: {
        'fileSize': await imageFile.length(),
        'analysisType': analysisType ?? 'general',
      },
    );
  }

  // الوظيفة الأساسية لتوليد المحتوى
  static Future<AIResult> _generateContent(
    String prompt, {
    AISettings? settings,
    String? cacheKey,
    bool stream = false,
    String model = _defaultModel,
  }) async {
    final stopwatch = Stopwatch()..start();

    // التحقق من الذاكرة المؤقتة
    if (cacheKey != null && _isCacheValid(cacheKey)) {
      return _cache[cacheKey]!;
    }

    // إعدادات افتراضية
    settings ??= const AISettings();

    // محاولة الطلب مع إعادة المحاولة
    for (int attempt = 0; attempt < _maxRetries; attempt++) {
      try {
        final result = await _makeRequest(prompt, settings, model, stream);

        stopwatch.stop();

        final aiResult = AIResult(
          content: result['content'] ?? 'لا يوجد رد',
          tokensUsed: result['tokensUsed'],
          processingTime: stopwatch.elapsed,
          metadata: result['metadata'],
        );

        // حفظ في الذاكرة المؤقتة
        if (cacheKey != null) {
          _cache[cacheKey] = aiResult;
          _cacheTimestamps[cacheKey] = DateTime.now();
        }

        return aiResult;
      } catch (e) {
        if (attempt == _maxRetries - 1) {
          throw AIException(
            'فشل الطلب بعد $_maxRetries محاولات: $e',
            code: 'MAX_RETRIES_EXCEEDED',
          );
        }

        await Future.delayed(_retryDelay * (attempt + 1));
      }
    }

    throw AIException('خطأ غير متوقع', code: 'UNKNOWN_ERROR');
  }

  // إجراء الطلب الفعلي
  static Future<Map<String, dynamic>> _makeRequest(
    String prompt,
    AISettings settings,
    String model,
    bool stream,
  ) async {
    final url = Uri.parse('$_geminiBaseUrl$model:generateContent?key=$_apiKey');

    final body = {
      'contents': [
        {
          'parts': [
            {'text': prompt},
          ],
        },
      ],
      'generationConfig': {
        'temperature': settings.temperature,
        'topK': settings.topK,
        'topP': settings.topP,
        'maxOutputTokens': settings.maxTokens,
        if (settings.stopSequences != null)
          'stopSequences': settings.stopSequences,
      },
    };

    try {
      final response = await http
          .post(
            url,
            headers: {'Content-Type': 'application/json'},
            body: jsonEncode(body),
          )
          .timeout(const Duration(seconds: 30));

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        final content =
            data['candidates']?[0]?['content']?['parts']?[0]?['text'] ?? '';

        return {
          'content': content,
          'tokensUsed': data['usageMetadata']?['totalTokenCount'],
          'metadata': {
            'model': model,
            'finishReason': data['candidates']?[0]?['finishReason'],
          },
        };
      } else {
        final error = jsonDecode(response.body);
        throw AIException(
          error['error']?['message'] ?? 'خطأ في الخادم',
          code: error['error']?['code']?.toString() ?? 'SERVER_ERROR',
          statusCode: response.statusCode,
        );
      }
    } on SocketException {
      throw AIException('لا يوجد اتصال بالإنترنت', code: 'NO_INTERNET');
    } on TimeoutException {
      throw AIException('انتهت مهلة الطلب', code: 'TIMEOUT');
    } on FormatException {
      throw AIException('خطأ في تنسيق البيانات', code: 'FORMAT_ERROR');
    }
  }

  // بناء سياق المحادثة
  static String _buildConversationContext(String conversationId) {
    final messages = _conversations[conversationId] ?? [];
    final recentMessages =
        messages.length > 10
            ? messages.sublist(messages.length - 10)
            : messages;

    final context = StringBuffer();

    for (final message in recentMessages) {
      if (message.role == 'user') {
        context.writeln('المستخدم: ${message.content}');
      } else {
        context.writeln('المساعد: ${message.content}');
      }
      context.writeln();
    }

    return context.toString();
  }

  // التحقق من صلاحية الذاكرة المؤقتة
  static bool _isCacheValid(String key) {
    if (!_cache.containsKey(key)) return false;

    final timestamp = _cacheTimestamps[key];
    if (timestamp == null) return false;

    return DateTime.now().difference(timestamp) < _cacheExpiration;
  }

  // مسح الذاكرة المؤقتة
  static void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
  }

  // مسح محادثة معينة
  static void clearConversation(String conversationId) {
    _conversations.remove(conversationId);
  }

  // الحصول على سجل المحادثة
  static List<AIMessage> getConversationHistory(String conversationId) {
    return List.unmodifiable(_conversations[conversationId] ?? []);
  }
}

// Stream للردود الطويلة (للاستخدام المستقبلي)
class AIStreamResponse {
  final Stream<String> contentStream;
  final Future<int?> totalTokens;

  AIStreamResponse({required this.contentStream, required this.totalTokens});
}
