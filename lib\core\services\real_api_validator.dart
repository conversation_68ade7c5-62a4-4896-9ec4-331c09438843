// import 'dart:convert';  // غير مستخدم
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../security/api_key_manager.dart';
import '../models/api_provider_model.dart';
import 'api_provider_service.dart';

/// خدمة التحقق من صحة APIs الحقيقية
class RealApiValidator {
  static final Dio _dio = Dio();
  static const Duration _timeout = Duration(seconds: 10);

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    _dio.options = BaseOptions(
      connectTimeout: _timeout,
      receiveTimeout: _timeout,
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'DeepSeek-AI-App/1.0',
      },
    );
  }

  /// التحقق من صحة جميع APIs المضافة
  static Future<Map<String, ApiValidationResult>> validateAllApis() async {
    final results = <String, ApiValidationResult>{};
    final providers = ApiProviderService.getAllProviders();

    for (final provider in providers) {
      if (provider.apiKey != null && provider.apiKey!.isNotEmpty) {
        results[provider.id] = await validateProvider(provider);
      } else {
        results[provider.id] = ApiValidationResult(
          isValid: false,
          error: 'لا يوجد مفتاح API',
          capabilities: [],
        );
      }
    }

    return results;
  }

  /// التحقق من صحة مقدم خدمة محدد
  static Future<ApiValidationResult> validateProvider(
    ApiProvider provider,
  ) async {
    if (provider.apiKey == null || provider.apiKey!.isEmpty) {
      return ApiValidationResult(
        isValid: false,
        error: 'لا يوجد مفتاح API',
        capabilities: [],
      );
    }

    try {
      // حماية من الحلقة اللا نهائية
      await Future.delayed(const Duration(milliseconds: 200));

      switch (provider.type) {
        case ApiProviderType.openai:
          return await _validateOpenAI(provider);
        case ApiProviderType.openrouter:
          return await _validateOpenRouter(provider);
        case ApiProviderType.gemini:
          return await _validateGemini(provider);
        case ApiProviderType.anthropic:
          return await _validateAnthropic(provider);
        default:
          return ApiValidationResult(
            isValid: false,
            error: 'نوع مقدم غير مدعوم',
            capabilities: [],
          );
      }
    } catch (e) {
      // تقليل رسائل الخطأ لتجنب الإزعاج
      if (!e.toString().contains('Bad state: No element')) {
        debugPrint('خطأ في اختبار المقدم: $e');
      }
      return ApiValidationResult(
        isValid: false,
        error: 'خطأ في الاتصال: $e',
        capabilities: [],
      );
    }
  }

  /// التحقق من OpenAI
  static Future<ApiValidationResult> _validateOpenAI(
    ApiProvider provider,
  ) async {
    try {
      // اختبار الدردشة
      final chatResponse = await _dio.post(
        '${provider.baseUrl}/chat/completions',
        options: Options(
          headers: {'Authorization': 'Bearer ${provider.apiKey}'},
        ),
        data: {
          'model': 'gpt-3.5-turbo',
          'messages': [
            {'role': 'user', 'content': 'Test message'},
          ],
          'max_tokens': 5,
        },
      );

      final capabilities = <String>['chat', 'text'];

      // اختبار إنشاء الصور
      try {
        await _dio.post(
          '${provider.baseUrl}/images/generations',
          options: Options(
            headers: {'Authorization': 'Bearer ${provider.apiKey}'},
          ),
          data: {
            'model': 'dall-e-3',
            'prompt': 'test image',
            'n': 1,
            'size': '1024x1024',
          },
        );
        capabilities.add('image_generation');
      } catch (e) {
        debugPrint('OpenAI Image generation not available: $e');
      }

      return ApiValidationResult(
        isValid: true,
        capabilities: capabilities,
        modelsCount: chatResponse.data['usage'] != null ? 1 : 0,
        responseTime: DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      return ApiValidationResult(
        isValid: false,
        error: _parseError(e),
        capabilities: [],
      );
    }
  }

  /// التحقق من OpenRouter
  static Future<ApiValidationResult> _validateOpenRouter(
    ApiProvider provider,
  ) async {
    try {
      // اختبار الدردشة
      final chatResponse = await _dio.post(
        '${provider.baseUrl}/chat/completions',
        options: Options(
          headers: {
            'Authorization': 'Bearer ${provider.apiKey}',
            'HTTP-Referer': 'https://deepseek-ai-app.com',
            'X-Title': 'DeepSeek AI App',
          },
        ),
        data: {
          'model': 'openai/gpt-3.5-turbo',
          'messages': [
            {'role': 'user', 'content': 'Test message'},
          ],
          'max_tokens': 5,
        },
      );

      // جلب النماذج المتاحة
      final modelsResponse = await _dio.get(
        '${provider.baseUrl}/models',
        options: Options(
          headers: {'Authorization': 'Bearer ${provider.apiKey}'},
        ),
      );

      final modelsCount = modelsResponse.data['data']?.length ?? 0;

      return ApiValidationResult(
        isValid: true,
        capabilities: ['chat', 'text', 'multiple_models'],
        modelsCount: modelsCount,
        responseTime: DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      return ApiValidationResult(
        isValid: false,
        error: _parseError(e),
        capabilities: [],
      );
    }
  }

  /// التحقق من Gemini
  static Future<ApiValidationResult> _validateGemini(
    ApiProvider provider,
  ) async {
    try {
      // اختبار الدردشة
      final chatResponse = await _dio.post(
        '${provider.baseUrl}/models/gemini-pro:generateContent?key=${provider.apiKey}',
        data: {
          'contents': [
            {
              'parts': [
                {'text': 'Test message'},
              ],
            },
          ],
          'generationConfig': {'maxOutputTokens': 5},
        },
      );

      final capabilities = <String>['chat', 'text'];

      // اختبار Gemini Vision
      try {
        await _dio.post(
          '${provider.baseUrl}/models/gemini-pro-vision:generateContent?key=${provider.apiKey}',
          data: {
            'contents': [
              {
                'parts': [
                  {'text': 'What is this?'},
                  {
                    'inline_data': {
                      'mime_type': 'image/jpeg',
                      'data':
                          'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
                    },
                  },
                ],
              },
            ],
            'generationConfig': {'maxOutputTokens': 5},
          },
        );
        capabilities.add('vision');
      } catch (e) {
        debugPrint('Gemini Vision not available: $e');
      }

      return ApiValidationResult(
        isValid: true,
        capabilities: capabilities,
        modelsCount: 2, // gemini-pro, gemini-pro-vision
        responseTime: DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      return ApiValidationResult(
        isValid: false,
        error: _parseError(e),
        capabilities: [],
      );
    }
  }

  /// التحقق من Anthropic
  static Future<ApiValidationResult> _validateAnthropic(
    ApiProvider provider,
  ) async {
    try {
      final chatResponse = await _dio.post(
        '${provider.baseUrl}/messages',
        options: Options(
          headers: {
            'x-api-key': provider.apiKey!,
            'anthropic-version': '2023-06-01',
          },
        ),
        data: {
          'model': 'claude-3-haiku-20240307',
          'max_tokens': 5,
          'messages': [
            {'role': 'user', 'content': 'Test message'},
          ],
        },
      );

      return ApiValidationResult(
        isValid: true,
        capabilities: ['chat', 'text', 'vision'],
        modelsCount: 3, // claude-3-haiku, claude-3-sonnet, claude-3-opus
        responseTime: DateTime.now().millisecondsSinceEpoch,
      );
    } catch (e) {
      return ApiValidationResult(
        isValid: false,
        error: _parseError(e),
        capabilities: [],
      );
    }
  }

  /// اختبار قدرة محددة لمقدم خدمة
  static Future<bool> testCapability(
    ApiProvider provider,
    String capability,
  ) async {
    try {
      switch (capability) {
        case 'chat':
          return await _testChatCapability(provider);
        case 'image_generation':
          return await _testImageGenerationCapability(provider);
        case 'vision':
          return await _testVisionCapability(provider);
        case 'text_analysis':
          return await _testTextAnalysisCapability(provider);
        default:
          return false;
      }
    } catch (e) {
      debugPrint('خطأ في اختبار القدرة $capability: $e');
      return false;
    }
  }

  /// اختبار قدرة الدردشة
  static Future<bool> _testChatCapability(ApiProvider provider) async {
    try {
      final result = await validateProvider(provider);
      return result.isValid && result.capabilities.contains('chat');
    } catch (e) {
      return false;
    }
  }

  /// اختبار قدرة إنشاء الصور
  static Future<bool> _testImageGenerationCapability(
    ApiProvider provider,
  ) async {
    if (provider.type != ApiProviderType.openai) return false;

    try {
      await _dio.post(
        '${provider.baseUrl}/images/generations',
        options: Options(
          headers: {'Authorization': 'Bearer ${provider.apiKey}'},
        ),
        data: {
          'model': 'dall-e-3',
          'prompt': 'test',
          'n': 1,
          'size': '1024x1024',
        },
      );
      return true;
    } catch (e) {
      return false;
    }
  }

  /// اختبار قدرة الرؤية
  static Future<bool> _testVisionCapability(ApiProvider provider) async {
    try {
      switch (provider.type) {
        case ApiProviderType.gemini:
          await _dio.post(
            '${provider.baseUrl}/models/gemini-pro-vision:generateContent?key=${provider.apiKey}',
            data: {
              'contents': [
                {
                  'parts': [
                    {'text': 'What is this?'},
                    {
                      'inline_data': {
                        'mime_type': 'image/jpeg',
                        'data':
                            'iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mP8/5+hHgAHggJ/PchI7wAAAABJRU5ErkJggg==',
                      },
                    },
                  ],
                },
              ],
            },
          );
          return true;
        case ApiProviderType.anthropic:
          // Claude يدعم الرؤية في النماذج الحديثة
          return true;
        case ApiProviderType.openai:
          // GPT-4 Vision
          return true;
        default:
          return false;
      }
    } catch (e) {
      return false;
    }
  }

  /// اختبار قدرة تحليل النصوص
  static Future<bool> _testTextAnalysisCapability(ApiProvider provider) async {
    // جميع المقدمين يدعمون تحليل النصوص
    return await _testChatCapability(provider);
  }

  /// تحليل رسالة الخطأ
  static String _parseError(dynamic error) {
    if (error is DioException) {
      switch (error.response?.statusCode) {
        case 401:
          return 'مفتاح API غير صحيح';
        case 403:
          return 'ليس لديك صلاحية للوصول';
        case 429:
          return 'تم تجاوز حد الطلبات';
        case 500:
          return 'خطأ في الخادم';
        case 503:
          return 'الخدمة غير متاحة مؤقتاً';
        default:
          return error.response?.data?['error']?['message'] ??
              'خطأ في الاتصال: ${error.response?.statusCode}';
      }
    }
    return error.toString();
  }

  /// الحصول على تقرير شامل عن حالة APIs
  static Future<ApiHealthReport> getHealthReport() async {
    final validationResults = await validateAllApis();
    final providers = ApiProviderService.getAllProviders();

    int totalProviders = providers.length;
    int workingProviders = 0;
    int totalCapabilities = 0;
    int totalModels = 0;
    List<String> errors = [];

    for (final entry in validationResults.entries) {
      final result = entry.value;
      if (result.isValid) {
        workingProviders++;
        totalCapabilities += result.capabilities.length;
        totalModels += result.modelsCount;
      } else {
        errors.add('${entry.key}: ${result.error}');
      }
    }

    return ApiHealthReport(
      totalProviders: totalProviders,
      workingProviders: workingProviders,
      totalCapabilities: totalCapabilities,
      totalModels: totalModels,
      errors: errors,
      lastCheck: DateTime.now(),
      validationResults: validationResults,
    );
  }

  /// فحص دوري لحالة APIs
  static Future<void> performPeriodicCheck() async {
    try {
      final report = await getHealthReport();
      debugPrint('📊 تقرير حالة APIs:');
      debugPrint(
        '   المقدمين العاملين: ${report.workingProviders}/${report.totalProviders}',
      );
      debugPrint('   إجمالي القدرات: ${report.totalCapabilities}');
      debugPrint('   إجمالي النماذج: ${report.totalModels}');

      if (report.errors.isNotEmpty) {
        debugPrint('❌ أخطاء:');
        for (final error in report.errors) {
          debugPrint('   - $error');
        }
      }
    } catch (e) {
      debugPrint('خطأ في الفحص الدوري: $e');
    }
  }
}

/// نتيجة التحقق من صحة API
class ApiValidationResult {
  final bool isValid;
  final String? error;
  final List<String> capabilities;
  final int modelsCount;
  final int? responseTime;

  ApiValidationResult({
    required this.isValid,
    this.error,
    required this.capabilities,
    this.modelsCount = 0,
    this.responseTime,
  });

  Map<String, dynamic> toJson() => {
    'isValid': isValid,
    'error': error,
    'capabilities': capabilities,
    'modelsCount': modelsCount,
    'responseTime': responseTime,
  };

  factory ApiValidationResult.fromJson(Map<String, dynamic> json) =>
      ApiValidationResult(
        isValid: json['isValid'] ?? false,
        error: json['error'],
        capabilities: List<String>.from(json['capabilities'] ?? []),
        modelsCount: json['modelsCount'] ?? 0,
        responseTime: json['responseTime'],
      );
}

/// تقرير شامل عن حالة APIs
class ApiHealthReport {
  final int totalProviders;
  final int workingProviders;
  final int totalCapabilities;
  final int totalModels;
  final List<String> errors;
  final DateTime lastCheck;
  final Map<String, ApiValidationResult> validationResults;

  ApiHealthReport({
    required this.totalProviders,
    required this.workingProviders,
    required this.totalCapabilities,
    required this.totalModels,
    required this.errors,
    required this.lastCheck,
    required this.validationResults,
  });

  double get healthScore {
    if (totalProviders == 0) return 0.0;
    return workingProviders / totalProviders;
  }

  bool get isHealthy => healthScore >= 0.5;

  Map<String, dynamic> toJson() => {
    'totalProviders': totalProviders,
    'workingProviders': workingProviders,
    'totalCapabilities': totalCapabilities,
    'totalModels': totalModels,
    'errors': errors,
    'lastCheck': lastCheck.toIso8601String(),
    'healthScore': healthScore,
    'isHealthy': isHealthy,
  };
}
