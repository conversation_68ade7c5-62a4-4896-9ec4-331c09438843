import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:url_launcher/url_launcher.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../core/services/enhanced_ai_service.dart';
import '../widgets/modern_ui_components.dart';

class SmartBrowsingScreen extends StatefulWidget {
  const SmartBrowsingScreen({super.key});

  @override
  State<SmartBrowsingScreen> createState() => _SmartBrowsingScreenState();
}

class _SmartBrowsingScreenState extends State<SmartBrowsingScreen>
    with TickerProviderStateMixin {
  final TextEditingController _queryController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  List<Map<String, dynamic>> _searchResults = [];
  String _summary = '';
  bool _isLoading = false;
  String _searchMode = 'شامل';
  bool _includeSummary = true;
  int _maxResults = 10;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<String> _searchModes = [
    'شامل',
    'أكاديمي',
    'أخبار',
    'تقني',
    'تجاري',
    'ترفيهي',
  ];

  final List<Map<String, dynamic>> _quickSearches = [
    {
      'icon': Icons.trending_up,
      'title': 'الأخبار الرائجة',
      'query': 'آخر الأخبار والأحداث المهمة اليوم',
      'color': Colors.red,
    },
    {
      'icon': Icons.science_outlined,
      'title': 'اكتشافات علمية',
      'query': 'أحدث الاكتشافات العلمية والتقنية',
      'color': Colors.blue,
    },
    {
      'icon': Icons.business_outlined,
      'title': 'أخبار الأعمال',
      'query': 'آخر أخبار الاقتصاد والأعمال',
      'color': Colors.green,
    },
    {
      'icon': Icons.sports_soccer,
      'title': 'الرياضة',
      'query': 'آخر أخبار الرياضة والمباريات',
      'color': Colors.orange,
    },
    {
      'icon': Icons.computer_outlined,
      'title': 'التكنولوجيا',
      'query': 'أحدث التطورات التقنية والبرمجية',
      'color': Colors.purple,
    },
    {
      'icon': Icons.health_and_safety,
      'title': 'الصحة',
      'query': 'نصائح صحية وآخر الأبحاث الطبية',
      'color': Colors.teal,
    },
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _queryController.dispose();
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _performSearch() async {
    if (_queryController.text.trim().isEmpty) {
      _showSnackBar('يرجى إدخال استعلام البحث', isError: true);
      return;
    }

    setState(() {
      _isLoading = true;
      _searchResults.clear();
      _summary = '';
    });

    try {
      final result = await EnhancedAIService.smartBrowsing(
        query: _queryController.text.trim(),
        mode: _searchMode.toLowerCase(),
        maxResults: _maxResults,
        includeSummary: _includeSummary,
      );

      setState(() {
        _searchResults = result['results'] ?? [];
        _summary = result['summary'] ?? '';
        _isLoading = false;
      });

      _showSnackBar('تم العثور على ${_searchResults.length} نتيجة');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showSnackBar('حدث خطأ أثناء البحث: ${e.toString()}', isError: true);
    }
  }

  void _useQuickSearch(String query) {
    setState(() {
      _queryController.text = query;
    });
    _performSearch();
  }

  Future<void> _openUrl(String url) async {
    try {
      final uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        _showSnackBar('لا يمكن فتح الرابط', isError: true);
      }
    } catch (e) {
      _showSnackBar('خطأ في فتح الرابط: ${e.toString()}', isError: true);
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : AppColors.primaryPurple,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.darkPurple.withValues(alpha: 0.9),
              AppColors.background,
              AppColors.primaryPurple.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                _buildHeader(),
                _buildQuickSearches(),
                _buildSearchBar(),
                _buildSettingsSection(),
                Expanded(
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        if (_summary.isNotEmpty) ...[
                          _buildSummarySection(),
                          const SizedBox(height: 20),
                        ],
                        if (_searchResults.isNotEmpty || _isLoading) ...[
                          _buildResultsSection(),
                        ],
                        if (_searchResults.isEmpty && !_isLoading && _queryController.text.isNotEmpty) ...[
                          _buildNoResultsSection(),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.electricBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => Navigator.pop(context),
                borderRadius: BorderRadius.circular(12),
                child: const Padding(
                  padding: EdgeInsets.all(12),
                  child: Icon(Icons.arrow_back, color: Colors.white, size: 20),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.web_outlined,
                      color: AppColors.electricBlue,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'التصفح الذكي',
                      style: AppTextStyles.heading2.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Text(
                  'ابحث واستكشف الويب بذكاء',
                  style: AppTextStyles.body.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickSearches() {
    return Container(
      height: 120,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'بحث سريع',
            style: AppTextStyles.body.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _quickSearches.length,
              itemBuilder: (context, index) {
                final search = _quickSearches[index];
                return Container(
                  width: 140,
                  margin: const EdgeInsets.only(right: 12),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => _useQuickSearch(search['query']),
                      borderRadius: BorderRadius.circular(16),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              search['color'].withValues(alpha: 0.2),
                              search['color'].withValues(alpha: 0.1),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: search['color'].withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              search['icon'],
                              color: search['color'],
                              size: 24,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              search['title'],
                              style: AppTextStyles.caption.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.electricBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: TextField(
              controller: _queryController,
              style: AppTextStyles.body.copyWith(color: Colors.white),
              decoration: InputDecoration(
                hintText: 'ابحث عن أي شيء...',
                hintStyle: AppTextStyles.body.copyWith(
                  color: AppColors.textSecondary,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
                prefixIcon: Icon(
                  Icons.search,
                  color: AppColors.electricBlue,
                ),
              ),
              onSubmitted: (_) => _performSearch(),
            ),
          ),
          Container(
            margin: const EdgeInsets.all(8),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: _isLoading ? null : _performSearch,
                borderRadius: BorderRadius.circular(12),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.primaryPurple,
                        AppColors.electricBlue,
                      ],
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: _isLoading
                      ? SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Icon(
                          Icons.search,
                          color: Colors.white,
                          size: 20,
                        ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppColors.electricBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: ExpansionTile(
        title: Text(
          'إعدادات البحث',
          style: AppTextStyles.body.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        iconColor: AppColors.electricBlue,
        collapsedIconColor: AppColors.electricBlue,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              children: [
                // نمط البحث
                Row(
                  children: [
                    Text(
                      'نمط البحث:',
                      style: AppTextStyles.body.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                        decoration: BoxDecoration(
                          color: AppColors.darkGrey.withValues(alpha: 0.5),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: DropdownButtonHideUnderline(
                          child: DropdownButton<String>(
                            value: _searchMode,
                            onChanged: (value) {
                              if (value != null) {
                                setState(() {
                                  _searchMode = value;
                                });
                              }
                            },
                            dropdownColor: AppColors.darkGrey,
                            style: AppTextStyles.body.copyWith(color: Colors.white),
                            items: _searchModes.map((mode) {
                              return DropdownMenuItem<String>(
                                value: mode,
                                child: Text(mode),
                              );
                            }).toList(),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),

                // عدد النتائج
                Row(
                  children: [
                    Text(
                      'عدد النتائج: $_maxResults',
                      style: AppTextStyles.body.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    Expanded(
                      child: Slider(
                        value: _maxResults.toDouble(),
                        min: 5,
                        max: 20,
                        divisions: 3,
                        activeColor: AppColors.electricBlue,
                        inactiveColor: AppColors.darkGrey,
                        onChanged: (value) {
                          setState(() {
                            _maxResults = value.round();
                          });
                        },
                      ),
                    ),
                  ],
                ),

                // تضمين الملخص
                Row(
                  children: [
                    Text(
                      'تضمين ملخص ذكي',
                      style: AppTextStyles.body.copyWith(
                        color: AppColors.textSecondary,
                      ),
                    ),
                    const Spacer(),
                    Switch(
                      value: _includeSummary,
                      onChanged: (value) {
                        setState(() {
                          _includeSummary = value;
                        });
                      },
                      activeColor: AppColors.electricBlue,
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
