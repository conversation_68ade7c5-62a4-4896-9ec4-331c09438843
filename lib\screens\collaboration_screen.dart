import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/app_colors.dart';
import '../widgets/modern_ui_components.dart';
import '../core/collaboration/user_management_service.dart';
import '../core/collaboration/workspace_service.dart';
import '../core/collaboration/team_chat_service.dart';

/// شاشة التعاون الجماعي
class CollaborationScreen extends ConsumerStatefulWidget {
  const CollaborationScreen({super.key});

  @override
  ConsumerState<CollaborationScreen> createState() => _CollaborationScreenState();
}

class _CollaborationScreenState extends ConsumerState<CollaborationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeServices();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await UserManagementService.initialize();
      await WorkspaceService.initialize();
      await TeamChatService.initialize();
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمات التعاون: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.darkGrey,
        title: Text(
          'التعاون الجماعي',
          style: TextStyle(color: AppColors.white),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primaryPurple,
          unselectedLabelColor: AppColors.lightGrey,
          indicatorColor: AppColors.primaryPurple,
          tabs: const [
            Tab(text: 'الفرق', icon: Icon(Icons.groups)),
            Tab(text: 'مساحات العمل', icon: Icon(Icons.workspaces)),
            Tab(text: 'الدردشة', icon: Icon(Icons.chat)),
            Tab(text: 'المستخدمين', icon: Icon(Icons.people)),
          ],
        ),
      ),
      body: _isLoading
          ? Center(child: ModernUIComponents.modernLoadingIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildTeamsTab(),
                _buildWorkspacesTab(),
                _buildChatTab(),
                _buildUsersTab(),
              ],
            ),
    );
  }

  Widget _buildTeamsTab() {
    final currentUser = UserManagementService.currentUser;
    final userTeams = currentUser != null 
        ? UserManagementService.getUserTeams(currentUser.id)
        : <Team>[];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTeamsHeader(),
          const SizedBox(height: 24),
          if (userTeams.isEmpty)
            _buildEmptyTeamsState()
          else
            _buildTeamsList(userTeams),
        ],
      ),
    );
  }

  Widget _buildTeamsHeader() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'فرق العمل',
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'تعاون مع فريقك في المشاريع',
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          ModernUIComponents.modernButton(
            text: 'إنشاء فريق',
            onPressed: _showCreateTeamDialog,
            icon: Icons.add,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyTeamsState() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(40),
      child: Column(
        children: [
          Icon(
            Icons.groups_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد فرق بعد',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بإنشاء فريق جديد للتعاون',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ModernUIComponents.modernButton(
            text: 'إنشاء فريق جديد',
            onPressed: _showCreateTeamDialog,
            icon: Icons.add,
          ),
        ],
      ),
    );
  }

  Widget _buildTeamsList(List<Team> teams) {
    return Column(
      children: teams.map((team) => _buildTeamCard(team)).toList(),
    );
  }

  Widget _buildTeamCard(Team team) {
    final members = UserManagementService.getTeamMembers(team.id);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: ModernUIComponents.glassCard(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: AppColors.primaryGradient,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.groups,
                    color: AppColors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        team.name,
                        style: TextStyle(
                          color: AppColors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        team.description,
                        style: TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 12,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.more_vert, color: AppColors.primaryPurple),
                  onPressed: () => _showTeamOptions(team),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Icon(Icons.people, color: AppColors.primaryPurple, size: 16),
                const SizedBox(width: 8),
                Text(
                  '${members.length} أعضاء',
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 12,
                  ),
                ),
                const Spacer(),
                Text(
                  'تم الإنشاء ${_formatDate(team.createdAt)}',
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkspacesTab() {
    final currentUser = UserManagementService.currentUser;
    final userWorkspaces = currentUser != null 
        ? WorkspaceService.getUserWorkspaces(currentUser.id)
        : <Workspace>[];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildWorkspacesHeader(),
          const SizedBox(height: 24),
          if (userWorkspaces.isEmpty)
            _buildEmptyWorkspacesState()
          else
            _buildWorkspacesList(userWorkspaces),
        ],
      ),
    );
  }

  Widget _buildWorkspacesHeader() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مساحات العمل',
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'نظم مشاريعك ومستنداتك',
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          ModernUIComponents.modernButton(
            text: 'إنشاء مساحة',
            onPressed: _showCreateWorkspaceDialog,
            icon: Icons.add,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyWorkspacesState() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(40),
      child: Column(
        children: [
          Icon(
            Icons.workspaces_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد مساحات عمل',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'أنشئ مساحة عمل لتنظيم مشاريعك',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ModernUIComponents.modernButton(
            text: 'إنشاء مساحة عمل',
            onPressed: _showCreateWorkspaceDialog,
            icon: Icons.add,
          ),
        ],
      ),
    );
  }

  Widget _buildWorkspacesList(List<Workspace> workspaces) {
    return Column(
      children: workspaces.map((workspace) => _buildWorkspaceCard(workspace)).toList(),
    );
  }

  Widget _buildWorkspaceCard(Workspace workspace) {
    final projects = WorkspaceService.getWorkspaceProjects(workspace.id);
    final documents = WorkspaceService.getWorkspaceDocuments(workspace.id);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: ModernUIComponents.glassCard(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: AppColors.glowGradient,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.workspaces,
                    color: AppColors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        workspace.name,
                        style: TextStyle(
                          color: AppColors.white,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        workspace.description,
                        style: TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 12,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
                IconButton(
                  icon: Icon(Icons.arrow_forward_ios, color: AppColors.primaryPurple),
                  onPressed: () => _openWorkspace(workspace),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                _buildWorkspaceStatItem(
                  Icons.folder,
                  '${projects.length} مشروع',
                ),
                const SizedBox(width: 24),
                _buildWorkspaceStatItem(
                  Icons.description,
                  '${documents.length} مستند',
                ),
                const SizedBox(width: 24),
                _buildWorkspaceStatItem(
                  Icons.people,
                  '${workspace.memberIds.length} عضو',
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWorkspaceStatItem(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, color: AppColors.primaryPurple, size: 16),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Widget _buildChatTab() {
    final channels = TeamChatService.allChannels;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildChatHeader(),
          const SizedBox(height: 24),
          if (channels.isEmpty)
            _buildEmptyChatState()
          else
            _buildChannelsList(channels),
        ],
      ),
    );
  }

  Widget _buildChatHeader() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'قنوات الدردشة',
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'تواصل مع فريقك في الوقت الفعلي',
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          ModernUIComponents.modernButton(
            text: 'إنشاء قناة',
            onPressed: _showCreateChannelDialog,
            icon: Icons.add,
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyChatState() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(40),
      child: Column(
        children: [
          Icon(
            Icons.chat_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد قنوات دردشة',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'أنشئ قناة للتواصل مع فريقك',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          ModernUIComponents.modernButton(
            text: 'إنشاء قناة دردشة',
            onPressed: _showCreateChannelDialog,
            icon: Icons.add,
          ),
        ],
      ),
    );
  }

  Widget _buildChannelsList(List<ChatChannel> channels) {
    return Column(
      children: channels.map((channel) => _buildChannelCard(channel)).toList(),
    );
  }

  Widget _buildChannelCard(ChatChannel channel) {
    final messages = TeamChatService.getChannelMessages(channel.id, limit: 1);
    final lastMessage = messages.isNotEmpty ? messages.first : null;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: ModernUIComponents.glassCard(
        padding: const EdgeInsets.all(20),
        child: InkWell(
          onTap: () => _openChannel(channel),
          borderRadius: BorderRadius.circular(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    width: 40,
                    height: 40,
                    decoration: BoxDecoration(
                      color: channel.type == ChannelType.private 
                          ? AppColors.warning.withValues(alpha: 0.2)
                          : AppColors.primaryPurple.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(10),
                    ),
                    child: Icon(
                      channel.type == ChannelType.private 
                          ? Icons.lock 
                          : Icons.tag,
                      color: channel.type == ChannelType.private 
                          ? AppColors.warning 
                          : AppColors.primaryPurple,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          channel.name,
                          style: TextStyle(
                            color: AppColors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (lastMessage != null) ...[
                          const SizedBox(height: 4),
                          Text(
                            lastMessage.content,
                            style: TextStyle(
                              color: AppColors.textSecondary,
                              fontSize: 12,
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ],
                    ),
                  ),
                  Text(
                    '${channel.memberIds.length} عضو',
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildUsersTab() {
    final users = UserManagementService.allUsers;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildUsersHeader(),
          const SizedBox(height: 24),
          _buildUsersList(users),
        ],
      ),
    );
  }

  Widget _buildUsersHeader() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المستخدمين',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'إدارة أعضاء الفريق والصلاحيات',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsersList(List<CollaborationUser> users) {
    return Column(
      children: users.map((user) => _buildUserCard(user)).toList(),
    );
  }

  Widget _buildUserCard(CollaborationUser user) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: ModernUIComponents.glassCard(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            CircleAvatar(
              radius: 20,
              backgroundColor: AppColors.primaryPurple,
              child: Text(
                user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                style: TextStyle(
                  color: AppColors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user.name,
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    user.email,
                    style: TextStyle(
                      color: AppColors.textSecondary,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              width: 8,
              height: 8,
              decoration: BoxDecoration(
                color: user.isOnline ? AppColors.success : AppColors.lightGrey,
                shape: BoxShape.circle,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              user.isOnline ? 'متصل' : 'غير متصل',
              style: TextStyle(
                color: user.isOnline ? AppColors.success : AppColors.textSecondary,
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays > 0) {
      return '${difference.inDays} يوم';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} ساعة';
    } else {
      return '${difference.inMinutes} دقيقة';
    }
  }

  void _showCreateTeamDialog() {
    // TODO: إظهار حوار إنشاء فريق
  }

  void _showCreateWorkspaceDialog() {
    // TODO: إظهار حوار إنشاء مساحة عمل
  }

  void _showCreateChannelDialog() {
    // TODO: إظهار حوار إنشاء قناة
  }

  void _showTeamOptions(Team team) {
    // TODO: إظهار خيارات الفريق
  }

  void _openWorkspace(Workspace workspace) {
    // TODO: فتح مساحة العمل
  }

  void _openChannel(ChatChannel channel) {
    // TODO: فتح قناة الدردشة
  }
}
