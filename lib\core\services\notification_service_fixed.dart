import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

/// خدمة الإشعارات المبسطة والمصححة
class NotificationServiceFixed {
  static bool _isInitialized = false;

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تهيئة أساسية للإشعارات
      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة الإشعارات المبسطة بنجاح');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الإشعارات: $e');
    }
  }

  /// إرسال إشعار بسيط (محاكاة)
  static Future<void> showSimpleNotification({
    required int id,
    required String title,
    required String body,
    String? payload,
  }) async {
    if (!_isInitialized) await initialize();

    // محاكاة إرسال الإشعار
    debugPrint('📱 إشعار: $title - $body');
    
    // في التطبيق الحقيقي، يمكن استخدام flutter_local_notifications
    // أو إظهار SnackBar كبديل مؤقت
  }

  /// إشعار نجاح العملية
  static Future<void> showSuccessNotification(String message) async {
    await showSimpleNotification(
      id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title: '✅ نجحت العملية',
      body: message,
    );
  }

  /// إشعار خطأ
  static Future<void> showErrorNotification(String message) async {
    await showSimpleNotification(
      id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title: '❌ حدث خطأ',
      body: message,
    );
  }

  /// إشعار معلومات
  static Future<void> showInfoNotification(String message) async {
    await showSimpleNotification(
      id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title: 'ℹ️ معلومات',
      body: message,
    );
  }

  /// إشعار تحذير
  static Future<void> showWarningNotification(String message) async {
    await showSimpleNotification(
      id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title: '⚠️ تحذير',
      body: message,
    );
  }

  /// إشعار تقدم العملية
  static Future<void> showProgressNotification({
    required int id,
    required String title,
    required int progress,
    required int maxProgress,
    String? body,
  }) async {
    if (!_isInitialized) await initialize();

    final percentage = ((progress / maxProgress) * 100).round();
    debugPrint('📊 تقدم: $title - $percentage%');
  }

  /// إلغاء إشعار محدد
  static Future<void> cancelNotification(int id) async {
    debugPrint('🚫 تم إلغاء الإشعار: $id');
  }

  /// إلغاء جميع الإشعارات
  static Future<void> cancelAllNotifications() async {
    debugPrint('🚫 تم إلغاء جميع الإشعارات');
  }

  /// التحقق من حالة التهيئة
  static bool get isInitialized => _isInitialized;

  /// إشعارات خاصة بالذكاء الاصطناعي
  static Future<void> showAINotification({
    required String title,
    required String message,
    AINotificationType type = AINotificationType.info,
  }) async {
    String emoji;
    switch (type) {
      case AINotificationType.imageGenerated:
        emoji = '🎨';
        break;
      case AINotificationType.textSummarized:
        emoji = '📝';
        break;
      case AINotificationType.dataAnalyzed:
        emoji = '📊';
        break;
      case AINotificationType.planCreated:
        emoji = '📅';
        break;
      case AINotificationType.writingAssisted:
        emoji = '✍️';
        break;
      case AINotificationType.chatResponse:
        emoji = '💬';
        break;
      case AINotificationType.error:
        emoji = '❌';
        break;
      default:
        emoji = 'ℹ️';
    }

    await showSimpleNotification(
      id: DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title: '$emoji $title',
      body: message,
    );
  }

  /// عرض إشعار في الواجهة (كبديل مؤقت)
  static void showInAppNotification(
    BuildContext context, {
    required String title,
    required String message,
    NotificationLevel level = NotificationLevel.info,
  }) {
    Color backgroundColor;
    IconData icon;

    switch (level) {
      case NotificationLevel.success:
        backgroundColor = Colors.green;
        icon = Icons.check_circle;
        break;
      case NotificationLevel.error:
        backgroundColor = Colors.red;
        icon = Icons.error;
        break;
      case NotificationLevel.warning:
        backgroundColor = Colors.orange;
        icon = Icons.warning;
        break;
      default:
        backgroundColor = Colors.blue;
        icon = Icons.info;
    }

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(icon, color: Colors.white),
            const SizedBox(width: 12),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  Text(
                    message,
                    style: const TextStyle(color: Colors.white),
                  ),
                ],
              ),
            ),
          ],
        ),
        backgroundColor: backgroundColor,
        duration: const Duration(seconds: 4),
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
      ),
    );
  }
}

/// أنواع إشعارات الذكاء الاصطناعي
enum AINotificationType {
  info,
  imageGenerated,
  textSummarized,
  dataAnalyzed,
  planCreated,
  writingAssisted,
  chatResponse,
  error,
}

/// مستويات الإشعارات
enum NotificationLevel {
  info,
  success,
  warning,
  error,
}

/// نموذج الإشعار
class NotificationModel {
  final int id;
  final String title;
  final String body;
  final String? payload;
  final DateTime timestamp;
  final AINotificationType type;

  NotificationModel({
    required this.id,
    required this.title,
    required this.body,
    this.payload,
    required this.timestamp,
    required this.type,
  });

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'payload': payload,
      'timestamp': timestamp.toIso8601String(),
      'type': type.toString(),
    };
  }

  factory NotificationModel.fromJson(Map<String, dynamic> json) {
    return NotificationModel(
      id: json['id'],
      title: json['title'],
      body: json['body'],
      payload: json['payload'],
      timestamp: DateTime.parse(json['timestamp']),
      type: AINotificationType.values.firstWhere(
        (e) => e.toString() == json['type'],
        orElse: () => AINotificationType.info,
      ),
    );
  }
}
