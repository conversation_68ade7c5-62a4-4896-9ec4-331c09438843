import 'package:flutter/foundation.dart';
import '../storage/storage_service.dart';
import 'ai_analytics_engine.dart';
import 'analytics_models.dart';

/// خدمة لوحات المعلومات التفاعلية
class DashboardService {
  static const String _dashboardsKey = 'analytics_dashboards';
  static const String _widgetsKey = 'dashboard_widgets';
  static const String _layoutsKey = 'dashboard_layouts';
  
  static bool _isInitialized = false;
  static Map<String, AnalyticsDashboard> _dashboards = {};
  static Map<String, DashboardWidget> _widgets = {};
  static Map<String, DashboardLayout> _layouts = {};

  /// تهيئة خدمة لوحات المعلومات
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadDashboards();
      await _loadWidgets();
      await _loadLayouts();
      
      // إنشاء لوحات افتراضية إذا لم توجد
      if (_dashboards.isEmpty) {
        await _createDefaultDashboards();
      }
      
      _isInitialized = true;
      debugPrint('📊 تم تهيئة خدمة لوحات المعلومات');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة لوحات المعلومات: $e');
    }
  }

  /// إنشاء لوحة معلومات جديدة
  static Future<AnalyticsDashboard> createDashboard({
    required String name,
    required String description,
    DashboardType type = DashboardType.custom,
    List<String>? widgetIds,
  }) async {
    final dashboard = AnalyticsDashboard(
      id: 'dash_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      description: description,
      type: type,
      widgetIds: widgetIds ?? [],
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _dashboards[dashboard.id] = dashboard;
    await _saveDashboards();
    
    debugPrint('✅ تم إنشاء لوحة معلومات: $name');
    return dashboard;
  }

  /// إنشاء ويدجت جديد
  static Future<DashboardWidget> createWidget({
    required String title,
    required WidgetType type,
    required Map<String, dynamic> config,
    WidgetSize size = WidgetSize.medium,
  }) async {
    final widget = DashboardWidget(
      id: 'widget_${DateTime.now().millisecondsSinceEpoch}',
      title: title,
      type: type,
      size: size,
      config: config,
      data: {},
      isVisible: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _widgets[widget.id] = widget;
    await _saveWidgets();
    
    // تحديث بيانات الويدجت
    await updateWidgetData(widget.id);
    
    debugPrint('✅ تم إنشاء ويدجت: $title');
    return widget;
  }

  /// تحديث بيانات ويدجت
  static Future<bool> updateWidgetData(String widgetId) async {
    try {
      final widget = _widgets[widgetId];
      if (widget == null) return false;

      Map<String, dynamic> newData = {};

      switch (widget.type) {
        case WidgetType.chart:
          newData = await _generateChartData(widget);
          break;
        case WidgetType.metric:
          newData = await _generateMetricData(widget);
          break;
        case WidgetType.table:
          newData = await _generateTableData(widget);
          break;
        case WidgetType.insight:
          newData = await _generateInsightData(widget);
          break;
        case WidgetType.prediction:
          newData = await _generatePredictionData(widget);
          break;
        case WidgetType.heatmap:
          newData = await _generateHeatmapData(widget);
          break;
      }

      final updatedWidget = DashboardWidget(
        id: widget.id,
        title: widget.title,
        type: widget.type,
        size: widget.size,
        config: widget.config,
        data: newData,
        isVisible: widget.isVisible,
        createdAt: widget.createdAt,
        updatedAt: DateTime.now(),
      );

      _widgets[widgetId] = updatedWidget;
      await _saveWidgets();
      
      debugPrint('🔄 تم تحديث بيانات الويدجت: ${widget.title}');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في تحديث بيانات الويدجت: $e');
      return false;
    }
  }

  /// الحصول على لوحة معلومات
  static AnalyticsDashboard? getDashboard(String dashboardId) {
    return _dashboards[dashboardId];
  }

  /// الحصول على جميع لوحات المعلومات
  static List<AnalyticsDashboard> getAllDashboards() {
    return _dashboards.values.toList();
  }

  /// الحصول على ويدجتات لوحة معلومات
  static List<DashboardWidget> getDashboardWidgets(String dashboardId) {
    final dashboard = _dashboards[dashboardId];
    if (dashboard == null) return [];

    return dashboard.widgetIds
        .map((id) => _widgets[id])
        .where((widget) => widget != null)
        .cast<DashboardWidget>()
        .toList();
  }

  /// إضافة ويدجت للوحة معلومات
  static Future<bool> addWidgetToDashboard(String dashboardId, String widgetId) async {
    try {
      final dashboard = _dashboards[dashboardId];
      if (dashboard == null) return false;

      if (!dashboard.widgetIds.contains(widgetId)) {
        dashboard.widgetIds.add(widgetId);
        
        final updatedDashboard = AnalyticsDashboard(
          id: dashboard.id,
          name: dashboard.name,
          description: dashboard.description,
          type: dashboard.type,
          widgetIds: dashboard.widgetIds,
          isActive: dashboard.isActive,
          createdAt: dashboard.createdAt,
          updatedAt: DateTime.now(),
        );

        _dashboards[dashboardId] = updatedDashboard;
        await _saveDashboards();
        
        debugPrint('✅ تم إضافة ويدجت للوحة المعلومات');
        return true;
      }
      return true;
    } catch (e) {
      debugPrint('❌ فشل في إضافة ويدجت للوحة المعلومات: $e');
      return false;
    }
  }

  /// تحديث جميع الويدجتات
  static Future<void> refreshAllWidgets() async {
    for (final widgetId in _widgets.keys) {
      await updateWidgetData(widgetId);
    }
    debugPrint('🔄 تم تحديث جميع الويدجتات');
  }

  /// إنشاء تقرير من لوحة المعلومات
  static Future<DashboardReport> generateReport(String dashboardId) async {
    final dashboard = _dashboards[dashboardId];
    if (dashboard == null) {
      throw Exception('لوحة المعلومات غير موجودة');
    }

    final widgets = getDashboardWidgets(dashboardId);
    final summary = AIAnalyticsEngine.getAnalyticsSummary();
    
    return DashboardReport(
      id: 'report_${DateTime.now().millisecondsSinceEpoch}',
      dashboardId: dashboardId,
      dashboardName: dashboard.name,
      widgetCount: widgets.length,
      summary: summary,
      insights: AIAnalyticsEngine.insights.take(5).toList(),
      predictions: AIAnalyticsEngine.predictions.take(3).toList(),
      generatedAt: DateTime.now(),
    );
  }

  // Private methods for data generation

  /// توليد بيانات الرسم البياني
  static Future<Map<String, dynamic>> _generateChartData(DashboardWidget widget) async {
    final chartType = widget.config['chartType'] ?? 'line';
    final dataPoints = <Map<String, dynamic>>[];
    
    // محاكاة بيانات الرسم البياني
    for (int i = 0; i < 7; i++) {
      dataPoints.add({
        'x': DateTime.now().subtract(Duration(days: 6 - i)).millisecondsSinceEpoch,
        'y': (50 + (i * 10) + (DateTime.now().millisecond % 20)).toDouble(),
      });
    }
    
    return {
      'chartType': chartType,
      'dataPoints': dataPoints,
      'title': widget.title,
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  /// توليد بيانات المقياس
  static Future<Map<String, dynamic>> _generateMetricData(DashboardWidget widget) async {
    final metricType = widget.config['metricType'] ?? 'count';
    final summary = AIAnalyticsEngine.getAnalyticsSummary();
    
    double value = 0;
    String unit = '';
    
    switch (metricType) {
      case 'events':
        value = summary.totalEvents.toDouble();
        unit = 'حدث';
        break;
      case 'insights':
        value = summary.totalInsights.toDouble();
        unit = 'رؤية';
        break;
      case 'predictions':
        value = summary.totalPredictions.toDouble();
        unit = 'توقع';
        break;
      default:
        value = 100 + (DateTime.now().millisecond % 50);
        unit = 'وحدة';
    }
    
    return {
      'value': value,
      'unit': unit,
      'change': (DateTime.now().millisecond % 20) - 10, // تغيير عشوائي
      'trend': value > 50 ? 'up' : 'down',
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  /// توليد بيانات الجدول
  static Future<Map<String, dynamic>> _generateTableData(DashboardWidget widget) async {
    final headers = ['الاسم', 'القيمة', 'التغيير', 'الحالة'];
    final rows = <List<String>>[];
    
    // محاكاة بيانات الجدول
    for (int i = 0; i < 5; i++) {
      rows.add([
        'عنصر ${i + 1}',
        '${100 + i * 20}',
        '${(i % 2 == 0 ? '+' : '-')}${i + 5}%',
        i % 2 == 0 ? 'نشط' : 'غير نشط',
      ]);
    }
    
    return {
      'headers': headers,
      'rows': rows,
      'totalRows': rows.length,
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  /// توليد بيانات الرؤى
  static Future<Map<String, dynamic>> _generateInsightData(DashboardWidget widget) async {
    final insights = AIAnalyticsEngine.insights.take(3).toList();
    
    return {
      'insights': insights.map((insight) => {
        'title': insight.title,
        'description': insight.description,
        'confidence': insight.confidence,
        'impact': insight.impact.toString(),
        'type': insight.type.toString(),
      }).toList(),
      'totalInsights': insights.length,
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  /// توليد بيانات التوقعات
  static Future<Map<String, dynamic>> _generatePredictionData(DashboardWidget widget) async {
    final predictions = AIAnalyticsEngine.predictions.take(2).toList();
    
    return {
      'predictions': predictions.map((prediction) => {
        'title': prediction.title,
        'description': prediction.description,
        'confidence': prediction.confidence,
        'timeframe': prediction.timeframe.toString(),
        'type': prediction.type.toString(),
      }).toList(),
      'totalPredictions': predictions.length,
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  /// توليد بيانات الخريطة الحرارية
  static Future<Map<String, dynamic>> _generateHeatmapData(DashboardWidget widget) async {
    final heatmapData = <Map<String, dynamic>>[];
    
    // محاكاة بيانات الخريطة الحرارية
    for (int hour = 0; hour < 24; hour++) {
      for (int day = 0; day < 7; day++) {
        heatmapData.add({
          'hour': hour,
          'day': day,
          'value': (hour * day + DateTime.now().millisecond) % 100,
        });
      }
    }
    
    return {
      'data': heatmapData,
      'maxValue': 100,
      'minValue': 0,
      'lastUpdated': DateTime.now().toIso8601String(),
    };
  }

  /// إنشاء لوحات معلومات افتراضية
  static Future<void> _createDefaultDashboards() async {
    // لوحة المعلومات الرئيسية
    final mainDashboard = await createDashboard(
      name: 'لوحة المعلومات الرئيسية',
      description: 'نظرة عامة على جميع المقاييس',
      type: DashboardType.overview,
    );

    // إنشاء ويدجتات افتراضية
    final eventsWidget = await createWidget(
      title: 'إجمالي الأحداث',
      type: WidgetType.metric,
      config: {'metricType': 'events'},
      size: WidgetSize.small,
    );

    final insightsWidget = await createWidget(
      title: 'الرؤى الذكية',
      type: WidgetType.insight,
      config: {'maxInsights': 3},
      size: WidgetSize.large,
    );

    final chartWidget = await createWidget(
      title: 'اتجاه الاستخدام',
      type: WidgetType.chart,
      config: {'chartType': 'line', 'period': 'week'},
      size: WidgetSize.medium,
    );

    // إضافة الويدجتات للوحة
    await addWidgetToDashboard(mainDashboard.id, eventsWidget.id);
    await addWidgetToDashboard(mainDashboard.id, insightsWidget.id);
    await addWidgetToDashboard(mainDashboard.id, chartWidget.id);

    debugPrint('✅ تم إنشاء لوحات المعلومات الافتراضية');
  }

  // Data persistence methods
  static Future<void> _saveDashboards() async {
    final data = _dashboards.map((key, value) => MapEntry(key, value.toMap()));
    await StorageService.saveData(_dashboardsKey, data);
  }

  static Future<void> _loadDashboards() async {
    try {
      final data = await StorageService.getData(_dashboardsKey);
      if (data != null && data is Map) {
        _dashboards = data.map((key, value) => 
            MapEntry(key, AnalyticsDashboard.fromMap(value)));
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل لوحات المعلومات: $e');
    }
  }

  static Future<void> _saveWidgets() async {
    final data = _widgets.map((key, value) => MapEntry(key, value.toMap()));
    await StorageService.saveData(_widgetsKey, data);
  }

  static Future<void> _loadWidgets() async {
    try {
      final data = await StorageService.getData(_widgetsKey);
      if (data != null && data is Map) {
        _widgets = data.map((key, value) => 
            MapEntry(key, DashboardWidget.fromMap(value)));
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل الويدجتات: $e');
    }
  }

  static Future<void> _saveLayouts() async {
    final data = _layouts.map((key, value) => MapEntry(key, value.toMap()));
    await StorageService.saveData(_layoutsKey, data);
  }

  static Future<void> _loadLayouts() async {
    try {
      final data = await StorageService.getData(_layoutsKey);
      if (data != null && data is Map) {
        _layouts = data.map((key, value) => 
            MapEntry(key, DashboardLayout.fromMap(value)));
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل التخطيطات: $e');
    }
  }

  // Getters
  static bool get isInitialized => _isInitialized;
  static List<AnalyticsDashboard> get dashboards => _dashboards.values.toList();
  static List<DashboardWidget> get widgets => _widgets.values.toList();
  static int get dashboardsCount => _dashboards.length;
  static int get widgetsCount => _widgets.length;
}
