# خدمة الذكاء الاصطناعي المحسنة

## نظرة عامة

تم تحسين خدمة الذكاء الاصطناعي في تطبيق DeepSeek لتوفير:

- 🚀 **أداء محسن** مع ذاكرة تخزين مؤقت وإعادة المحاولة التلقائية
- 🔒 **أمان أفضل** مع إدارة مفاتيح API المنفصلة
- 🎯 **معالجة أخطاء متقدمة** مع رسائل واضحة للمستخدم
- 💬 **دعم المحادثات** مع الحفاظ على السياق
- 📊 **تحليلات مفصلة** مع معلومات الاستخدام والأداء
- 🌐 **دعم متعدد النماذج** (Gemini, GPT, DeepSeek)

## البنية الجديدة

```
lib/
├── services/
│   ├── ai_service.dart         # الخدمة الرئيسية
│   └── error_handler.dart      # معالج الأخطاء المركزي
├── config/
│   └── api_config.dart         # إعدادات API
└── examples/
    └── ai_service_example.dart # مثال الاستخدام
```

## الميزات الرئيسية

### 1. نماذج البيانات المحسنة

```dart
// رسالة مع بيانات وصفية
AIMessage(
  content: "محتوى الرسالة",
  role: "user", // أو "assistant" أو "system"
  timestamp: DateTime.now(),
  metadata: {"key": "value"},
)

// إعدادات مخصصة للتوليد
AISettings(
  temperature: 0.7,      // الإبداعية (0-1)
  maxTokens: 2048,       // الحد الأقصى للرموز
  topP: 0.95,           // التنوع
  topK: 40,             // أفضل K خيارات
  stopSequences: ["\\n"], // تسلسلات الإيقاف
  systemPrompt: "أنت مساعد مفيد", // موجه النظام
)

// نتيجة غنية بالمعلومات
AIResult(
  content: "المحتوى المولد",
  tokensUsed: 150,              // عدد الرموز المستخدمة
  processingTime: Duration(...), // وقت المعالجة
  metadata: {...},              // بيانات إضافية
)
```

### 2. الوظائف المحسنة

#### تلخيص النص
```dart
final result = await AIService.summarizeText(
  "النص الطويل هنا...",
  language: 'ar', // أو 'en'
  settings: AISettings(temperature: 0.3),
);
```

#### المساعدة في الكتابة
```dart
final result = await AIService.helpMeWrite(
  "موضوع الكتابة",
  style: 'creative', // professional, academic, casual, technical
  language: 'ar',
);
```

#### إنشاء خطة
```dart
final result = await AIService.makeAPlan(
  "هدفي هو...",
  timeframe: 30, // أيام
  constraints: "الميزانية محدودة",
);
```

#### تحليل البيانات
```dart
final result = await AIService.analyzeData(
  "البيانات هنا...",
  analysisType: 'statistical', // trend, predictive, comparative
  includeVisualization: true,
);
```

#### المحادثة مع السياق
```dart
// بدء محادثة جديدة
final conversationId = DateTime.now().millisecondsSinceEpoch.toString();

// إرسال رسالة
final result = await AIService.chat(
  "مرحباً",
  conversationId: conversationId,
  streamResponse: false, // للردود الطويلة
);

// الحصول على سجل المحادثة
final history = AIService.getConversationHistory(conversationId);
```

### 3. معالجة الأخطاء المتقدمة

```dart
try {
  final result = await AIService.summarizeText(text);
  // استخدام النتيجة
} catch (error) {
  // عرض رسالة خطأ مخصصة
  ErrorHandler.showError(context, error);
  
  // أو عرض SnackBar بسيط
  ErrorHandler.showSnackBarError(context, error);
  
  // تسجيل الخطأ للتحليل
  ErrorHandler.logError(error);
}
```

#### إعادة المحاولة التلقائية
```dart
final result = await ErrorHandler.handleWithRetry(
  operation: () => AIService.summarizeText(text),
  maxRetries: 3,
  retryDelay: Duration(seconds: 2),
  onRetry: (error, attempt) {
    print('محاولة رقم $attempt');
  },
);
```

### 4. إدارة التكوين

```dart
// في api_config.dart
class ApiConfig {
  // مفاتيح API (يجب نقلها لمتغيرات البيئة)
  static const String geminiApiKey = 'YOUR_KEY';
  
  // النماذج المتاحة
  static const Map<String, String> availableModels = {
    'gemini-pro': 'Gemini Pro',
    'gpt-4': 'GPT-4',
    'deepseek-chat': 'DeepSeek Chat',
  };
  
  // حدود الاستخدام
  static const int maxRequestsPerMinute = 60;
  static const int maxTokensPerRequest = 4096;
}
```

### 5. الذاكرة المؤقتة

```dart
// يتم تخزين النتائج تلقائياً لمدة 30 دقيقة
// لمسح الذاكرة المؤقتة:
AIService.clearCache();

// لمسح محادثة معينة:
AIService.clearConversation(conversationId);
```

## أفضل الممارسات

### 1. الأمان
- **لا تضع مفاتيح API في الكود مباشرة**
- استخدم متغيرات البيئة أو خدمة إدارة المفاتيح
- قم بتشفير البيانات الحساسة

### 2. الأداء
- استخدم الذاكرة المؤقتة للطلبات المتكررة
- حدد `maxTokens` المناسب لتوفير التكلفة
- استخدم `streamResponse` للردود الطويلة

### 3. تجربة المستخدم
- أظهر مؤشرات التحميل أثناء المعالجة
- قدم رسائل خطأ واضحة ومفيدة
- اسمح بإعادة المحاولة عند الفشل

### 4. التطوير
- استخدم `EnvironmentConfig` لإدارة البيئات
- فعّل التسجيل في بيئة التطوير فقط
- راقب استخدام الرموز والتكلفة

## أمثلة متقدمة

### معالجة دفعات من النصوص
```dart
final texts = ["نص 1", "نص 2", "نص 3"];
final results = await Future.wait(
  texts.map((text) => AIService.summarizeText(text)),
);
```

### محادثة مع ذاكرة طويلة
```dart
class ChatManager {
  final String conversationId;
  final int maxMessages = 20;
  
  Future<String> sendMessage(String message) async {
    // تنظيف المحادثات القديمة
    final history = AIService.getConversationHistory(conversationId);
    if (history.length > maxMessages) {
      AIService.clearConversation(conversationId);
      // إعادة إضافة آخر 10 رسائل فقط
    }
    
    final result = await AIService.chat(
      message,
      conversationId: conversationId,
    );
    
    return result.content;
  }
}
```

### تحليل متعدد المراحل
```dart
Future<Map<String, dynamic>> comprehensiveAnalysis(String data) async {
  // المرحلة 1: التحليل الأساسي
  final basicAnalysis = await AIService.analyzeData(
    data,
    analysisType: 'descriptive',
  );
  
  // المرحلة 2: اكتشاف الأنماط
  final patterns = await AIService.analyzeData(
    basicAnalysis.content,
    analysisType: 'trend',
  );
  
  // المرحلة 3: التنبؤات
  final predictions = await AIService.analyzeData(
    patterns.content,
    analysisType: 'predictive',
  );
  
  return {
    'basic': basicAnalysis.content,
    'patterns': patterns.content,
    'predictions': predictions.content,
    'totalTokens': (basicAnalysis.tokensUsed ?? 0) + 
                   (patterns.tokensUsed ?? 0) + 
                   (predictions.tokensUsed ?? 0),
  };
}
```

## استكشاف الأخطاء

### خطأ: مفتاح API غير صالح
```
AIException: Invalid API key (Code: INVALID_API_KEY)
```
**الحل**: تحقق من مفتاح API في `api_config.dart`

### خطأ: تجاوز حد الطلبات
```
AIException: Rate limit exceeded (Code: RATE_LIMIT)
```
**الحل**: انتظر قليلاً أو قلل عدد الطلبات

### خطأ: انتهت مهلة الطلب
```
AIException: Request timeout (Code: TIMEOUT)
```
**الحل**: تحقق من الاتصال بالإنترنت أو زد مهلة الانتظار

## الخطوات التالية

1. **إضافة دعم للصور**: تنفيذ `createImage` و `analyzeImage` مع APIs حقيقية
2. **Streaming API**: تنفيذ الردود المباشرة للمحادثات الطويلة
3. **تحليلات متقدمة**: إضافة تتبع للاستخدام والتكلفة
4. **دعم Offline**: تخزين محلي للنتائج المهمة
5. **تكامل مع MCP**: ربط مع Model Context Protocol

## المساهمة

نرحب بالمساهمات! الرجاء:
1. فتح issue لمناقشة التغييرات الكبيرة
2. اتباع نمط الكود الموجود
3. إضافة اختبارات للميزات الجديدة
4. تحديث التوثيق حسب الحاجة

## الترخيص

هذا المشروع مرخص تحت [رخصة MIT](LICENSE).