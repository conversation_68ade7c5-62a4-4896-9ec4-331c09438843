import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/app_colors.dart';
import '../widgets/modern_ui_components.dart';
import '../core/cloud/cloud_storage_service.dart';
import '../core/cloud/cloud_database_service.dart';
import '../core/cloud/push_notification_service.dart';

/// شاشة إدارة الخدمات السحابية
class CloudManagementScreen extends ConsumerStatefulWidget {
  const CloudManagementScreen({super.key});

  @override
  ConsumerState<CloudManagementScreen> createState() => _CloudManagementScreenState();
}

class _CloudManagementScreenState extends ConsumerState<CloudManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeServices();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    setState(() {
      _isLoading = true;
    });

    try {
      await CloudStorageService.initialize();
      await CloudDatabaseService.initialize();
      await PushNotificationService.initialize();
    } catch (e) {
      debugPrint('خطأ في تهيئة الخدمات السحابية: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.darkGrey,
        title: Text(
          'إدارة السحابة',
          style: TextStyle(color: AppColors.white),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primaryPurple,
          unselectedLabelColor: AppColors.lightGrey,
          indicatorColor: AppColors.primaryPurple,
          tabs: const [
            Tab(text: 'التخزين', icon: Icon(Icons.cloud_upload)),
            Tab(text: 'قاعدة البيانات', icon: Icon(Icons.storage)),
            Tab(text: 'الإشعارات', icon: Icon(Icons.notifications)),
          ],
        ),
      ),
      body: _isLoading
          ? Center(child: ModernUIComponents.modernLoadingIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildStorageTab(),
                _buildDatabaseTab(),
                _buildNotificationsTab(),
              ],
            ),
    );
  }

  Widget _buildStorageTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStorageStats(),
          const SizedBox(height: 24),
          _buildFilesList(),
          const SizedBox(height: 24),
          _buildUploadSection(),
        ],
      ),
    );
  }

  Widget _buildDatabaseTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDatabaseStats(),
          const SizedBox(height: 24),
          _buildCollectionsList(),
          const SizedBox(height: 24),
          _buildSyncSection(),
        ],
      ),
    );
  }

  Widget _buildNotificationsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildNotificationStats(),
          const SizedBox(height: 24),
          _buildNotificationHistory(),
          const SizedBox(height: 24),
          _buildSendNotificationSection(),
        ],
      ),
    );
  }

  Widget _buildStorageStats() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات التخزين',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'الملفات المرفوعة',
                  '${CloudStorageService.pendingUploadsCount}',
                  Icons.cloud_upload,
                  AppColors.primaryPurple,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatItem(
                  'حالة الاتصال',
                  CloudStorageService.isConnected ? 'متصل' : 'غير متصل',
                  Icons.wifi,
                  CloudStorageService.isConnected ? AppColors.success : AppColors.error,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 12,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildFilesList() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الملفات الحديثة',
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              TextButton(
                onPressed: () {
                  // عرض جميع الملفات
                },
                child: Text(
                  'عرض الكل',
                  style: TextStyle(color: AppColors.primaryPurple),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: 3,
            itemBuilder: (context, index) {
              return _buildFileItem(
                'ملف_$index.pdf',
                '${(index + 1) * 1.2} MB',
                Icons.picture_as_pdf,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildFileItem(String name, String size, IconData icon) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(icon, color: AppColors.primaryPurple),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: TextStyle(color: AppColors.white, fontSize: 14),
                ),
                Text(
                  size,
                  style: TextStyle(color: AppColors.textSecondary, fontSize: 12),
                ),
              ],
            ),
          ),
          IconButton(
            icon: Icon(Icons.download, color: AppColors.primaryPurple),
            onPressed: () {
              // تحميل الملف
            },
          ),
        ],
      ),
    );
  }

  Widget _buildUploadSection() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'رفع ملف جديد',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ModernUIComponents.modernButton(
            text: 'اختيار ملف',
            onPressed: () {
              // اختيار ملف للرفع
            },
            icon: Icons.file_upload,
            width: double.infinity,
          ),
        ],
      ),
    );
  }

  Widget _buildDatabaseStats() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات قاعدة البيانات',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'المجموعات',
                  '${CloudDatabaseService.getAllCollections().length}',
                  Icons.folder,
                  AppColors.primaryPurple,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatItem(
                  'العمليات المعلقة',
                  '${CloudDatabaseService.pendingSyncCount}',
                  Icons.sync,
                  AppColors.warning,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCollectionsList() {
    final collections = CloudDatabaseService.getAllCollections();
    
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المجموعات',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          if (collections.isEmpty)
            Text(
              'لا توجد مجموعات',
              style: TextStyle(color: AppColors.textSecondary),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: collections.length,
              itemBuilder: (context, index) {
                final collection = collections[index];
                return _buildCollectionItem(collection);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildCollectionItem(CloudCollection collection) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(Icons.folder, color: AppColors.primaryPurple),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              collection.name,
              style: TextStyle(color: AppColors.white, fontSize: 14),
            ),
          ),
          IconButton(
            icon: Icon(Icons.arrow_forward_ios, color: AppColors.primaryPurple),
            onPressed: () {
              // فتح المجموعة
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSyncSection() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'المزامنة',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ModernUIComponents.modernButton(
            text: 'مزامنة الآن',
            onPressed: () async {
              await CloudDatabaseService.syncDatabase();
              setState(() {});
            },
            icon: Icons.sync,
            width: double.infinity,
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationStats() {
    final stats = PushNotificationService.getNotificationStats();
    
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات الإشعارات',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'المرسلة',
                  '${stats.totalSent}',
                  Icons.send,
                  AppColors.primaryPurple,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatItem(
                  'المستلمة',
                  '${stats.delivered}',
                  Icons.done,
                  AppColors.success,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNotificationHistory() {
    final history = PushNotificationService.getNotificationHistory(limit: 5);
    
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الإشعارات الأخيرة',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          if (history.isEmpty)
            Text(
              'لا توجد إشعارات',
              style: TextStyle(color: AppColors.textSecondary),
            )
          else
            ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: history.length,
              itemBuilder: (context, index) {
                final notification = history[index];
                return _buildNotificationItem(notification);
              },
            ),
        ],
      ),
    );
  }

  Widget _buildNotificationItem(PushNotification notification) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            notification.title,
            style: TextStyle(
              color: AppColors.white,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            notification.body,
            style: TextStyle(color: AppColors.textSecondary, fontSize: 12),
          ),
        ],
      ),
    );
  }

  Widget _buildSendNotificationSection() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إرسال إشعار',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ModernUIComponents.modernButton(
            text: 'إرسال إشعار تجريبي',
            onPressed: () async {
              await PushNotificationService.sendCustomNotification(
                title: 'إشعار تجريبي',
                body: 'هذا إشعار تجريبي من التطبيق',
              );
              setState(() {});
            },
            icon: Icons.send,
            width: double.infinity,
          ),
        ],
      ),
    );
  }
}
