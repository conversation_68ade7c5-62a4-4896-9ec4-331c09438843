# سكريبت إصلاح مسارات الاستيراد
Write-Host "🔧 بدء إصلاح مسارات الاستيراد..." -ForegroundColor Green

$files = @(
    "lib/core/collaboration/workspace_service.dart",
    "lib/core/documentation/documentation_generator.dart", 
    "lib/core/notifications/advanced_notification_service.dart",
    "lib/core/offline/offline_service.dart",
    "lib/core/payment/payment_service.dart",
    "lib/core/performance/memory_manager.dart",
    "lib/core/performance/performance_optimizer.dart",
    "lib/core/performance/performance_service.dart",
    "lib/core/platform/companion_app_service.dart",
    "lib/core/platform/platform_support_service.dart",
    "lib/core/security/security_service.dart",
    "lib/core/testing/test_suite_manager.dart"
)

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "📝 إصلاح: $file" -ForegroundColor Yellow
        (Get-Content $file) -replace "../storage/storage_service.dart", "../services/storage_service.dart" | Set-Content $file
        Write-Host "✅ تم إصلاح: $file" -ForegroundColor Green
    } else {
        Write-Host "❌ الملف غير موجود: $file" -ForegroundColor Red
    }
}

Write-Host "🎉 تم الانتهاء من إصلاح مسارات الاستيراد!" -ForegroundColor Green
