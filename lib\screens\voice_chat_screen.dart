import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/services/voice_service.dart';
import '../core/services/enhanced_ai_service.dart';
import '../core/analytics/usage_analytics.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../widgets/enhanced_widgets.dart';

/// شاشة الدردشة الصوتية
class VoiceChatScreen extends ConsumerStatefulWidget {
  const VoiceChatScreen({super.key});

  @override
  ConsumerState<VoiceChatScreen> createState() => _VoiceChatScreenState();
}

class _VoiceChatScreenState extends ConsumerState<VoiceChatScreen>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _waveController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _waveAnimation;

  bool _isListening = false;
  bool _isSpeaking = false;
  bool _isProcessing = false;
  String _currentText = '';
  String _lastResponse = '';
  List<VoiceChatMessage> _messages = [];

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeVoiceService();
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _waveController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );

    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    _waveAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _waveController, curve: Curves.easeInOut),
    );

    _pulseController.repeat(reverse: true);
  }

  Future<void> _initializeVoiceService() async {
    try {
      await VoiceService.initialize();
      if (!VoiceService.isAvailable) {
        _showErrorSnackBar('خدمة الصوت غير متاحة على هذا الجهاز');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تهيئة خدمة الصوت: $e');
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _waveController.dispose();
    VoiceService.stopListening();
    VoiceService.stopSpeaking();
    super.dispose();
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
      ),
    );
  }

  Future<void> _startVoiceChat() async {
    if (!VoiceService.isAvailable) {
      _showErrorSnackBar('خدمة الصوت غير متاحة');
      return;
    }

    if (_isListening || _isSpeaking || _isProcessing) {
      await _stopCurrentAction();
      return;
    }

    try {
      setState(() {
        _isListening = true;
        _currentText = 'جاري الاستماع...';
      });

      _waveController.repeat();

      final recognizedText = await VoiceService.startListening(
        timeout: const Duration(seconds: 30),
      );

      _waveController.stop();

      if (recognizedText != null && recognizedText.isNotEmpty) {
        setState(() {
          _isListening = false;
          _isProcessing = true;
          _currentText = recognizedText;
        });

        // إضافة رسالة المستخدم
        final userMessage = VoiceChatMessage(
          text: recognizedText,
          isUser: true,
          timestamp: DateTime.now(),
        );
        
        setState(() {
          _messages.add(userMessage);
        });

        // إرسال للذكاء الاصطناعي
        await _processAIResponse(recognizedText);
      } else {
        setState(() {
          _isListening = false;
          _currentText = 'لم يتم التعرف على أي كلام';
        });
      }
    } catch (e) {
      setState(() {
        _isListening = false;
        _isProcessing = false;
        _currentText = 'خطأ في التعرف على الكلام';
      });
      _showErrorSnackBar('خطأ في التعرف على الكلام: $e');
    }
  }

  Future<void> _processAIResponse(String userInput) async {
    try {
      // تتبع الاستخدام
      await UsageAnalytics.trackToolUsage('voice_chat');

      final response = await EnhancedAIService.sendMessage(
        message: userInput,
        conversationId: 'voice_chat_${DateTime.now().millisecondsSinceEpoch}',
      );

      final aiMessage = VoiceChatMessage(
        text: response.content,
        isUser: false,
        timestamp: DateTime.now(),
      );

      setState(() {
        _messages.add(aiMessage);
        _isProcessing = false;
        _isSpeaking = true;
        _lastResponse = response.content;
        _currentText = 'جاري التحدث...';
      });

      // تحويل الرد إلى كلام
      await VoiceService.speakLongText(
        response.content,
        language: 'ar-SA',
      );

      setState(() {
        _isSpeaking = false;
        _currentText = 'اضغط للتحدث';
      });

    } catch (e) {
      setState(() {
        _isProcessing = false;
        _isSpeaking = false;
        _currentText = 'خطأ في معالجة الطلب';
      });
      _showErrorSnackBar('خطأ في معالجة الطلب: $e');
    }
  }

  Future<void> _stopCurrentAction() async {
    if (_isListening) {
      await VoiceService.stopListening();
      _waveController.stop();
    }
    if (_isSpeaking) {
      await VoiceService.stopSpeaking();
    }

    setState(() {
      _isListening = false;
      _isSpeaking = false;
      _isProcessing = false;
      _currentText = 'اضغط للتحدث';
    });
  }

  void _clearMessages() {
    setState(() {
      _messages.clear();
      _currentText = 'اضغط للتحدث';
      _lastResponse = '';
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.darkGrey,
        title: Text(
          'الدردشة الصوتية',
          style: AppTextStyles.heading.copyWith(color: AppColors.white),
        ),
        actions: [
          IconButton(
            onPressed: _clearMessages,
            icon: const Icon(Icons.clear_all, color: AppColors.white),
          ),
          IconButton(
            onPressed: _stopCurrentAction,
            icon: const Icon(Icons.stop, color: AppColors.error),
          ),
        ],
      ),
      body: Column(
        children: [
          // قائمة الرسائل
          Expanded(
            child: _messages.isEmpty
                ? _buildEmptyState()
                : ListView.builder(
                    padding: const EdgeInsets.all(16),
                    itemCount: _messages.length,
                    itemBuilder: (context, index) {
                      return _buildMessageBubble(_messages[index]);
                    },
                  ),
          ),

          // منطقة التحكم الصوتي
          _buildVoiceControlArea(),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.mic,
            size: 80,
            color: AppColors.lightPurple.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'مرحباً بك في الدردشة الصوتية',
            style: AppTextStyles.heading.copyWith(
              color: AppColors.lightPurple,
              fontSize: 20,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'اضغط على الميكروفون وابدأ التحدث',
            style: AppTextStyles.body.copyWith(
              color: AppColors.lightGrey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(VoiceChatMessage message) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment:
            message.isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        children: [
          if (!message.isUser) ...[
            CircleAvatar(
              backgroundColor: AppColors.primaryPurple,
              radius: 16,
              child: const Icon(
                Icons.smart_toy,
                color: AppColors.white,
                size: 16,
              ),
            ),
            const SizedBox(width: 8),
          ],
          Flexible(
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: message.isUser
                    ? AppColors.primaryPurple
                    : AppColors.darkGrey,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    message.text,
                    style: AppTextStyles.body.copyWith(
                      color: AppColors.white,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    _formatTime(message.timestamp),
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.lightGrey,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (message.isUser) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              backgroundColor: AppColors.lightPurple,
              radius: 16,
              child: const Icon(
                Icons.person,
                color: AppColors.white,
                size: 16,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildVoiceControlArea() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.darkGrey,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // النص الحالي
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.background,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              _currentText,
              style: AppTextStyles.body.copyWith(
                color: AppColors.white,
              ),
              textAlign: TextAlign.center,
            ),
          ),

          const SizedBox(height: 20),

          // زر الميكروفون
          GestureDetector(
            onTap: _startVoiceChat,
            child: AnimatedBuilder(
              animation: _pulseAnimation,
              builder: (context, child) {
                return Transform.scale(
                  scale: _isListening ? _pulseAnimation.value : 1.0,
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      gradient: LinearGradient(
                        colors: _isListening
                            ? [AppColors.error, AppColors.error.withOpacity(0.7)]
                            : _isSpeaking
                                ? [AppColors.success, AppColors.success.withOpacity(0.7)]
                                : _isProcessing
                                    ? [AppColors.warning, AppColors.warning.withOpacity(0.7)]
                                    : [AppColors.primaryPurple, AppColors.lightPurple],
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: (_isListening
                                  ? AppColors.error
                                  : _isSpeaking
                                      ? AppColors.success
                                      : _isProcessing
                                          ? AppColors.warning
                                          : AppColors.primaryPurple)
                              .withOpacity(0.3),
                          blurRadius: 20,
                          offset: const Offset(0, 10),
                        ),
                      ],
                    ),
                    child: Icon(
                      _isListening
                          ? Icons.mic
                          : _isSpeaking
                              ? Icons.volume_up
                              : _isProcessing
                                  ? Icons.hourglass_empty
                                  : Icons.mic_none,
                      color: AppColors.white,
                      size: 32,
                    ),
                  ),
                );
              },
            ),
          ),

          const SizedBox(height: 16),

          // مؤشر الحالة
          Text(
            _getStatusText(),
            style: AppTextStyles.caption.copyWith(
              color: AppColors.lightGrey,
            ),
          ),
        ],
      ),
    );
  }

  String _getStatusText() {
    if (_isListening) return 'جاري الاستماع... اضغط للإيقاف';
    if (_isSpeaking) return 'جاري التحدث... اضغط للإيقاف';
    if (_isProcessing) return 'جاري المعالجة...';
    return 'اضغط للتحدث';
  }

  String _formatTime(DateTime time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }
}

/// نموذج رسالة الدردشة الصوتية
class VoiceChatMessage {
  final String text;
  final bool isUser;
  final DateTime timestamp;

  VoiceChatMessage({
    required this.text,
    required this.isUser,
    required this.timestamp,
  });
}
