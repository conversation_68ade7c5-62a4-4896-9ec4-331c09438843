import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../core/services/enhanced_ai_service.dart';
import '../widgets/modern_ui_components.dart';

class WritingAssistanceScreen extends StatefulWidget {
  const WritingAssistanceScreen({super.key});

  @override
  State<WritingAssistanceScreen> createState() => _WritingAssistanceScreenState();
}

class _WritingAssistanceScreenState extends State<WritingAssistanceScreen>
    with TickerProviderStateMixin {
  final TextEditingController _topicController = TextEditingController();
  final TextEditingController _contextController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  String _generatedContent = '';
  bool _isLoading = false;
  String _selectedStyle = 'إبداعي';
  String _selectedTone = 'ودود';
  String _selectedAudience = 'عام';
  int _targetLength = 500;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<String> _writingStyles = [
    'إبداعي',
    'احترافي',
    'أكاديمي',
    'تقني',
    'غير رسمي',
    'مقنع',
  ];

  final List<String> _tones = [
    'ودود',
    'رسمي',
    'متحمس',
    'هادئ',
    'مقنع',
    'تعليمي',
  ];

  final List<String> _audiences = [
    'عام',
    'متخصصين',
    'طلاب',
    'أطفال',
    'مراهقين',
    'كبار السن',
  ];

  final List<Map<String, dynamic>> _quickTemplates = [
    {
      'icon': Icons.article_outlined,
      'title': 'مقال',
      'description': 'مقال شامل ومفصل',
      'color': Colors.blue,
      'prompt': 'اكتب مقالاً شاملاً عن',
    },
    {
      'icon': Icons.email_outlined,
      'title': 'رسالة',
      'description': 'رسالة رسمية أو شخصية',
      'color': Colors.green,
      'prompt': 'اكتب رسالة مهذبة حول',
    },
    {
      'icon': Icons.description_outlined,
      'title': 'تقرير',
      'description': 'تقرير مفصل ومنظم',
      'color': Colors.orange,
      'prompt': 'اكتب تقريراً مفصلاً عن',
    },
    {
      'icon': Icons.campaign_outlined,
      'title': 'إعلان',
      'description': 'محتوى إعلاني جذاب',
      'color': Colors.purple,
      'prompt': 'اكتب إعلاناً جذاباً لـ',
    },
    {
      'icon': Icons.school_outlined,
      'title': 'درس',
      'description': 'محتوى تعليمي واضح',
      'color': Colors.teal,
      'prompt': 'اكتب درساً تعليمياً عن',
    },
    {
      'icon': Icons.auto_stories_outlined,
      'title': 'قصة',
      'description': 'قصة إبداعية ممتعة',
      'color': Colors.pink,
      'prompt': 'اكتب قصة إبداعية عن',
    },
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _topicController.dispose();
    _contextController.dispose();
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _generateContent() async {
    if (_topicController.text.trim().isEmpty) {
      _showSnackBar('يرجى إدخال موضوع الكتابة', isError: true);
      return;
    }

    setState(() {
      _isLoading = true;
      _generatedContent = '';
    });

    try {
      final result = await EnhancedAIService.assistWriting(
        topic: _topicController.text.trim(),
        style: _selectedStyle.toLowerCase(),
        tone: _selectedTone.toLowerCase(),
        audience: _selectedAudience.toLowerCase(),
        targetLength: _targetLength,
        context: _contextController.text.trim().isNotEmpty
            ? _contextController.text.trim()
            : null,
      );

      setState(() {
        _generatedContent = result;
        _isLoading = false;
      });

      _showSnackBar('تم إنشاء المحتوى بنجاح!');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showSnackBar('حدث خطأ أثناء إنشاء المحتوى: ${e.toString()}', isError: true);
    }
  }

  void _useTemplate(String prompt) {
    setState(() {
      _topicController.text = prompt;
    });
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : AppColors.primaryPurple,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.darkPurple.withValues(alpha: 0.9),
              AppColors.background,
              AppColors.primaryPurple.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                _buildHeader(),
                _buildTemplatesSection(),
                Expanded(
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        _buildInputSection(),
                        const SizedBox(height: 20),
                        _buildSettingsSection(),
                        const SizedBox(height: 20),
                        _buildGenerateButton(),
                        if (_generatedContent.isNotEmpty || _isLoading) ...[
                          const SizedBox(height: 20),
                          _buildResultSection(),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.electricBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => Navigator.pop(context),
                borderRadius: BorderRadius.circular(12),
                child: const Padding(
                  padding: EdgeInsets.all(12),
                  child: Icon(Icons.arrow_back, color: Colors.white, size: 20),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.edit_outlined,
                      color: AppColors.electricBlue,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'مساعدة الكتابة',
                      style: AppTextStyles.heading2.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Text(
                  'اكتب محتوى إبداعي ومتميز',
                  style: AppTextStyles.body.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplatesSection() {
    return Container(
      height: 120,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'قوالب سريعة',
            style: AppTextStyles.body.copyWith(
              color: AppColors.textSecondary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _quickTemplates.length,
              itemBuilder: (context, index) {
                final template = _quickTemplates[index];
                return Container(
                  width: 140,
                  margin: const EdgeInsets.only(right: 12),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () => _useTemplate(template['prompt']),
                      borderRadius: BorderRadius.circular(16),
                      child: Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            colors: [
                              template['color'].withValues(alpha: 0.2),
                              template['color'].withValues(alpha: 0.1),
                            ],
                          ),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: template['color'].withValues(alpha: 0.3),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              template['icon'],
                              color: template['color'],
                              size: 24,
                            ),
                            const SizedBox(height: 4),
                            Text(
                              template['title'],
                              style: AppTextStyles.caption.copyWith(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                            Text(
                              template['description'],
                              style: AppTextStyles.caption.copyWith(
                                color: AppColors.textSecondary,
                                fontSize: 10,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputSection() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.electricBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.topic_outlined,
                  color: AppColors.electricBlue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'موضوع الكتابة',
                  style: AppTextStyles.body.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.electricBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: TextField(
              controller: _topicController,
              style: AppTextStyles.body.copyWith(color: Colors.white),
              maxLines: 3,
              decoration: InputDecoration(
                hintText: 'اكتب موضوع المحتوى المراد إنشاؤه...',
                hintStyle: AppTextStyles.body.copyWith(
                  color: AppColors.textSecondary,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
              ),
            ),
          ),

          // السياق الإضافي
          Padding(
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppColors.electricBlue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'سياق إضافي (اختياري)',
                  style: AppTextStyles.body.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.electricBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: TextField(
              controller: _contextController,
              style: AppTextStyles.body.copyWith(color: Colors.white),
              maxLines: 2,
              decoration: InputDecoration(
                hintText: 'أضف معلومات إضافية أو توجيهات خاصة...',
                hintStyle: AppTextStyles.body.copyWith(
                  color: AppColors.textSecondary,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.electricBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.tune_outlined,
                color: AppColors.electricBlue,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'إعدادات الكتابة',
                style: AppTextStyles.body.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // أسلوب الكتابة
          _buildDropdownSetting(
            'أسلوب الكتابة',
            _selectedStyle,
            _writingStyles,
            (value) => setState(() => _selectedStyle = value!),
          ),

          const SizedBox(height: 16),

          // نبرة الكتابة
          _buildDropdownSetting(
            'نبرة الكتابة',
            _selectedTone,
            _tones,
            (value) => setState(() => _selectedTone = value!),
          ),

          const SizedBox(height: 16),

          // الجمهور المستهدف
          _buildDropdownSetting(
            'الجمهور المستهدف',
            _selectedAudience,
            _audiences,
            (value) => setState(() => _selectedAudience = value!),
          ),

          const SizedBox(height: 16),

          // طول المحتوى
          Text(
            'طول المحتوى المطلوب: $_targetLength كلمة',
            style: AppTextStyles.body.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: AppColors.electricBlue,
              inactiveTrackColor: AppColors.darkGrey,
              thumbColor: AppColors.primaryPurple,
              overlayColor: AppColors.primaryPurple.withValues(alpha: 0.2),
            ),
            child: Slider(
              value: _targetLength.toDouble(),
              min: 100,
              max: 2000,
              divisions: 19,
              onChanged: (value) {
                setState(() {
                  _targetLength = value.round();
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownSetting(
    String label,
    String value,
    List<String> options,
    ValueChanged<String?> onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: AppTextStyles.body.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          decoration: BoxDecoration(
            color: AppColors.darkGrey.withValues(alpha: 0.5),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: AppColors.electricBlue.withValues(alpha: 0.3),
              width: 1,
            ),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<String>(
              value: value,
              onChanged: onChanged,
              dropdownColor: AppColors.darkGrey,
              style: AppTextStyles.body.copyWith(color: Colors.white),
              icon: Icon(
                Icons.keyboard_arrow_down,
                color: AppColors.electricBlue,
              ),
              items: options.map((String option) {
                return DropdownMenuItem<String>(
                  value: option,
                  child: Text(option),
                );
              }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildGenerateButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryPurple,
            AppColors.electricBlue,
          ],
        ),
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryPurple.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _isLoading ? null : _generateContent,
          borderRadius: BorderRadius.circular(28),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (_isLoading) ...[
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'جاري الكتابة...',
                    style: AppTextStyles.body.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ] else ...[
                  Icon(
                    Icons.auto_awesome,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'إنشاء المحتوى',
                    style: AppTextStyles.body.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildResultSection() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.electricBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.article_outlined,
                  color: AppColors.electricBlue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'المحتوى المُنشأ',
                  style: AppTextStyles.body.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (_generatedContent.isNotEmpty)
                  GestureDetector(
                    onTap: () {
                      Clipboard.setData(ClipboardData(text: _generatedContent));
                      _showSnackBar('تم نسخ المحتوى');
                    },
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.primaryPurple.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.copy,
                        color: AppColors.electricBlue,
                        size: 16,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.electricBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: _isLoading
                ? Center(
                    child: Column(
                      children: [
                        CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColors.electricBlue,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'جاري إنشاء المحتوى الإبداعي...',
                          style: AppTextStyles.body.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  )
                : SelectableText(
                    _generatedContent,
                    style: AppTextStyles.body.copyWith(
                      color: Colors.white,
                      height: 1.6,
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}