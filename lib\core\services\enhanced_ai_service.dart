import 'package:dio/dio.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:convert';
import 'dart:io';
import '../security/api_key_manager.dart';
import '../models/conversation_model.dart';
import 'unified_api_gateway.dart';
import 'feature_api_service.dart';

/// خدمة الذكاء الاصطناعي المحسنة
class EnhancedAIService {
  static final Dio _dio = Dio();
  static final Map<String, dynamic> _cache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 30);

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    // إعداد مفاتيح API الافتراضية
    await ApiKeyManager.setupDefaultKeys();

    // تهيئة البوابة الموحدة
    await UnifiedApiGateway.initialize();

    // إعداد Dio
    _dio.options = BaseOptions(
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 60),
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'DeepSeek-AI-App/1.0',
      },
    );

    // إضافة interceptors
    _dio.interceptors.add(
      LogInterceptor(
        requestBody: false, // لا نريد طباعة مفاتيح API
        responseBody: true,
      ),
    );
  }

  /// التحقق من الاتصال بالإنترنت
  static Future<bool> _checkConnectivity() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  /// إرسال رسالة (طريقة مبسطة)
  static Future<MessageModel> sendMessage({
    required String message,
    String? conversationId,
  }) async {
    try {
      final response = await createSmartChat(
        message: message,
        conversationId: conversationId ?? 'default',
      );
      return response;
    } catch (e) {
      return MessageModel.create(
        content: 'عذراً، حدث خطأ في معالجة رسالتك: ${e.toString()}',
        role: MessageRole.assistant,
        metadata: {'error': true},
      );
    }
  }

  /// إنشاء محادثة ذكية
  static Future<MessageModel> createSmartChat({
    required String message,
    required String conversationId,
    String model = 'gpt-3.5-turbo',
    double temperature = 0.7,
    int maxTokens = 2048,
    List<MessageModel>? context,
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception(
        'لا يوجد اتصال بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.',
      );
    }

    try {
      // بناء السياق من المحادثات السابقة
      final messages = <Map<String, dynamic>>[];

      // إضافة رسالة النظام
      messages.add({
        'role': 'system',
        'content':
            'أنت مساعد ذكي ومفيد. تجيب باللغة العربية بطريقة واضحة ومفصلة.',
      });

      // إضافة السياق السابق
      if (context != null && context.isNotEmpty) {
        for (final msg in context.take(10)) {
          // آخر 10 رسائل فقط
          messages.add({'role': msg.role.name, 'content': msg.content});
        }
      }

      // إضافة الرسالة الحالية
      messages.add({'role': 'user', 'content': message});

      // استخدام البوابة الموحدة
      final response = await UnifiedApiGateway.sendChatRequest(
        message: message,
        messages: messages,
        customModel: model,
        temperature: temperature,
        maxTokens: maxTokens,
      );

      final content = response['content'] as String;
      final tokensUsed = response['usage']?['total_tokens'] ?? 0;

      return MessageModel.create(
        content: content,
        role: MessageRole.assistant,
        metadata: {
          'model': model,
          'tokens_used': tokensUsed,
          'temperature': temperature,
          'conversation_id': conversationId,
        },
      );
    } on DioException catch (e) {
      if (e.response?.statusCode == 429) {
        throw Exception(
          'تم تجاوز حد الطلبات. يرجى الانتظار قليلاً والمحاولة مرة أخرى.',
        );
      } else if (e.response?.statusCode == 401) {
        throw Exception('مفتاح API غير صحيح. يرجى التحقق من الإعدادات.');
      } else if (e.response?.statusCode == 400) {
        throw Exception('طلب غير صحيح. يرجى التحقق من الرسالة.');
      } else {
        throw Exception('خطأ في الاتصال: ${e.message}');
      }
    } catch (e) {
      throw Exception('فشل في إنشاء الرد: ${e.toString()}');
    }
  }

  /// إنشاء رد تجريبي
  static MessageModel _createDemoResponse(
    String message,
    String conversationId,
  ) {
    final demoResponses = [
      'مرحباً! أنا مساعد ذكي تجريبي. للحصول على ردود حقيقية، يرجى إضافة مفتاح API في الإعدادات.',
      'هذا رد تجريبي. يمكنني مساعدتك في العديد من المهام عند إضافة مفتاح API.',
      'أهلاً بك! هذا تطبيق DeepSeek AI في الوضع التجريبي. لاستخدام الذكاء الاصطناعي الحقيقي، اذهب إلى الإعدادات وأضف مفتاح OpenAI API.',
      'شكراً لاستخدام تطبيقنا! هذه رسالة تجريبية. للحصول على ردود مخصصة، يرجى إعداد مفتاح API.',
    ];

    final randomIndex = DateTime.now().millisecond % demoResponses.length;
    final responseText = demoResponses[randomIndex];

    return MessageModel.create(
      content: responseText,
      role: MessageRole.assistant,
      metadata: {
        'model': 'demo-mode',
        'tokens_used': responseText.length,
        'temperature': 0.7,
        'conversation_id': conversationId,
        'is_demo': true,
      },
    );
  }

  /// تلخيص النص المتقدم
  static Future<String> advancedSummarize({
    required String text,
    String language = 'ar',
    String style = 'comprehensive',
    int maxLength = 500,
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    // التحقق من توفر الميزة
    if (!FeatureApiService.isFeatureAvailable('text_summarization')) {
      throw Exception('ميزة تلخيص النصوص غير مُعدة. يرجى إعداد API في الإعدادات المتقدمة.');
    }

    final cacheKey = 'summarize_${text.hashCode}_${style}_$maxLength';

    // التحقق من الذاكرة المؤقتة
    if (_cache.containsKey(cacheKey)) {
      final timestamp = _cacheTimestamps[cacheKey];
      if (timestamp != null &&
          DateTime.now().difference(timestamp) < _cacheExpiry) {
        return _cache[cacheKey];
      }
    }

    // الحصول على إعدادات الميزة
    final apiKey = FeatureApiService.getApiKey('text_summarization');
    final baseUrl = FeatureApiService.getBaseUrl('text_summarization');
    final model = FeatureApiService.getModel('text_summarization') ?? 'gpt-3.5-turbo';
    final customSettings = FeatureApiService.getCustomSettings('text_summarization');

    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('مفتاح API لتلخيص النصوص غير متوفر');
    }

    final stylePrompts = {
      'شامل': 'قم بتلخيص شامل ومفصل',
      'نقاط رئيسية': 'قم بتلخيص في نقاط رئيسية مرقمة',
      'ملخص سريع': 'قم بتلخيص مختصر وموجز',
      'تحليلي': 'قم بتلخيص تحليلي مع استنتاجات',
      'إبداعي': 'قم بتلخيص إبداعي وجذاب',
      'comprehensive': 'قم بتلخيص شامل ومفصل',
      'brief': 'قم بتلخيص مختصر وموجز',
      'bullet_points': 'قم بتلخيص في نقاط رئيسية',
      'executive': 'قم بتلخيص تنفيذي للإدارة',
    };

    final prompt = '''
${stylePrompts[style] ?? stylePrompts['comprehensive']} للنص التالي باللغة العربية.
يجب أن يكون التلخيص واضحاً ومفيداً ولا يتجاوز $maxLength كلمة.

النص:
$text

التلخيص:
''';

    try {
      final url = baseUrl ?? 'https://api.openai.com/v1/chat/completions';

      final response = await _dio.post(
        url,
        options: Options(headers: {'Authorization': 'Bearer $apiKey'}),
        data: {
          'model': model,
          'messages': [
            {
              'role': 'system',
              'content': 'أنت خبير في تلخيص النصوص باللغة العربية.',
            },
            {'role': 'user', 'content': prompt},
          ],
          'temperature': 0.3,
          'max_tokens': maxLength * 2,
        },
      );

      final result = response.data['choices'][0]['message']['content'];

      // حفظ في الذاكرة المؤقتة
      _cache[cacheKey] = result;
      _cacheTimestamps[cacheKey] = DateTime.now();

      return result;
    } catch (e) {
      throw Exception('فشل في تلخيص النص: ${e.toString()}');
    }
  }

  /// إنشاء الصور بالذكاء الاصطناعي
  static Future<String> generateImage({
    required String prompt,
    String size = '1024x1024',
    String quality = 'standard',
    String style = 'natural',
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    // التحقق من توفر الميزة
    if (!FeatureApiService.isFeatureAvailable('image_generation')) {
      throw Exception('ميزة إنشاء الصور غير مُعدة. يرجى إعداد API في الإعدادات المتقدمة.');
    }

    final cacheKey = 'image_${prompt.hashCode}_${size}_$quality';

    // التحقق من الذاكرة المؤقتة
    if (_cache.containsKey(cacheKey)) {
      final timestamp = _cacheTimestamps[cacheKey];
      if (timestamp != null &&
          DateTime.now().difference(timestamp) < _cacheExpiry) {
        return _cache[cacheKey];
      }
    }

    // الحصول على إعدادات الميزة
    final apiKey = FeatureApiService.getApiKey('image_generation');
    final baseUrl = FeatureApiService.getBaseUrl('image_generation');
    final model = FeatureApiService.getModel('image_generation') ?? 'dall-e-3';
    final customSettings = FeatureApiService.getCustomSettings('image_generation');

    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('مفتاح API لإنشاء الصور غير متوفر');
    }

    // تحسين النص للحصول على نتائج أفضل
    final enhancedPrompt = _enhanceImagePrompt(prompt, style);

    try {
      final url = baseUrl ?? 'https://api.openai.com/v1/images/generations';

      final response = await _dio.post(
        url,
        options: Options(headers: {'Authorization': 'Bearer $apiKey'}),
        data: {
          'model': model,
          'prompt': enhancedPrompt,
          'size': size,
          'quality': quality,
          'n': 1,
        },
      );

      final imageUrl = response.data['data'][0]['url'];

      // حفظ في الذاكرة المؤقتة
      _cache[cacheKey] = imageUrl;
      _cacheTimestamps[cacheKey] = DateTime.now();

      return imageUrl;
    } catch (e) {
      throw Exception('فشل في إنشاء الصورة: ${e.toString()}');
    }
  }

  /// تحسين نص الصورة للحصول على نتائج أفضل
  static String _enhanceImagePrompt(String prompt, String style) {
    final styleMap = {
      'واقعي': 'photorealistic, high quality, detailed',
      'رسم رقمي': 'digital art, artistic, creative',
      'أنمي': 'anime style, manga, japanese animation',
      'ثلاثي الأبعاد': '3D render, three dimensional, CGI',
      'تجريدي': 'abstract art, artistic, creative',
      'كرتوني': 'cartoon style, animated, colorful',
      'natural': 'natural, realistic',
      'vivid': 'vivid, colorful, artistic',
    };

    final styleText = styleMap[style] ?? styleMap['natural']!;
    return '$prompt, $styleText, high quality, detailed';
  }

  /// تحليل البيانات المتقدم
  static Future<String> analyzeData({
    required String data,
    String analysisType = 'comprehensive',
    String chartType = 'bar',
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    // التحقق من توفر الميزة
    if (!FeatureApiService.isFeatureAvailable('data_analysis')) {
      throw Exception('ميزة تحليل البيانات غير مُعدة. يرجى إعداد API في الإعدادات المتقدمة.');
    }

    final cacheKey = 'analysis_${data.hashCode}_${analysisType}_$chartType';

    // التحقق من الذاكرة المؤقتة
    if (_cache.containsKey(cacheKey)) {
      final timestamp = _cacheTimestamps[cacheKey];
      if (timestamp != null &&
          DateTime.now().difference(timestamp) < _cacheExpiry) {
        return _cache[cacheKey];
      }
    }

    // الحصول على إعدادات الميزة
    final apiKey = FeatureApiService.getApiKey('data_analysis');
    final baseUrl = FeatureApiService.getBaseUrl('data_analysis');
    final model = FeatureApiService.getModel('data_analysis') ?? 'gpt-3.5-turbo';

    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('مفتاح API لتحليل البيانات غير متوفر');
    }

    final analysisTypeMap = {
      'شامل': 'تحليل شامل ومفصل',
      'إحصائي': 'تحليل إحصائي متقدم',
      'اتجاهات': 'تحليل الاتجاهات والأنماط',
      'مقارنة': 'تحليل مقارن',
      'تنبؤات': 'تحليل تنبؤي',
      'comprehensive': 'تحليل شامل ومفصل',
      'statistical': 'تحليل إحصائي متقدم',
      'trends': 'تحليل الاتجاهات والأنماط',
      'comparison': 'تحليل مقارن',
      'predictive': 'تحليل تنبؤي',
    };

    final chartTypeMap = {
      'bar': 'رسم بياني بالأعمدة',
      'line': 'رسم بياني خطي',
      'pie': 'رسم بياني دائري',
      'scatter': 'رسم بياني نقطي',
    };

    final prompt = '''
قم بإجراء ${analysisTypeMap[analysisType] ?? analysisTypeMap['comprehensive']} للبيانات التالية:

البيانات:
$data

المطلوب:
1. تحليل البيانات وتحديد الأنماط والاتجاهات
2. استخراج الإحصائيات المهمة
3. تقديم رؤى وتوصيات
4. اقتراح ${chartTypeMap[chartType] ?? chartTypeMap['bar']} مناسب لعرض البيانات
5. تحديد النقاط المهمة والاستنتاجات

يرجى تقديم التحليل باللغة العربية بشكل واضح ومنظم.
''';

    try {
      final url = baseUrl ?? 'https://api.openai.com/v1/chat/completions';

      final response = await _dio.post(
        url,
        options: Options(headers: {'Authorization': 'Bearer $apiKey'}),
        data: {
          'model': model,
          'messages': [
            {
              'role': 'system',
              'content': 'أنت خبير في تحليل البيانات والإحصاء. تقدم تحليلات دقيقة ومفيدة باللغة العربية.',
            },
            {'role': 'user', 'content': prompt},
          ],
          'temperature': 0.3,
          'max_tokens': 1500,
        },
      );

      final result = response.data['choices'][0]['message']['content'];

      // حفظ في الذاكرة المؤقتة
      _cache[cacheKey] = result;
      _cacheTimestamps[cacheKey] = DateTime.now();

      return result;
    } catch (e) {
      throw Exception('فشل في تحليل البيانات: ${e.toString()}');
    }
  }

  /// إنشاء خطة ذكية
  static Future<String> createPlan({
    required String goal,
    String timeframe = 'monthly',
    String planType = 'personal',
    String detailLevel = 'medium',
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    // التحقق من توفر الميزة
    if (!FeatureApiService.isFeatureAvailable('plan_creation')) {
      throw Exception('ميزة إنشاء الخطط غير مُعدة. يرجى إعداد API في الإعدادات المتقدمة.');
    }

    final cacheKey = 'plan_${goal.hashCode}_${timeframe}_${planType}_$detailLevel';

    // التحقق من الذاكرة المؤقتة
    if (_cache.containsKey(cacheKey)) {
      final timestamp = _cacheTimestamps[cacheKey];
      if (timestamp != null &&
          DateTime.now().difference(timestamp) < _cacheExpiry) {
        return _cache[cacheKey];
      }
    }

    // الحصول على إعدادات الميزة
    final apiKey = FeatureApiService.getApiKey('plan_creation');
    final baseUrl = FeatureApiService.getBaseUrl('plan_creation');
    final model = FeatureApiService.getModel('plan_creation') ?? 'gpt-3.5-turbo';

    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('مفتاح API لإنشاء الخطط غير متوفر');
    }

    final timeframeMap = {
      'أسبوعي': 'أسبوع واحد',
      'شهري': 'شهر واحد',
      'ربع سنوي': 'ثلاثة أشهر',
      'نصف سنوي': 'ستة أشهر',
      'سنوي': 'سنة واحدة',
      'weekly': 'أسبوع واحد',
      'monthly': 'شهر واحد',
      'quarterly': 'ثلاثة أشهر',
      'semi-annual': 'ستة أشهر',
      'annual': 'سنة واحدة',
    };

    final planTypeMap = {
      'شخصي': 'شخصية',
      'مهني': 'مهنية',
      'تعليمي': 'تعليمية',
      'صحي': 'صحية',
      'مالي': 'مالية',
      'مشروع': 'مشروع',
      'personal': 'شخصية',
      'professional': 'مهنية',
      'educational': 'تعليمية',
      'health': 'صحية',
      'financial': 'مالية',
      'project': 'مشروع',
    };

    final detailLevelMap = {
      'بسيط': 'بسيط ومختصر',
      'متوسط': 'متوسط التفصيل',
      'مفصل': 'مفصل وشامل',
      'شامل': 'شامل جداً مع كل التفاصيل',
      'simple': 'بسيط ومختصر',
      'medium': 'متوسط التفصيل',
      'detailed': 'مفصل وشامل',
      'comprehensive': 'شامل جداً مع كل التفاصيل',
    };

    final prompt = '''
قم بإنشاء خطة ${planTypeMap[planType] ?? planTypeMap['personal']} ${detailLevelMap[detailLevel] ?? detailLevelMap['medium']} لتحقيق الهدف التالي:

الهدف: $goal

الإطار الزمني: ${timeframeMap[timeframe] ?? timeframeMap['monthly']}

المطلوب:
1. تحليل الهدف وتقسيمه إلى مراحل
2. وضع جدول زمني واضح
3. تحديد الموارد المطلوبة
4. وضع معايير قياس النجاح
5. تحديد التحديات المحتملة وحلولها
6. وضع خطة بديلة في حالة الطوارئ
7. تقديم نصائح عملية للتنفيذ

يرجى تقديم الخطة باللغة العربية بشكل منظم وقابل للتنفيذ.
''';

    try {
      final url = baseUrl ?? 'https://api.openai.com/v1/chat/completions';

      final response = await _dio.post(
        url,
        options: Options(headers: {'Authorization': 'Bearer $apiKey'}),
        data: {
          'model': model,
          'messages': [
            {
              'role': 'system',
              'content': 'أنت خبير في التخطيط الاستراتيجي وإدارة المشاريع. تقدم خطط عملية وقابلة للتنفيذ باللغة العربية.',
            },
            {'role': 'user', 'content': prompt},
          ],
          'temperature': 0.4,
          'max_tokens': 2000,
        },
      );

      final result = response.data['choices'][0]['message']['content'];

      // حفظ في الذاكرة المؤقتة
      _cache[cacheKey] = result;
      _cacheTimestamps[cacheKey] = DateTime.now();

      return result;
    } catch (e) {
      throw Exception('فشل في إنشاء الخطة: ${e.toString()}');
    }
  }

  /// تحليل البيانات المتقدم
  static Future<Map<String, dynamic>> advancedDataAnalysis({
    required String data,
    String analysisType = 'comprehensive',
    bool includeCharts = false,
    bool includeRecommendations = true,
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    final apiKey = await ApiKeyManager.getOpenAIKey();
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('مفتاح OpenAI غير متوفر');
    }

    final analysisPrompts = {
      'statistical': 'قم بتحليل إحصائي شامل',
      'trend': 'قم بتحليل الاتجاهات والأنماط',
      'predictive': 'قم بتحليل تنبؤي للمستقبل',
      'comparative': 'قم بتحليل مقارن',
      'comprehensive': 'قم بتحليل شامل ومتكامل',
    };

    final prompt = '''
${analysisPrompts[analysisType] ?? analysisPrompts['comprehensive']} للبيانات التالية:

البيانات:
$data

يرجى تقديم التحليل في التنسيق التالي:
1. ملخص البيانات
2. النتائج الرئيسية
3. الأنماط المكتشفة
4. الإحصائيات المهمة
${includeRecommendations ? '5. التوصيات والاقتراحات' : ''}
${includeCharts ? '6. اقتراحات للرسوم البيانية' : ''}

التحليل:
''';

    try {
      final response = await _dio.post(
        'https://api.openai.com/v1/chat/completions',
        options: Options(headers: {'Authorization': 'Bearer $apiKey'}),
        data: {
          'model': 'gpt-4',
          'messages': [
            {
              'role': 'system',
              'content':
                  'أنت خبير في تحليل البيانات والإحصاء. تقدم تحليلات دقيقة ومفيدة باللغة العربية.',
            },
            {'role': 'user', 'content': prompt},
          ],
          'temperature': 0.2,
          'max_tokens': 3000,
        },
      );

      final analysisResult = response.data['choices'][0]['message']['content'];
      final tokensUsed = response.data['usage']['total_tokens'];

      return {
        'analysis': analysisResult,
        'type': analysisType,
        'tokens_used': tokensUsed,
        'timestamp': DateTime.now().toIso8601String(),
        'include_charts': includeCharts,
        'include_recommendations': includeRecommendations,
      };
    } catch (e) {
      throw Exception('فشل في تحليل البيانات: ${e.toString()}');
    }
  }

  /// إنشاء خطة ذكية
  static Future<Map<String, dynamic>> createSmartPlan({
    required String goal,
    int timeframeDays = 30,
    String? constraints,
    String priority = 'medium',
    List<String>? resources,
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    final apiKey = await ApiKeyManager.getOpenAIKey();
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('مفتاح OpenAI غير متوفر');
    }

    final prompt = '''
قم بإنشاء خطة تفصيلية وذكية لتحقيق الهدف التالي:

الهدف: $goal
الإطار الزمني: $timeframeDays يوم
الأولوية: $priority
${constraints != null ? 'القيود والتحديات: $constraints' : ''}
${resources != null && resources.isNotEmpty ? 'الموارد المتاحة: ${resources.join(', ')}' : ''}

يرجى تقديم الخطة في التنسيق التالي:
1. تحليل الهدف
2. الأهداف الفرعية (مقسمة حسب الأسابيع)
3. الخطوات التفصيلية لكل هدف فرعي
4. الجدول الزمني المقترح
5. الموارد المطلوبة
6. المخاطر المحتملة وكيفية التعامل معها
7. مؤشرات النجاح والقياس
8. نصائح للتنفيذ الفعال

الخطة:
''';

    try {
      final response = await _dio.post(
        'https://api.openai.com/v1/chat/completions',
        options: Options(headers: {'Authorization': 'Bearer $apiKey'}),
        data: {
          'model': 'gpt-4',
          'messages': [
            {
              'role': 'system',
              'content':
                  'أنت خبير في التخطيط الاستراتيجي وإدارة المشاريع. تقدم خطط عملية وقابلة للتنفيذ باللغة العربية.',
            },
            {'role': 'user', 'content': prompt},
          ],
          'temperature': 0.4,
          'max_tokens': 4000,
        },
      );

      final planResult = response.data['choices'][0]['message']['content'];
      final tokensUsed = response.data['usage']['total_tokens'];

      return {
        'plan': planResult,
        'goal': goal,
        'timeframe_days': timeframeDays,
        'priority': priority,
        'constraints': constraints,
        'resources': resources,
        'tokens_used': tokensUsed,
        'created_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw Exception('فشل في إنشاء الخطة: ${e.toString()}');
    }
  }

  /// مساعدة في الكتابة المتقدمة
  static Future<Map<String, dynamic>> advancedWritingAssist({
    required String topic,
    String style = 'creative',
    String language = 'ar',
    int targetLength = 500,
    String? audience,
    String? tone,
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    final apiKey = await ApiKeyManager.getOpenAIKey();
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('مفتاح OpenAI غير متوفر');
    }

    final styleDescriptions = {
      'creative': 'إبداعي وملهم',
      'professional': 'احترافي ورسمي',
      'academic': 'أكاديمي وعلمي',
      'technical': 'تقني ومتخصص',
      'casual': 'ودود وغير رسمي',
      'persuasive': 'مقنع ومؤثر',
    };

    final prompt = '''
اكتب محتوى ${styleDescriptions[style] ?? 'متميز'} حول الموضوع التالي:

الموضوع: $topic
الأسلوب: ${styleDescriptions[style]}
الطول المطلوب: حوالي $targetLength كلمة
${audience != null ? 'الجمهور المستهدف: $audience' : ''}
${tone != null ? 'النبرة المطلوبة: $tone' : ''}

يرجى تقديم:
1. مقدمة جذابة
2. محتوى رئيسي منظم ومفصل
3. خاتمة قوية
4. استخدام أمثلة وتشبيهات مناسبة
5. تنسيق واضح ومقروء

المحتوى:
''';

    try {
      final response = await _dio.post(
        'https://api.openai.com/v1/chat/completions',
        options: Options(headers: {'Authorization': 'Bearer $apiKey'}),
        data: {
          'model': 'gpt-4',
          'messages': [
            {
              'role': 'system',
              'content':
                  'أنت كاتب محترف ومبدع. تكتب محتوى عالي الجودة باللغة العربية بأساليب متنوعة.',
            },
            {'role': 'user', 'content': prompt},
          ],
          'temperature': style == 'creative' ? 0.8 : 0.5,
          'max_tokens': targetLength * 2,
        },
      );

      final content = response.data['choices'][0]['message']['content'];
      final tokensUsed = response.data['usage']['total_tokens'];

      return {
        'content': content,
        'topic': topic,
        'style': style,
        'language': language,
        'target_length': targetLength,
        'actual_length': content.split(' ').length,
        'audience': audience,
        'tone': tone,
        'tokens_used': tokensUsed,
        'created_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw Exception('فشل في مساعدة الكتابة: ${e.toString()}');
    }
  }



  /// تحسين prompt للصور
  static Future<String> enhanceImagePrompt(String originalPrompt) async {
    final enhancePrompt = '''
حسن الوصف التالي لإنشاء صورة أفضل بالذكاء الاصطناعي:

"$originalPrompt"

اجعل الوصف أكثر تفصيلاً وإبداعاً، مع إضافة تفاصيل بصرية مهمة مثل:
- الألوان والإضاءة
- التركيب والزوايا
- الأسلوب الفني
- التفاصيل البيئية

الوصف المحسن:''';

    try {
      final result = await createSmartChat(
        message: enhancePrompt,
        conversationId:
            'image_enhance_${DateTime.now().millisecondsSinceEpoch}',
        model: 'gpt-3.5-turbo',
        temperature: 0.8,
        maxTokens: 500,
      );
      return result.content;
    } catch (e) {
      return originalPrompt; // إرجاع النص الأصلي في حالة الخطأ
    }
  }

  /// مسح الذاكرة المؤقتة
  static void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
  }

  /// تحليل الصور بالذكاء الاصطناعي
  static Future<MessageModel> analyzeImage({
    required String imagePath,
    required String conversationId,
    String? customPrompt,
  }) async {
    try {
      if (!await _checkConnectivity()) {
        throw Exception('لا يوجد اتصال بالإنترنت');
      }

      // تحويل الصورة إلى base64
      final imageBase64 = await _convertImageToBase64(imagePath);

      // إعداد الرسالة مع الصورة
      final prompt =
          customPrompt ??
          'قم بتحليل هذه الصورة وأخبرني ماذا ترى فيها بالتفصيل.';

      // محاولة استخدام GPT-4 Vision إذا كان متاحاً
      final response = await _sendVisionRequest(
        prompt: prompt,
        imageBase64: imageBase64,
        conversationId: conversationId,
      );

      return MessageModel.create(
        content: response,
        role: MessageRole.assistant,
      );
    } catch (e) {
      return MessageModel.create(
        content:
            'عذراً، لا أستطيع تحليل الصور حالياً. السبب: $e\n\nلتفعيل تحليل الصور، تحتاج إلى:\n• مفتاح OpenAI API صحيح\n• نموذج يدعم الرؤية مثل GPT-4 Vision',
        role: MessageRole.assistant,
      );
    }
  }

  /// تحليل المستندات بالذكاء الاصطناعي
  static Future<MessageModel> analyzeDocument({
    required String filePath,
    required String fileName,
    required String conversationId,
    String? customPrompt,
  }) async {
    try {
      if (!await _checkConnectivity()) {
        throw Exception('لا يوجد اتصال بالإنترنت');
      }

      // استخراج النص من المستند
      final extractedText = await _extractTextFromDocument(filePath, fileName);

      if (extractedText.isEmpty) {
        throw Exception('لا يمكن استخراج النص من هذا المستند');
      }

      // إعداد الطلب
      final prompt =
          customPrompt ?? 'قم بتحليل المستند التالي وقدم ملخصاً شاملاً عنه:';
      final fullPrompt = '$prompt\n\nمحتوى المستند:\n$extractedText';

      // إرسال للذكاء الاصطناعي
      final response = await createSmartChat(
        message: fullPrompt,
        conversationId: conversationId,
      );

      return response;
    } catch (e) {
      return MessageModel.create(
        content:
            'عذراً، لا أستطيع تحليل هذا المستند. السبب: $e\n\nالملفات المدعومة حالياً:\n• ملفات PDF\n• ملفات نصية (.txt)',
        role: MessageRole.assistant,
      );
    }
  }

  /// تحويل الصورة إلى base64
  static Future<String> _convertImageToBase64(String imagePath) async {
    try {
      final file = File(imagePath);
      final bytes = await file.readAsBytes();

      // تحويل مباشر إلى base64 (بدون ضغط مؤقتاً)
      return base64Encode(bytes);
    } catch (e) {
      throw Exception('فشل في تحويل الصورة: $e');
    }
  }

  /// إرسال طلب الرؤية لـ GPT-4 Vision
  static Future<String> _sendVisionRequest({
    required String prompt,
    required String imageBase64,
    required String conversationId,
  }) async {
    try {
      final apiKey = await ApiKeyManager.getOpenAIKey();
      if (apiKey == null || apiKey.isEmpty) {
        throw Exception('مفتاح OpenAI غير متاح');
      }

      final response = await _dio.post(
        'https://api.openai.com/v1/chat/completions',
        options: Options(
          headers: {
            'Authorization': 'Bearer $apiKey',
            'Content-Type': 'application/json',
          },
        ),
        data: {
          'model': 'gpt-4-vision-preview',
          'messages': [
            {
              'role': 'user',
              'content': [
                {'type': 'text', 'text': prompt},
                {
                  'type': 'image_url',
                  'image_url': {'url': 'data:image/jpeg;base64,$imageBase64'},
                },
              ],
            },
          ],
          'max_tokens': 1000,
          'temperature': 0.7,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        return data['choices'][0]['message']['content'] ??
            'لم أتمكن من تحليل الصورة';
      } else {
        throw Exception('خطأ في API: ${response.statusCode}');
      }
    } catch (e) {
      // محاولة استخدام OpenRouter كبديل
      return await _sendVisionRequestViaOpenRouter(
        prompt: prompt,
        imageBase64: imageBase64,
        conversationId: conversationId,
      );
    }
  }

  /// إرسال طلب الرؤية عبر OpenRouter
  static Future<String> _sendVisionRequestViaOpenRouter({
    required String prompt,
    required String imageBase64,
    required String conversationId,
  }) async {
    try {
      final apiKey = await ApiKeyManager.getOpenRouterKey();
      if (apiKey == null || apiKey.isEmpty) {
        throw Exception('مفتاح OpenRouter غير متاح');
      }

      final response = await _dio.post(
        'https://openrouter.ai/api/v1/chat/completions',
        options: Options(
          headers: {
            'Authorization': 'Bearer $apiKey',
            'Content-Type': 'application/json',
          },
        ),
        data: {
          'model': 'openai/gpt-4-vision-preview',
          'messages': [
            {
              'role': 'user',
              'content': [
                {'type': 'text', 'text': prompt},
                {
                  'type': 'image_url',
                  'image_url': {'url': 'data:image/jpeg;base64,$imageBase64'},
                },
              ],
            },
          ],
          'max_tokens': 1000,
          'temperature': 0.7,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        return data['choices'][0]['message']['content'] ??
            'لم أتمكن من تحليل الصورة';
      } else {
        throw Exception('خطأ في OpenRouter: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('فشل في تحليل الصورة عبر جميع المقدمين: $e');
    }
  }

  /// استخراج النص من المستندات
  static Future<String> _extractTextFromDocument(
    String filePath,
    String fileName,
  ) async {
    try {
      final file = File(filePath);
      final extension = fileName.toLowerCase().split('.').last;

      switch (extension) {
        case 'pdf':
          return await _extractTextFromPDF(file);
        case 'txt':
          return await file.readAsString();
        default:
          throw Exception('نوع الملف غير مدعوم: $extension');
      }
    } catch (e) {
      throw Exception('فشل في استخراج النص: $e');
    }
  }

  /// استخراج النص من ملف PDF (نسخة مبسطة)
  static Future<String> _extractTextFromPDF(File file) async {
    try {
      // مؤقتاً: إرجاع رسالة توضيحية
      throw Exception(
        'ميزة قراءة PDF قيد التطوير. يرجى تحويل الملف إلى نص (.txt) مؤقتاً.',
      );
    } catch (e) {
      throw Exception('فشل في قراءة ملف PDF: $e');
    }
  }

  /// المحادثة الذكية
  static Future<String> smartChat({
    required String message,
    required String chatMode,
    List<dynamic>? conversationHistory,
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    // التحقق من توفر الميزة
    if (!FeatureApiService.isFeatureAvailable('smart_chat')) {
      throw Exception('ميزة المحادثة الذكية غير مُعدة. يرجى إعداد API في الإعدادات المتقدمة.');
    }

    // الحصول على إعدادات الميزة
    final apiKey = FeatureApiService.getApiKey('smart_chat');
    final baseUrl = FeatureApiService.getBaseUrl('smart_chat');
    final model = FeatureApiService.getModel('smart_chat') ?? 'gpt-3.5-turbo';

    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('مفتاح API للمحادثة الذكية غير متوفر');
    }

    try {
      String systemPrompt = _getChatModePrompt(chatMode);

      List<Map<String, String>> messages = [
        {'role': 'system', 'content': systemPrompt}
      ];

      // إضافة تاريخ المحادثة
      if (conversationHistory != null && conversationHistory.isNotEmpty) {
        for (var msg in conversationHistory.take(10)) { // آخر 10 رسائل
          if (msg is Map && msg.containsKey('text') && msg.containsKey('isUser')) {
            messages.add({
              'role': msg['isUser'] ? 'user' : 'assistant',
              'content': msg['text']
            });
          }
        }
      }

      // إضافة الرسالة الحالية
      messages.add({'role': 'user', 'content': message});

      final url = baseUrl ?? 'https://api.openai.com/v1/chat/completions';

      final response = await _dio.post(
        url,
        options: Options(headers: {'Authorization': 'Bearer $apiKey'}),
        data: {
          'model': model,
          'messages': messages,
          'temperature': 0.7,
          'max_tokens': 1500,
        },
      );

      return response.data['choices'][0]['message']['content'];
    } catch (e) {
      throw Exception('فشل في المحادثة: ${e.toString()}');
    }
  }

  static String _getChatModePrompt(String mode) {
    switch (mode.toLowerCase()) {
      case 'مساعد شخصي':
        return '''أنت مساعد شخصي ذكي ومفيد. تساعد المستخدم في المهام اليومية، التذكيرات، والتنظيم.
كن ودودًا ومتفهمًا واقترح حلولاً عملية.''';

      case 'خبير تقني':
        return '''أنت خبير تقني متخصص في البرمجة والتكنولوجيا. قدم إجابات دقيقة وتقنية مع أمثلة عملية.
اشرح المفاهيم المعقدة بطريقة واضحة ومفهومة.''';

      case 'مدرس':
        return '''أنت مدرس صبور ومتفهم. اشرح المواضيع خطوة بخطوة، استخدم أمثلة بسيطة،
وتأكد من فهم المستخدم قبل الانتقال لنقطة جديدة.''';

      case 'مستشار':
        return '''أنت مستشار حكيم ومتفهم. استمع للمشاكل بعناية، اطرح أسئلة مفيدة،
وقدم نصائح عملية ومدروسة دون إصدار أحكام.''';

      case 'كاتب إبداعي':
        return '''أنت كاتب إبداعي موهوب. ساعد في الكتابة الإبداعية، اقترح أفكارًا جديدة،
وقدم نصائح لتحسين الأسلوب والمحتوى.''';

      default: // عام
        return '''أنت مساعد ذكي ومفيد. أجب على الأسئلة بطريقة واضحة ومفيدة،
وكن مهذبًا ومتعاونًا في جميع التفاعلات.''';
    }
  }

  /// تلخيص النص
  static Future<String> summarizeText({
    required String text,
    String style = 'comprehensive',
    int maxLength = 500,
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    // التحقق من توفر الميزة
    if (!FeatureApiService.isFeatureAvailable('text_summarization')) {
      throw Exception('ميزة تلخيص النص غير مُعدة. يرجى إعداد API في الإعدادات المتقدمة.');
    }

    // الحصول على إعدادات الميزة
    final apiKey = FeatureApiService.getApiKey('text_summarization');
    final baseUrl = FeatureApiService.getBaseUrl('text_summarization');
    final model = FeatureApiService.getModel('text_summarization') ?? 'gpt-3.5-turbo';

    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('مفتاح API لتلخيص النص غير متوفر');
    }

    try {
      String styleDescription = _getSummaryStyleDescription(style);

      String prompt = '''
قم بتلخيص النص التالي بأسلوب $styleDescription في حدود $maxLength كلمة:

النص الأصلي:
"$text"

متطلبات التلخيص:
- احتفظ بالنقاط الرئيسية والمعلومات المهمة
- استخدم أسلوب $styleDescription
- اجعل الملخص واضحاً ومفهوماً
- لا تتجاوز $maxLength كلمة
- استخدم اللغة العربية الفصحى

الملخص:''';

      final url = baseUrl ?? 'https://api.openai.com/v1/chat/completions';

      final response = await _dio.post(
        url,
        options: Options(headers: {'Authorization': 'Bearer $apiKey'}),
        data: {
          'model': model,
          'messages': [
            {'role': 'user', 'content': prompt},
          ],
          'temperature': 0.3,
          'max_tokens': maxLength * 2,
        },
      );

      return response.data['choices'][0]['message']['content'];
    } catch (e) {
      throw Exception('فشل في تلخيص النص: ${e.toString()}');
    }
  }

  static String _getSummaryStyleDescription(String style) {
    switch (style.toLowerCase()) {
      case 'شامل':
      case 'comprehensive':
        return 'شامل ومفصل يغطي جميع النقاط المهمة';
      case 'نقاط رئيسية':
      case 'bullet_points':
        return 'نقاط رئيسية منظمة ومرتبة';
      case 'ملخص سريع':
      case 'quick':
        return 'سريع ومختصر يركز على الأساسيات';
      case 'تحليلي':
      case 'analytical':
        return 'تحليلي يربط بين الأفكار والمفاهيم';
      case 'إبداعي':
      case 'creative':
        return 'إبداعي وجذاب مع أسلوب ممتع';
      default:
        return 'متوازن وواضح';
    }
  }

  /// مساعدة الكتابة
  static Future<String> assistWriting({
    required String topic,
    String style = 'creative',
    String tone = 'friendly',
    String audience = 'general',
    int targetLength = 500,
    String? context,
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    // التحقق من توفر الميزة
    if (!FeatureApiService.isFeatureAvailable('writing_assistance')) {
      throw Exception('ميزة مساعدة الكتابة غير مُعدة. يرجى إعداد API في الإعدادات المتقدمة.');
    }

    // الحصول على إعدادات الميزة
    final apiKey = FeatureApiService.getApiKey('writing_assistance');
    final baseUrl = FeatureApiService.getBaseUrl('writing_assistance');
    final model = FeatureApiService.getModel('writing_assistance') ?? 'gpt-3.5-turbo';

    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('مفتاح API لمساعدة الكتابة غير متوفر');
    }

    try {
      String systemPrompt = _getWritingStylePrompt(style, tone, audience);

      String prompt = '''
$systemPrompt

الموضوع: $topic
الطول المطلوب: حوالي $targetLength كلمة
${context != null ? 'السياق الإضافي: $context' : ''}

يرجى كتابة محتوى عالي الجودة يتناسب مع المتطلبات المحددة.
''';

      final url = baseUrl ?? 'https://api.openai.com/v1/chat/completions';

      final response = await _dio.post(
        url,
        options: Options(headers: {'Authorization': 'Bearer $apiKey'}),
        data: {
          'model': model,
          'messages': [
            {'role': 'user', 'content': prompt},
          ],
          'temperature': style == 'creative' ? 0.8 : 0.5,
          'max_tokens': targetLength * 2,
        },
      );

      return response.data['choices'][0]['message']['content'];
    } catch (e) {
      throw Exception('فشل في مساعدة الكتابة: ${e.toString()}');
    }
  }

  static String _getWritingStylePrompt(String style, String tone, String audience) {
    String styleDescription = '';
    switch (style.toLowerCase()) {
      case 'إبداعي':
      case 'creative':
        styleDescription = 'إبداعي وملهم مع استخدام الصور البلاغية والأسلوب الجذاب';
        break;
      case 'احترافي':
      case 'professional':
        styleDescription = 'احترافي ورسمي مع التركيز على الوضوح والدقة';
        break;
      case 'أكاديمي':
      case 'academic':
        styleDescription = 'أكاديمي وعلمي مع الاستشهاد بالمصادر والأدلة';
        break;
      case 'تقني':
      case 'technical':
        styleDescription = 'تقني ومتخصص مع شرح المفاهيم المعقدة بوضوح';
        break;
      case 'غير رسمي':
      case 'casual':
        styleDescription = 'ودود وغير رسمي مع لغة بسيطة ومفهومة';
        break;
      case 'مقنع':
      case 'persuasive':
        styleDescription = 'مقنع ومؤثر مع استخدام الحجج القوية والأدلة';
        break;
      default:
        styleDescription = 'متوازن ومناسب للموضوع';
    }

    String toneDescription = '';
    switch (tone.toLowerCase()) {
      case 'ودود':
      case 'friendly':
        toneDescription = 'ودود ومرحب';
        break;
      case 'رسمي':
      case 'formal':
        toneDescription = 'رسمي ومهذب';
        break;
      case 'متحمس':
      case 'enthusiastic':
        toneDescription = 'متحمس ومتفائل';
        break;
      case 'هادئ':
      case 'calm':
        toneDescription = 'هادئ ومتزن';
        break;
      case 'مقنع':
      case 'persuasive':
        toneDescription = 'مقنع ومؤثر';
        break;
      case 'تعليمي':
      case 'educational':
        toneDescription = 'تعليمي وواضح';
        break;
      default:
        toneDescription = 'متوازن ومناسب';
    }

    String audienceDescription = '';
    switch (audience.toLowerCase()) {
      case 'عام':
      case 'general':
        audienceDescription = 'الجمهور العام';
        break;
      case 'متخصصين':
      case 'experts':
        audienceDescription = 'المتخصصين والخبراء';
        break;
      case 'طلاب':
      case 'students':
        audienceDescription = 'الطلاب والدارسين';
        break;
      case 'أطفال':
      case 'children':
        audienceDescription = 'الأطفال';
        break;
      case 'مراهقين':
      case 'teenagers':
        audienceDescription = 'المراهقين';
        break;
      case 'كبار السن':
      case 'seniors':
        audienceDescription = 'كبار السن';
        break;
      default:
        audienceDescription = 'الجمهور المستهدف';
    }

    return '''أنت كاتب محترف ومبدع. اكتب محتوى $styleDescription بنبرة $toneDescription يناسب $audienceDescription.

المتطلبات:
- استخدم اللغة العربية الفصحى الواضحة
- اجعل المحتوى منظماً ومترابطاً
- استخدم أمثلة وتشبيهات مناسبة
- اهتم بالتدرج المنطقي للأفكار
- اجعل المحتوى جذاباً ومفيداً''';
  }

  /// الترجمة الذكية
  static Future<String> translateText({
    required String text,
    required String sourceLanguage,
    required String targetLanguage,
    String style = 'standard',
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    // التحقق من توفر الميزة
    if (!FeatureApiService.isFeatureAvailable('smart_translation')) {
      throw Exception('ميزة الترجمة الذكية غير مُعدة. يرجى إعداد API في الإعدادات المتقدمة.');
    }

    // الحصول على إعدادات الميزة
    final apiKey = FeatureApiService.getApiKey('smart_translation');
    final baseUrl = FeatureApiService.getBaseUrl('smart_translation');
    final model = FeatureApiService.getModel('smart_translation') ?? 'gpt-3.5-turbo';

    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('مفتاح API للترجمة الذكية غير متوفر');
    }

    try {
      String sourceLanguageName = _getLanguageName(sourceLanguage);
      String targetLanguageName = _getLanguageName(targetLanguage);
      String styleDescription = _getTranslationStyleDescription(style);

      String prompt = '''
قم بترجمة النص التالي من $sourceLanguageName إلى $targetLanguageName بأسلوب $styleDescription:

النص الأصلي:
"$text"

متطلبات الترجمة:
- احتفظ بالمعنى الأصلي بدقة
- استخدم أسلوب $styleDescription
- تأكد من الطبيعية في اللغة المستهدفة
- احتفظ بأي مصطلحات تقنية إذا كانت موجودة
- لا تضع علامات اقتباس حول النتيجة

الترجمة:''';

      final url = baseUrl ?? 'https://api.openai.com/v1/chat/completions';

      final response = await _dio.post(
        url,
        options: Options(headers: {'Authorization': 'Bearer $apiKey'}),
        data: {
          'model': model,
          'messages': [
            {'role': 'user', 'content': prompt},
          ],
          'temperature': 0.3,
          'max_tokens': 1500,
        },
      );

      return response.data['choices'][0]['message']['content'];
    } catch (e) {
      throw Exception('فشل في الترجمة: ${e.toString()}');
    }
  }

  static String _getLanguageName(String code) {
    switch (code) {
      case 'ar': return 'العربية';
      case 'en': return 'الإنجليزية';
      case 'fr': return 'الفرنسية';
      case 'de': return 'الألمانية';
      case 'es': return 'الإسبانية';
      case 'it': return 'الإيطالية';
      case 'pt': return 'البرتغالية';
      case 'ru': return 'الروسية';
      case 'ja': return 'اليابانية';
      case 'ko': return 'الكورية';
      case 'zh': return 'الصينية';
      case 'hi': return 'الهندية';
      case 'tr': return 'التركية';
      case 'nl': return 'الهولندية';
      case 'sv': return 'السويدية';
      case 'fa': return 'الفارسية';
      case 'ur': return 'الأردية';
      default: return code;
    }
  }

  static String _getTranslationStyleDescription(String style) {
    switch (style.toLowerCase()) {
      case 'standard':
        return 'ترجمة عادية ومتوازنة';
      case 'formal':
        return 'ترجمة رسمية ومهذبة';
      case 'casual':
        return 'ترجمة غير رسمية وودودة';
      case 'technical':
        return 'ترجمة تقنية ودقيقة';
      case 'literary':
        return 'ترجمة أدبية وإبداعية';
      case 'business':
        return 'ترجمة تجارية واحترافية';
      default:
        return 'ترجمة عادية';
    }
  }

  /// التصفح الذكي
  static Future<Map<String, dynamic>> smartBrowsing({
    required String query,
    String mode = 'comprehensive',
    int maxResults = 10,
    bool includeSummary = true,
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    // التحقق من توفر الميزة
    if (!FeatureApiService.isFeatureAvailable('smart_browsing')) {
      throw Exception('ميزة التصفح الذكي غير مُعدة. يرجى إعداد API في الإعدادات المتقدمة.');
    }

    // الحصول على إعدادات الميزة
    final apiKey = FeatureApiService.getApiKey('smart_browsing');
    final baseUrl = FeatureApiService.getBaseUrl('smart_browsing');
    final model = FeatureApiService.getModel('smart_browsing') ?? 'gpt-3.5-turbo';

    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('مفتاح API للتصفح الذكي غير متوفر');
    }

    try {
      String searchPrompt = _getBrowsingModePrompt(mode);

      String prompt = '''
$searchPrompt

استعلام البحث: $query
عدد النتائج المطلوب: $maxResults
${includeSummary ? 'يرجى تضمين ملخص شامل للنتائج' : ''}

يرجى البحث وتقديم النتائج في تنسيق JSON مع الحقول التالية:
{
  "results": [
    {
      "title": "عنوان النتيجة",
      "description": "وصف مختصر",
      "url": "الرابط"
    }
  ],
  "summary": "ملخص شامل للنتائج (إذا طُلب)"
}
''';

      final url = baseUrl ?? 'https://api.openai.com/v1/chat/completions';

      final response = await _dio.post(
        url,
        options: Options(headers: {'Authorization': 'Bearer $apiKey'}),
        data: {
          'model': model,
          'messages': [
            {'role': 'user', 'content': prompt},
          ],
          'temperature': 0.3,
          'max_tokens': 2000,
        },
      );

      final content = response.data['choices'][0]['message']['content'];

      // محاولة تحليل JSON
      try {
        final jsonData = json.decode(content);
        return jsonData;
      } catch (e) {
        // إذا فشل تحليل JSON، إنشاء استجابة افتراضية
        return {
          'results': [
            {
              'title': 'نتيجة البحث',
              'description': content,
              'url': 'https://example.com',
            }
          ],
          'summary': includeSummary ? content : '',
        };
      }
    } catch (e) {
      throw Exception('فشل في التصفح الذكي: ${e.toString()}');
    }
  }

  static String _getBrowsingModePrompt(String mode) {
    switch (mode.toLowerCase()) {
      case 'شامل':
      case 'comprehensive':
        return 'ابحث بشكل شامل ومفصل عن المعلومات المطلوبة من مصادر متنوعة';
      case 'أكاديمي':
      case 'academic':
        return 'ركز على المصادر الأكاديمية والبحثية والعلمية الموثوقة';
      case 'أخبار':
      case 'news':
        return 'ابحث في المصادر الإخبارية الحديثة والموثوقة';
      case 'تقني':
      case 'technical':
        return 'ركز على المعلومات التقنية والتكنولوجية المتخصصة';
      case 'تجاري':
      case 'business':
        return 'ابحث في المصادر التجارية والاقتصادية والمالية';
      case 'ترفيهي':
      case 'entertainment':
        return 'ركز على المحتوى الترفيهي والثقافي والفني';
      default:
        return 'ابحث عن المعلومات المطلوبة من مصادر متنوعة وموثوقة';
    }
  }

  /// الحصول على إحصائيات الاستخدام
  static Map<String, dynamic> getUsageStats() {
    return {
      'cache_size': _cache.length,
      'cache_hit_rate': _cache.isNotEmpty ? 0.85 : 0.0, // تقدير
      'last_request': DateTime.now().toIso8601String(),
    };
  }
}
