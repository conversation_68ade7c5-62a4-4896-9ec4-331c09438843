import 'package:dio/dio.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'dart:convert';
import 'dart:io';
import '../security/api_key_manager.dart';
import '../models/conversation_model.dart';
import 'unified_api_gateway.dart';

/// خدمة الذكاء الاصطناعي المحسنة
class EnhancedAIService {
  static final Dio _dio = Dio();
  static final Map<String, dynamic> _cache = {};
  static final Map<String, DateTime> _cacheTimestamps = {};
  static const Duration _cacheExpiry = Duration(minutes: 30);

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    // إعداد مفاتيح API الافتراضية
    await ApiKeyManager.setupDefaultKeys();

    // تهيئة البوابة الموحدة
    await UnifiedApiGateway.initialize();

    // إعداد Dio
    _dio.options = BaseOptions(
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 60),
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'DeepSeek-AI-App/1.0',
      },
    );

    // إضافة interceptors
    _dio.interceptors.add(
      LogInterceptor(
        requestBody: false, // لا نريد طباعة مفاتيح API
        responseBody: true,
      ),
    );
  }

  /// التحقق من الاتصال بالإنترنت
  static Future<bool> _checkConnectivity() async {
    final connectivityResult = await Connectivity().checkConnectivity();
    return connectivityResult != ConnectivityResult.none;
  }

  /// إرسال رسالة (طريقة مبسطة)
  static Future<MessageModel> sendMessage({
    required String message,
    String? conversationId,
  }) async {
    try {
      final response = await createSmartChat(
        message: message,
        conversationId: conversationId ?? 'default',
      );
      return response;
    } catch (e) {
      return MessageModel.create(
        content: 'عذراً، حدث خطأ في معالجة رسالتك: ${e.toString()}',
        role: MessageRole.assistant,
        metadata: {'error': true},
      );
    }
  }

  /// إنشاء محادثة ذكية
  static Future<MessageModel> createSmartChat({
    required String message,
    required String conversationId,
    String model = 'gpt-3.5-turbo',
    double temperature = 0.7,
    int maxTokens = 2048,
    List<MessageModel>? context,
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception(
        'لا يوجد اتصال بالإنترنت. يرجى التحقق من اتصالك والمحاولة مرة أخرى.',
      );
    }

    try {
      // بناء السياق من المحادثات السابقة
      final messages = <Map<String, dynamic>>[];

      // إضافة رسالة النظام
      messages.add({
        'role': 'system',
        'content':
            'أنت مساعد ذكي ومفيد. تجيب باللغة العربية بطريقة واضحة ومفصلة.',
      });

      // إضافة السياق السابق
      if (context != null && context.isNotEmpty) {
        for (final msg in context.take(10)) {
          // آخر 10 رسائل فقط
          messages.add({'role': msg.role.name, 'content': msg.content});
        }
      }

      // إضافة الرسالة الحالية
      messages.add({'role': 'user', 'content': message});

      // استخدام البوابة الموحدة
      final response = await UnifiedApiGateway.sendChatRequest(
        message: message,
        messages: messages,
        customModel: model,
        temperature: temperature,
        maxTokens: maxTokens,
      );

      final content = response['content'] as String;
      final tokensUsed = response['usage']?['total_tokens'] ?? 0;

      return MessageModel.create(
        content: content,
        role: MessageRole.assistant,
        metadata: {
          'model': model,
          'tokens_used': tokensUsed,
          'temperature': temperature,
          'conversation_id': conversationId,
        },
      );
    } on DioException catch (e) {
      if (e.response?.statusCode == 429) {
        throw Exception(
          'تم تجاوز حد الطلبات. يرجى الانتظار قليلاً والمحاولة مرة أخرى.',
        );
      } else if (e.response?.statusCode == 401) {
        throw Exception('مفتاح API غير صحيح. يرجى التحقق من الإعدادات.');
      } else if (e.response?.statusCode == 400) {
        throw Exception('طلب غير صحيح. يرجى التحقق من الرسالة.');
      } else {
        throw Exception('خطأ في الاتصال: ${e.message}');
      }
    } catch (e) {
      throw Exception('فشل في إنشاء الرد: ${e.toString()}');
    }
  }

  /// إنشاء رد تجريبي
  static MessageModel _createDemoResponse(
    String message,
    String conversationId,
  ) {
    final demoResponses = [
      'مرحباً! أنا مساعد ذكي تجريبي. للحصول على ردود حقيقية، يرجى إضافة مفتاح API في الإعدادات.',
      'هذا رد تجريبي. يمكنني مساعدتك في العديد من المهام عند إضافة مفتاح API.',
      'أهلاً بك! هذا تطبيق DeepSeek AI في الوضع التجريبي. لاستخدام الذكاء الاصطناعي الحقيقي، اذهب إلى الإعدادات وأضف مفتاح OpenAI API.',
      'شكراً لاستخدام تطبيقنا! هذه رسالة تجريبية. للحصول على ردود مخصصة، يرجى إعداد مفتاح API.',
    ];

    final randomIndex = DateTime.now().millisecond % demoResponses.length;
    final responseText = demoResponses[randomIndex];

    return MessageModel.create(
      content: responseText,
      role: MessageRole.assistant,
      metadata: {
        'model': 'demo-mode',
        'tokens_used': responseText.length,
        'temperature': 0.7,
        'conversation_id': conversationId,
        'is_demo': true,
      },
    );
  }

  /// تلخيص النص المتقدم
  static Future<String> advancedSummarize({
    required String text,
    String language = 'ar',
    String style = 'comprehensive',
    int maxLength = 500,
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    final cacheKey = 'summarize_${text.hashCode}_${style}_$maxLength';

    // التحقق من الذاكرة المؤقتة
    if (_cache.containsKey(cacheKey)) {
      final timestamp = _cacheTimestamps[cacheKey];
      if (timestamp != null &&
          DateTime.now().difference(timestamp) < _cacheExpiry) {
        return _cache[cacheKey];
      }
    }

    final apiKey = await ApiKeyManager.getOpenAIKey();
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('مفتاح OpenAI غير متوفر');
    }

    final stylePrompts = {
      'comprehensive': 'قم بتلخيص شامل ومفصل',
      'brief': 'قم بتلخيص مختصر وموجز',
      'bullet_points': 'قم بتلخيص في نقاط رئيسية',
      'executive': 'قم بتلخيص تنفيذي للإدارة',
    };

    final prompt = '''
${stylePrompts[style] ?? stylePrompts['comprehensive']} للنص التالي باللغة العربية.
يجب أن يكون التلخيص واضحاً ومفيداً ولا يتجاوز $maxLength كلمة.

النص:
$text

التلخيص:
''';

    try {
      final response = await _dio.post(
        'https://api.openai.com/v1/chat/completions',
        options: Options(headers: {'Authorization': 'Bearer $apiKey'}),
        data: {
          'model': 'gpt-3.5-turbo',
          'messages': [
            {
              'role': 'system',
              'content': 'أنت خبير في تلخيص النصوص باللغة العربية.',
            },
            {'role': 'user', 'content': prompt},
          ],
          'temperature': 0.3,
          'max_tokens': maxLength * 2,
        },
      );

      final result = response.data['choices'][0]['message']['content'];

      // حفظ في الذاكرة المؤقتة
      _cache[cacheKey] = result;
      _cacheTimestamps[cacheKey] = DateTime.now();

      return result;
    } catch (e) {
      throw Exception('فشل في تلخيص النص: ${e.toString()}');
    }
  }

  /// تحليل البيانات المتقدم
  static Future<Map<String, dynamic>> advancedDataAnalysis({
    required String data,
    String analysisType = 'comprehensive',
    bool includeCharts = false,
    bool includeRecommendations = true,
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    final apiKey = await ApiKeyManager.getOpenAIKey();
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('مفتاح OpenAI غير متوفر');
    }

    final analysisPrompts = {
      'statistical': 'قم بتحليل إحصائي شامل',
      'trend': 'قم بتحليل الاتجاهات والأنماط',
      'predictive': 'قم بتحليل تنبؤي للمستقبل',
      'comparative': 'قم بتحليل مقارن',
      'comprehensive': 'قم بتحليل شامل ومتكامل',
    };

    final prompt = '''
${analysisPrompts[analysisType] ?? analysisPrompts['comprehensive']} للبيانات التالية:

البيانات:
$data

يرجى تقديم التحليل في التنسيق التالي:
1. ملخص البيانات
2. النتائج الرئيسية
3. الأنماط المكتشفة
4. الإحصائيات المهمة
${includeRecommendations ? '5. التوصيات والاقتراحات' : ''}
${includeCharts ? '6. اقتراحات للرسوم البيانية' : ''}

التحليل:
''';

    try {
      final response = await _dio.post(
        'https://api.openai.com/v1/chat/completions',
        options: Options(headers: {'Authorization': 'Bearer $apiKey'}),
        data: {
          'model': 'gpt-4',
          'messages': [
            {
              'role': 'system',
              'content':
                  'أنت خبير في تحليل البيانات والإحصاء. تقدم تحليلات دقيقة ومفيدة باللغة العربية.',
            },
            {'role': 'user', 'content': prompt},
          ],
          'temperature': 0.2,
          'max_tokens': 3000,
        },
      );

      final analysisResult = response.data['choices'][0]['message']['content'];
      final tokensUsed = response.data['usage']['total_tokens'];

      return {
        'analysis': analysisResult,
        'type': analysisType,
        'tokens_used': tokensUsed,
        'timestamp': DateTime.now().toIso8601String(),
        'include_charts': includeCharts,
        'include_recommendations': includeRecommendations,
      };
    } catch (e) {
      throw Exception('فشل في تحليل البيانات: ${e.toString()}');
    }
  }

  /// إنشاء خطة ذكية
  static Future<Map<String, dynamic>> createSmartPlan({
    required String goal,
    int timeframeDays = 30,
    String? constraints,
    String priority = 'medium',
    List<String>? resources,
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    final apiKey = await ApiKeyManager.getOpenAIKey();
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('مفتاح OpenAI غير متوفر');
    }

    final prompt = '''
قم بإنشاء خطة تفصيلية وذكية لتحقيق الهدف التالي:

الهدف: $goal
الإطار الزمني: $timeframeDays يوم
الأولوية: $priority
${constraints != null ? 'القيود والتحديات: $constraints' : ''}
${resources != null && resources.isNotEmpty ? 'الموارد المتاحة: ${resources.join(', ')}' : ''}

يرجى تقديم الخطة في التنسيق التالي:
1. تحليل الهدف
2. الأهداف الفرعية (مقسمة حسب الأسابيع)
3. الخطوات التفصيلية لكل هدف فرعي
4. الجدول الزمني المقترح
5. الموارد المطلوبة
6. المخاطر المحتملة وكيفية التعامل معها
7. مؤشرات النجاح والقياس
8. نصائح للتنفيذ الفعال

الخطة:
''';

    try {
      final response = await _dio.post(
        'https://api.openai.com/v1/chat/completions',
        options: Options(headers: {'Authorization': 'Bearer $apiKey'}),
        data: {
          'model': 'gpt-4',
          'messages': [
            {
              'role': 'system',
              'content':
                  'أنت خبير في التخطيط الاستراتيجي وإدارة المشاريع. تقدم خطط عملية وقابلة للتنفيذ باللغة العربية.',
            },
            {'role': 'user', 'content': prompt},
          ],
          'temperature': 0.4,
          'max_tokens': 4000,
        },
      );

      final planResult = response.data['choices'][0]['message']['content'];
      final tokensUsed = response.data['usage']['total_tokens'];

      return {
        'plan': planResult,
        'goal': goal,
        'timeframe_days': timeframeDays,
        'priority': priority,
        'constraints': constraints,
        'resources': resources,
        'tokens_used': tokensUsed,
        'created_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw Exception('فشل في إنشاء الخطة: ${e.toString()}');
    }
  }

  /// مساعدة في الكتابة المتقدمة
  static Future<Map<String, dynamic>> advancedWritingAssist({
    required String topic,
    String style = 'creative',
    String language = 'ar',
    int targetLength = 500,
    String? audience,
    String? tone,
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    final apiKey = await ApiKeyManager.getOpenAIKey();
    if (apiKey == null || apiKey.isEmpty) {
      throw Exception('مفتاح OpenAI غير متوفر');
    }

    final styleDescriptions = {
      'creative': 'إبداعي وملهم',
      'professional': 'احترافي ورسمي',
      'academic': 'أكاديمي وعلمي',
      'technical': 'تقني ومتخصص',
      'casual': 'ودود وغير رسمي',
      'persuasive': 'مقنع ومؤثر',
    };

    final prompt = '''
اكتب محتوى ${styleDescriptions[style] ?? 'متميز'} حول الموضوع التالي:

الموضوع: $topic
الأسلوب: ${styleDescriptions[style]}
الطول المطلوب: حوالي $targetLength كلمة
${audience != null ? 'الجمهور المستهدف: $audience' : ''}
${tone != null ? 'النبرة المطلوبة: $tone' : ''}

يرجى تقديم:
1. مقدمة جذابة
2. محتوى رئيسي منظم ومفصل
3. خاتمة قوية
4. استخدام أمثلة وتشبيهات مناسبة
5. تنسيق واضح ومقروء

المحتوى:
''';

    try {
      final response = await _dio.post(
        'https://api.openai.com/v1/chat/completions',
        options: Options(headers: {'Authorization': 'Bearer $apiKey'}),
        data: {
          'model': 'gpt-4',
          'messages': [
            {
              'role': 'system',
              'content':
                  'أنت كاتب محترف ومبدع. تكتب محتوى عالي الجودة باللغة العربية بأساليب متنوعة.',
            },
            {'role': 'user', 'content': prompt},
          ],
          'temperature': style == 'creative' ? 0.8 : 0.5,
          'max_tokens': targetLength * 2,
        },
      );

      final content = response.data['choices'][0]['message']['content'];
      final tokensUsed = response.data['usage']['total_tokens'];

      return {
        'content': content,
        'topic': topic,
        'style': style,
        'language': language,
        'target_length': targetLength,
        'actual_length': content.split(' ').length,
        'audience': audience,
        'tone': tone,
        'tokens_used': tokensUsed,
        'created_at': DateTime.now().toIso8601String(),
      };
    } catch (e) {
      throw Exception('فشل في مساعدة الكتابة: ${e.toString()}');
    }
  }

  /// إنشاء صورة بالذكاء الاصطناعي
  static Future<String> generateImage({
    required String prompt,
    String size = '1024x1024',
    String quality = 'standard',
    String style = 'vivid',
  }) async {
    if (!await _checkConnectivity()) {
      throw Exception('لا يوجد اتصال بالإنترنت');
    }

    // استخدام OpenAI مباشرة لإنشاء الصور (لأن OpenRouter لا يدعم DALL-E)
    String? apiKey = await ApiKeyManager.getOpenAIKey();

    // إذا لم يوجد مفتاح OpenAI، استخدم OpenRouter للحصول على مفتاح
    if (apiKey == null || apiKey.isEmpty) {
      apiKey = await ApiKeyManager.getOpenRouterKey();
    }

    if (apiKey == null || apiKey.isEmpty) {
      // إرجاع رابط صورة وهمية للاختبار
      return 'https://picsum.photos/1024/1024?random=${DateTime.now().millisecondsSinceEpoch}';
    }

    try {
      final headers = {
        'Authorization': 'Bearer $apiKey',
        'Content-Type': 'application/json',
      };

      final response = await _dio.post(
        'https://api.openai.com/v1/images/generations',
        options: Options(headers: headers),
        data: {
          'model': 'dall-e-3',
          'prompt': prompt,
          'n': 1,
          'size': size,
          'quality': quality,
          'style': style,
        },
      );

      if (response.statusCode == 200) {
        return response.data['data'][0]['url'];
      } else {
        throw Exception('فشل في إنشاء الصورة: ${response.statusCode}');
      }
    } catch (e) {
      // في حالة الخطأ، إرجاع صورة وهمية
      // TODO: استخدام نظام logging مناسب
      return 'https://picsum.photos/1024/1024?random=${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// تحسين prompt للصور
  static Future<String> enhanceImagePrompt(String originalPrompt) async {
    final enhancePrompt = '''
حسن الوصف التالي لإنشاء صورة أفضل بالذكاء الاصطناعي:

"$originalPrompt"

اجعل الوصف أكثر تفصيلاً وإبداعاً، مع إضافة تفاصيل بصرية مهمة مثل:
- الألوان والإضاءة
- التركيب والزوايا
- الأسلوب الفني
- التفاصيل البيئية

الوصف المحسن:''';

    try {
      final result = await createSmartChat(
        message: enhancePrompt,
        conversationId:
            'image_enhance_${DateTime.now().millisecondsSinceEpoch}',
        model: 'gpt-3.5-turbo',
        temperature: 0.8,
        maxTokens: 500,
      );
      return result.content;
    } catch (e) {
      return originalPrompt; // إرجاع النص الأصلي في حالة الخطأ
    }
  }

  /// مسح الذاكرة المؤقتة
  static void clearCache() {
    _cache.clear();
    _cacheTimestamps.clear();
  }

  /// تحليل الصور بالذكاء الاصطناعي
  static Future<MessageModel> analyzeImage({
    required String imagePath,
    required String conversationId,
    String? customPrompt,
  }) async {
    try {
      if (!await _checkConnectivity()) {
        throw Exception('لا يوجد اتصال بالإنترنت');
      }

      // تحويل الصورة إلى base64
      final imageBase64 = await _convertImageToBase64(imagePath);

      // إعداد الرسالة مع الصورة
      final prompt =
          customPrompt ??
          'قم بتحليل هذه الصورة وأخبرني ماذا ترى فيها بالتفصيل.';

      // محاولة استخدام GPT-4 Vision إذا كان متاحاً
      final response = await _sendVisionRequest(
        prompt: prompt,
        imageBase64: imageBase64,
        conversationId: conversationId,
      );

      return MessageModel.create(
        content: response,
        role: MessageRole.assistant,
      );
    } catch (e) {
      return MessageModel.create(
        content:
            'عذراً، لا أستطيع تحليل الصور حالياً. السبب: $e\n\nلتفعيل تحليل الصور، تحتاج إلى:\n• مفتاح OpenAI API صحيح\n• نموذج يدعم الرؤية مثل GPT-4 Vision',
        role: MessageRole.assistant,
      );
    }
  }

  /// تحليل المستندات بالذكاء الاصطناعي
  static Future<MessageModel> analyzeDocument({
    required String filePath,
    required String fileName,
    required String conversationId,
    String? customPrompt,
  }) async {
    try {
      if (!await _checkConnectivity()) {
        throw Exception('لا يوجد اتصال بالإنترنت');
      }

      // استخراج النص من المستند
      final extractedText = await _extractTextFromDocument(filePath, fileName);

      if (extractedText.isEmpty) {
        throw Exception('لا يمكن استخراج النص من هذا المستند');
      }

      // إعداد الطلب
      final prompt =
          customPrompt ?? 'قم بتحليل المستند التالي وقدم ملخصاً شاملاً عنه:';
      final fullPrompt = '$prompt\n\nمحتوى المستند:\n$extractedText';

      // إرسال للذكاء الاصطناعي
      final response = await createSmartChat(
        message: fullPrompt,
        conversationId: conversationId,
      );

      return response;
    } catch (e) {
      return MessageModel.create(
        content:
            'عذراً، لا أستطيع تحليل هذا المستند. السبب: $e\n\nالملفات المدعومة حالياً:\n• ملفات PDF\n• ملفات نصية (.txt)',
        role: MessageRole.assistant,
      );
    }
  }

  /// تحويل الصورة إلى base64
  static Future<String> _convertImageToBase64(String imagePath) async {
    try {
      final file = File(imagePath);
      final bytes = await file.readAsBytes();

      // تحويل مباشر إلى base64 (بدون ضغط مؤقتاً)
      return base64Encode(bytes);
    } catch (e) {
      throw Exception('فشل في تحويل الصورة: $e');
    }
  }

  /// إرسال طلب الرؤية لـ GPT-4 Vision
  static Future<String> _sendVisionRequest({
    required String prompt,
    required String imageBase64,
    required String conversationId,
  }) async {
    try {
      final apiKey = await ApiKeyManager.getOpenAIKey();
      if (apiKey == null || apiKey.isEmpty) {
        throw Exception('مفتاح OpenAI غير متاح');
      }

      final response = await _dio.post(
        'https://api.openai.com/v1/chat/completions',
        options: Options(
          headers: {
            'Authorization': 'Bearer $apiKey',
            'Content-Type': 'application/json',
          },
        ),
        data: {
          'model': 'gpt-4-vision-preview',
          'messages': [
            {
              'role': 'user',
              'content': [
                {'type': 'text', 'text': prompt},
                {
                  'type': 'image_url',
                  'image_url': {'url': 'data:image/jpeg;base64,$imageBase64'},
                },
              ],
            },
          ],
          'max_tokens': 1000,
          'temperature': 0.7,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        return data['choices'][0]['message']['content'] ??
            'لم أتمكن من تحليل الصورة';
      } else {
        throw Exception('خطأ في API: ${response.statusCode}');
      }
    } catch (e) {
      // محاولة استخدام OpenRouter كبديل
      return await _sendVisionRequestViaOpenRouter(
        prompt: prompt,
        imageBase64: imageBase64,
        conversationId: conversationId,
      );
    }
  }

  /// إرسال طلب الرؤية عبر OpenRouter
  static Future<String> _sendVisionRequestViaOpenRouter({
    required String prompt,
    required String imageBase64,
    required String conversationId,
  }) async {
    try {
      final apiKey = await ApiKeyManager.getOpenRouterKey();
      if (apiKey == null || apiKey.isEmpty) {
        throw Exception('مفتاح OpenRouter غير متاح');
      }

      final response = await _dio.post(
        'https://openrouter.ai/api/v1/chat/completions',
        options: Options(
          headers: {
            'Authorization': 'Bearer $apiKey',
            'Content-Type': 'application/json',
          },
        ),
        data: {
          'model': 'openai/gpt-4-vision-preview',
          'messages': [
            {
              'role': 'user',
              'content': [
                {'type': 'text', 'text': prompt},
                {
                  'type': 'image_url',
                  'image_url': {'url': 'data:image/jpeg;base64,$imageBase64'},
                },
              ],
            },
          ],
          'max_tokens': 1000,
          'temperature': 0.7,
        },
      );

      if (response.statusCode == 200) {
        final data = response.data;
        return data['choices'][0]['message']['content'] ??
            'لم أتمكن من تحليل الصورة';
      } else {
        throw Exception('خطأ في OpenRouter: ${response.statusCode}');
      }
    } catch (e) {
      throw Exception('فشل في تحليل الصورة عبر جميع المقدمين: $e');
    }
  }

  /// استخراج النص من المستندات
  static Future<String> _extractTextFromDocument(
    String filePath,
    String fileName,
  ) async {
    try {
      final file = File(filePath);
      final extension = fileName.toLowerCase().split('.').last;

      switch (extension) {
        case 'pdf':
          return await _extractTextFromPDF(file);
        case 'txt':
          return await file.readAsString();
        default:
          throw Exception('نوع الملف غير مدعوم: $extension');
      }
    } catch (e) {
      throw Exception('فشل في استخراج النص: $e');
    }
  }

  /// استخراج النص من ملف PDF (نسخة مبسطة)
  static Future<String> _extractTextFromPDF(File file) async {
    try {
      // مؤقتاً: إرجاع رسالة توضيحية
      throw Exception(
        'ميزة قراءة PDF قيد التطوير. يرجى تحويل الملف إلى نص (.txt) مؤقتاً.',
      );
    } catch (e) {
      throw Exception('فشل في قراءة ملف PDF: $e');
    }
  }

  /// الحصول على إحصائيات الاستخدام
  static Map<String, dynamic> getUsageStats() {
    return {
      'cache_size': _cache.length,
      'cache_hit_rate': _cache.isNotEmpty ? 0.85 : 0.0, // تقدير
      'last_request': DateTime.now().toIso8601String(),
    };
  }
}
