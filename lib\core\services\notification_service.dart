import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';

/// خدمة الإشعارات المتقدمة
class NotificationService {
  static final FlutterLocalNotificationsPlugin _notifications =
      FlutterLocalNotificationsPlugin();

  static bool _isInitialized = false;

  /// تهيئة خدمة الإشعارات
  static Future<void> initialize() async {
    if (_isInitialized) return;

    const AndroidInitializationSettings initializationSettingsAndroid =
        AndroidInitializationSettings('@mipmap/ic_launcher');

    const DarwinInitializationSettings initializationSettingsIOS =
        DarwinInitializationSettings(
          requestAlertPermission: true,
          requestBadgePermission: true,
          requestSoundPermission: true,
        );

    const InitializationSettings initializationSettings =
        InitializationSettings(
          android: initializationSettingsAndroid,
          iOS: initializationSettingsIOS,
        );

    await _notifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    _isInitialized = true;
  }

  /// معالجة النقر على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('تم النقر على الإشعار: ${response.payload}');
    // يمكن إضافة منطق التنقل هنا
  }

  /// طلب الأذونات (iOS)
  static Future<bool> requestPermissions() async {
    final bool? result = await _notifications
        .resolvePlatformSpecificImplementation<
          IOSFlutterLocalNotificationsPlugin
        >()
        ?.requestPermissions(alert: true, badge: true, sound: true);
    return result ?? false;
  }

  /// إرسال إشعار فوري
  static Future<void> showInstantNotification({
    required String title,
    required String body,
    String? payload,
    NotificationType type = NotificationType.info,
  }) async {
    await _notifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      _getNotificationDetails(type),
      payload: payload,
    );
  }

  /// إرسال إشعار مجدول
  static Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledDate,
    String? payload,
    NotificationType type = NotificationType.info,
  }) async {
    // TODO: تنفيذ جدولة الإشعارات لاحقاً
    debugPrint('جدولة إشعار: $title');
  }

  /// إشعار تقدم المهمة
  static Future<void> showProgressNotification({
    required String title,
    required int progress,
    required int maxProgress,
    String? body,
  }) async {
    final AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
          'progress_channel',
          'Progress Notifications',
          channelDescription: 'إشعارات تقدم المهام',
          importance: Importance.low,
          priority: Priority.low,
          showProgress: true,
          maxProgress: maxProgress,
          progress: progress,
          ongoing: true,
          autoCancel: false,
        );

    final NotificationDetails notificationDetails = NotificationDetails(
      android: androidDetails,
    );

    await _notifications.show(
      999, // معرف ثابت لإشعار التقدم
      title,
      body ?? '$progress من $maxProgress',
      notificationDetails,
    );
  }

  /// إشعار مع أزرار إجراءات
  static Future<void> showActionNotification({
    required String title,
    required String body,
    required List<NotificationAction> actions,
    String? payload,
  }) async {
    final AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
          'action_channel',
          'Action Notifications',
          channelDescription: 'إشعارات مع إجراءات',
          importance: Importance.high,
          priority: Priority.high,
          actions:
              actions
                  .map(
                    (action) => AndroidNotificationAction(
                      action.id,
                      action.title,
                      // icon: action.icon,
                    ),
                  )
                  .toList(),
        );

    final NotificationDetails notificationDetails = NotificationDetails(
      android: androidDetails,
    );

    await _notifications.show(
      DateTime.now().millisecondsSinceEpoch.remainder(100000),
      title,
      body,
      notificationDetails,
      payload: payload,
    );
  }

  /// إشعار مجموعة
  static Future<void> showGroupedNotification({
    required String groupKey,
    required String groupTitle,
    required List<NotificationItem> notifications,
  }) async {
    // إرسال الإشعارات الفردية
    for (int i = 0; i < notifications.length; i++) {
      final notification = notifications[i];
      final AndroidNotificationDetails androidDetails =
          AndroidNotificationDetails(
            'grouped_channel',
            'Grouped Notifications',
            channelDescription: 'إشعارات مجمعة',
            importance: Importance.high,
            priority: Priority.high,
            groupKey: groupKey,
          );

      final NotificationDetails notificationDetails = NotificationDetails(
        android: androidDetails,
      );

      await _notifications.show(
        i,
        notification.title,
        notification.body,
        notificationDetails,
        payload: notification.payload,
      );
    }

    // إرسال إشعار المجموعة
    final AndroidNotificationDetails groupAndroidDetails =
        AndroidNotificationDetails(
          'grouped_channel',
          'Grouped Notifications',
          channelDescription: 'إشعارات مجمعة',
          importance: Importance.high,
          priority: Priority.high,
          groupKey: groupKey,
          setAsGroupSummary: true,
          largeIcon: const DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
        );

    final NotificationDetails groupNotificationDetails = NotificationDetails(
      android: groupAndroidDetails,
    );

    await _notifications.show(
      notifications.length,
      groupTitle,
      '${notifications.length} إشعارات جديدة',
      groupNotificationDetails,
    );
  }

  /// إلغاء إشعار محدد
  static Future<void> cancelNotification(int id) async {
    await _notifications.cancel(id);
  }

  /// إلغاء جميع الإشعارات
  static Future<void> cancelAllNotifications() async {
    await _notifications.cancelAll();
  }

  /// الحصول على الإشعارات المعلقة
  static Future<List<PendingNotificationRequest>>
  getPendingNotifications() async {
    return await _notifications.pendingNotificationRequests();
  }

  /// الحصول على الإشعارات النشطة
  static Future<List<ActiveNotification>> getActiveNotifications() async {
    final List<ActiveNotification>? activeNotifications =
        await _notifications
            .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin
            >()
            ?.getActiveNotifications();
    return activeNotifications ?? [];
  }

  /// إنشاء تفاصيل الإشعار حسب النوع
  static NotificationDetails _getNotificationDetails(NotificationType type) {
    Color color;
    String channelId;
    String channelName;
    String sound;

    switch (type) {
      case NotificationType.success:
        color = const Color(0xFF4CAF50);
        channelId = 'success_channel';
        channelName = 'Success Notifications';
        sound = 'success_sound';
        break;
      case NotificationType.error:
        color = const Color(0xFFF44336);
        channelId = 'error_channel';
        channelName = 'Error Notifications';
        sound = 'error_sound';
        break;
      case NotificationType.warning:
        color = const Color(0xFFFFC107);
        channelId = 'warning_channel';
        channelName = 'Warning Notifications';
        sound = 'warning_sound';
        break;
      case NotificationType.info:
        color = const Color(0xFF2196F3);
        channelId = 'info_channel';
        channelName = 'Info Notifications';
        sound = 'default_sound';
        break;
    }

    final AndroidNotificationDetails androidDetails =
        AndroidNotificationDetails(
          channelId,
          channelName,
          channelDescription: 'إشعارات ${channelName}',
          importance: Importance.high,
          priority: Priority.high,
          color: color,
          ledColor: color,
          ledOnMs: 1000,
          ledOffMs: 500,
          enableVibration: true,
          // vibrationPattern: Int64List.fromList([0, 1000, 500, 1000]),
          largeIcon: const DrawableResourceAndroidBitmap('@mipmap/ic_launcher'),
        );

    const DarwinNotificationDetails iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    return NotificationDetails(android: androidDetails, iOS: iosDetails);
  }

  /// إشعارات خاصة بالذكاء الاصطناعي
  static Future<void> showAINotification({
    required String title,
    required String body,
    AINotificationType aiType = AINotificationType.chatComplete,
  }) async {
    String emoji;
    NotificationType type;

    switch (aiType) {
      case AINotificationType.chatComplete:
        emoji = '💬';
        type = NotificationType.success;
        break;
      case AINotificationType.analysisComplete:
        emoji = '📊';
        type = NotificationType.success;
        break;
      case AINotificationType.planGenerated:
        emoji = '📋';
        type = NotificationType.success;
        break;
      case AINotificationType.imageGenerated:
        emoji = '🎨';
        type = NotificationType.success;
        break;
      case AINotificationType.error:
        emoji = '❌';
        type = NotificationType.error;
        break;
    }

    await showInstantNotification(
      title: '$emoji $title',
      body: body,
      type: type,
      payload: aiType.toString(),
    );
  }

  /// إشعار يومي للتذكير
  static Future<void> scheduleDailyReminder({
    required String title,
    required String body,
    required TimeOfDay time,
  }) async {
    final now = DateTime.now();
    var scheduledDate = DateTime(
      now.year,
      now.month,
      now.day,
      time.hour,
      time.minute,
    );

    // إذا كان الوقت قد مضى اليوم، جدوله للغد
    if (scheduledDate.isBefore(now)) {
      scheduledDate = scheduledDate.add(const Duration(days: 1));
    }

    await scheduleNotification(
      title: title,
      body: body,
      scheduledDate: scheduledDate,
      type: NotificationType.info,
    );
  }
}

/// أنواع الإشعارات
enum NotificationType { info, success, warning, error }

/// أنواع إشعارات الذكاء الاصطناعي
enum AINotificationType {
  chatComplete,
  analysisComplete,
  planGenerated,
  imageGenerated,
  error,
}

/// عنصر إشعار
class NotificationItem {
  final String title;
  final String body;
  final String? payload;

  NotificationItem({required this.title, required this.body, this.payload});
}

/// إجراء الإشعار
class NotificationAction {
  final String id;
  final String title;
  final String? icon;

  NotificationAction({required this.id, required this.title, this.icon});
}
