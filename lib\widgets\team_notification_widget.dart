import 'package:flutter/material.dart';
import '../utils/app_colors.dart';
import '../widgets/modern_ui_components.dart';
import '../core/collaboration/user_management_service.dart';

/// ويدجت إشعارات الفريق
class TeamNotificationWidget extends StatefulWidget {
  final String title;
  final String message;
  final TeamNotificationType type;
  final String? userId;
  final String? teamId;
  final String? workspaceId;
  final VoidCallback? onTap;
  final VoidCallback? onDismiss;
  final DateTime timestamp;

  const TeamNotificationWidget({
    super.key,
    required this.title,
    required this.message,
    required this.type,
    this.userId,
    this.teamId,
    this.workspaceId,
    this.onTap,
    this.onDismiss,
    required this.timestamp,
  });

  @override
  State<TeamNotificationWidget> createState() => _TeamNotificationWidgetState();
}

class _TeamNotificationWidgetState extends State<TeamNotificationWidget>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(1.0, 0.0),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _slideController,
      curve: Curves.easeOut,
    ));

    _fadeAnimation = Tween<double>(
      begin: 1.0,
      end: 0.0,
    ).animate(CurvedAnimation(
      parent: _fadeController,
      curve: Curves.easeIn,
    ));

    _slideController.forward();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: FadeTransition(
        opacity: _fadeAnimation,
        child: Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
          child: ModernUIComponents.glassCard(
            padding: const EdgeInsets.all(16),
            child: InkWell(
              onTap: widget.onTap,
              borderRadius: BorderRadius.circular(16),
              child: Row(
                children: [
                  _buildNotificationIcon(),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildNotificationContent(),
                  ),
                  const SizedBox(width: 8),
                  _buildNotificationActions(),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildNotificationIcon() {
    return Container(
      width: 40,
      height: 40,
      decoration: BoxDecoration(
        gradient: _getNotificationGradient(),
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: _getNotificationColor().withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Icon(
        _getNotificationIcon(),
        color: AppColors.white,
        size: 20,
      ),
    );
  }

  Widget _buildNotificationContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Expanded(
              child: Text(
                widget.title,
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
            ),
            Text(
              _formatTimestamp(widget.timestamp),
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 11,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        Text(
          widget.message,
          style: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 12,
          ),
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),
        if (widget.userId != null) ...[
          const SizedBox(height: 8),
          _buildUserInfo(),
        ],
      ],
    );
  }

  Widget _buildUserInfo() {
    final user = UserManagementService.allUsers
        .where((u) => u.id == widget.userId)
        .firstOrNull;
    
    if (user == null) return const SizedBox.shrink();

    return Row(
      children: [
        CircleAvatar(
          radius: 8,
          backgroundColor: AppColors.primaryPurple,
          child: Text(
            user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 8,
              fontWeight: FontWeight.bold,
            ),
          ),
        ),
        const SizedBox(width: 6),
        Text(
          user.name,
          style: TextStyle(
            color: AppColors.primaryPurple,
            fontSize: 11,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildNotificationActions() {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (widget.onDismiss != null)
          IconButton(
            icon: Icon(
              Icons.close,
              color: AppColors.textSecondary,
              size: 16,
            ),
            onPressed: _dismissNotification,
            padding: EdgeInsets.zero,
            constraints: const BoxConstraints(
              minWidth: 24,
              minHeight: 24,
            ),
          ),
        Icon(
          Icons.arrow_forward_ios,
          color: AppColors.textSecondary,
          size: 12,
        ),
      ],
    );
  }

  IconData _getNotificationIcon() {
    switch (widget.type) {
      case TeamNotificationType.teamInvitation:
        return Icons.group_add;
      case TeamNotificationType.workspaceCreated:
        return Icons.workspaces;
      case TeamNotificationType.projectAssigned:
        return Icons.assignment;
      case TeamNotificationType.documentShared:
        return Icons.share;
      case TeamNotificationType.messageReceived:
        return Icons.message;
      case TeamNotificationType.mentionReceived:
        return Icons.alternate_email;
      case TeamNotificationType.taskCompleted:
        return Icons.task_alt;
      case TeamNotificationType.deadlineReminder:
        return Icons.schedule;
      case TeamNotificationType.systemUpdate:
        return Icons.system_update;
    }
  }

  Color _getNotificationColor() {
    switch (widget.type) {
      case TeamNotificationType.teamInvitation:
        return AppColors.primaryPurple;
      case TeamNotificationType.workspaceCreated:
        return AppColors.electricBlue;
      case TeamNotificationType.projectAssigned:
        return AppColors.glowPink;
      case TeamNotificationType.documentShared:
        return AppColors.lightPurple;
      case TeamNotificationType.messageReceived:
        return AppColors.primaryPurple;
      case TeamNotificationType.mentionReceived:
        return AppColors.warning;
      case TeamNotificationType.taskCompleted:
        return AppColors.success;
      case TeamNotificationType.deadlineReminder:
        return AppColors.error;
      case TeamNotificationType.systemUpdate:
        return AppColors.info;
    }
  }

  Gradient _getNotificationGradient() {
    final color = _getNotificationColor();
    return LinearGradient(
      colors: [color, color.withValues(alpha: 0.8)],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes}د';
    } else if (difference.inHours < 24) {
      return '${difference.inHours}س';
    } else if (difference.inDays < 7) {
      return '${difference.inDays}ي';
    } else {
      return '${timestamp.day}/${timestamp.month}';
    }
  }

  void _dismissNotification() async {
    await _fadeController.forward();
    widget.onDismiss?.call();
  }
}

/// قائمة إشعارات الفريق
class TeamNotificationsList extends StatefulWidget {
  final List<TeamNotificationData> notifications;
  final Function(TeamNotificationData)? onNotificationTap;
  final Function(TeamNotificationData)? onNotificationDismiss;
  final bool showHeader;

  const TeamNotificationsList({
    super.key,
    required this.notifications,
    this.onNotificationTap,
    this.onNotificationDismiss,
    this.showHeader = true,
  });

  @override
  State<TeamNotificationsList> createState() => _TeamNotificationsListState();
}

class _TeamNotificationsListState extends State<TeamNotificationsList> {
  List<TeamNotificationData> _notifications = [];

  @override
  void initState() {
    super.initState();
    _notifications = List.from(widget.notifications);
  }

  @override
  void didUpdateWidget(TeamNotificationsList oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.notifications != oldWidget.notifications) {
      setState(() {
        _notifications = List.from(widget.notifications);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_notifications.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showHeader) _buildHeader(),
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: _notifications.length,
          itemBuilder: (context, index) {
            final notification = _notifications[index];
            return TeamNotificationWidget(
              title: notification.title,
              message: notification.message,
              type: notification.type,
              userId: notification.userId,
              teamId: notification.teamId,
              workspaceId: notification.workspaceId,
              timestamp: notification.timestamp,
              onTap: () => widget.onNotificationTap?.call(notification),
              onDismiss: () => _dismissNotification(notification),
            );
          },
        ),
      ],
    );
  }

  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            'إشعارات الفريق',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (_notifications.isNotEmpty)
            TextButton(
              onPressed: _clearAllNotifications,
              child: Text(
                'مسح الكل',
                style: TextStyle(
                  color: AppColors.primaryPurple,
                  fontSize: 14,
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(40),
      child: Column(
        children: [
          Icon(
            Icons.notifications_none,
            size: 48,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'لا توجد إشعارات',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ستظهر إشعارات الفريق هنا',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _dismissNotification(TeamNotificationData notification) {
    setState(() {
      _notifications.remove(notification);
    });
    widget.onNotificationDismiss?.call(notification);
  }

  void _clearAllNotifications() {
    setState(() {
      _notifications.clear();
    });
    for (final notification in widget.notifications) {
      widget.onNotificationDismiss?.call(notification);
    }
  }
}

/// بيانات إشعار الفريق
class TeamNotificationData {
  final String id;
  final String title;
  final String message;
  final TeamNotificationType type;
  final String? userId;
  final String? teamId;
  final String? workspaceId;
  final DateTime timestamp;
  final bool isRead;

  TeamNotificationData({
    required this.id,
    required this.title,
    required this.message,
    required this.type,
    this.userId,
    this.teamId,
    this.workspaceId,
    required this.timestamp,
    this.isRead = false,
  });

  TeamNotificationData copyWith({
    String? id,
    String? title,
    String? message,
    TeamNotificationType? type,
    String? userId,
    String? teamId,
    String? workspaceId,
    DateTime? timestamp,
    bool? isRead,
  }) {
    return TeamNotificationData(
      id: id ?? this.id,
      title: title ?? this.title,
      message: message ?? this.message,
      type: type ?? this.type,
      userId: userId ?? this.userId,
      teamId: teamId ?? this.teamId,
      workspaceId: workspaceId ?? this.workspaceId,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'message': message,
      'type': type.toString(),
      'userId': userId,
      'teamId': teamId,
      'workspaceId': workspaceId,
      'timestamp': timestamp.toIso8601String(),
      'isRead': isRead,
    };
  }

  factory TeamNotificationData.fromMap(Map<String, dynamic> map) {
    return TeamNotificationData(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      message: map['message'] ?? '',
      type: TeamNotificationType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => TeamNotificationType.systemUpdate,
      ),
      userId: map['userId'],
      teamId: map['teamId'],
      workspaceId: map['workspaceId'],
      timestamp: DateTime.parse(map['timestamp']),
      isRead: map['isRead'] ?? false,
    );
  }
}

/// أنواع إشعارات الفريق
enum TeamNotificationType {
  teamInvitation,
  workspaceCreated,
  projectAssigned,
  documentShared,
  messageReceived,
  mentionReceived,
  taskCompleted,
  deadlineReminder,
  systemUpdate,
}
