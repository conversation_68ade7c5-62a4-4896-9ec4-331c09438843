# 📱 دليل تشغيل تطبيق DeepSeek على المحاكي

## 🚀 خطوات تشغيل التطبيق على المحاكي

### 1️⃣ **التحقق من متطلبات النظام**

#### ✅ المتطلبات الأساسية:
- **Flutter SDK 3.0+** مثبت ومضاف لمتغيرات البيئة
- **Android Studio** مع Android SDK
- **محاكي Android** أو جهاز Android متصل
- **8GB RAM** على الأقل (16GB مفضل)
- **20GB مساحة فارغة** على القرص الصلب

### 2️⃣ **التحقق من حالة Flutter**

افتح Command Prompt أو PowerShell وشغل:

```bash
flutter doctor -v
```

**النتيجة المطلوبة:**
```
✅ Flutter (Channel stable, 3.x.x)
✅ Android toolchain - develop for Android devices
✅ Chrome - develop for the web
✅ Visual Studio - develop for Windows
✅ Android Studio (version 2022.x)
✅ VS Code (version 1.x.x)
```

### 3️⃣ **إعداد المحاكي**

#### أ) عبر Android Studio:
1. افتح **Android Studio**
2. اذهب إلى **Tools > AVD Manager**
3. اضغط **Create Virtual Device**
4. اختر **Pixel 6** أو أي جهاز حديث
5. اختر **API Level 33** أو أحدث
6. اضغط **Finish**

#### ب) عبر سطر الأوامر:
```bash
# عرض المحاكيات المتاحة
flutter emulators

# تشغيل محاكي محدد
flutter emulators --launch <emulator_id>
```

### 4️⃣ **تحضير المشروع**

في مجلد المشروع، شغل الأوامر التالية:

```bash
# تحديث التبعيات
flutter pub get

# تنظيف المشروع
flutter clean

# إعادة تحديث التبعيات
flutter pub get

# فحص المشروع
flutter analyze
```

### 5️⃣ **تشغيل التطبيق**

#### الطريقة الأولى - تشغيل مباشر:
```bash
flutter run
```

#### الطريقة الثانية - اختيار الجهاز:
```bash
# عرض الأجهزة المتاحة
flutter devices

# تشغيل على جهاز محدد
flutter run -d <device_id>
```

#### الطريقة الثالثة - تشغيل في وضع Debug:
```bash
flutter run --debug
```

### 6️⃣ **حل المشاكل الشائعة**

#### ❌ مشكلة: "No devices found"
**الحل:**
```bash
# تشغيل محاكي جديد
flutter emulators --launch Pixel_6_API_33

# أو تشغيل محاكي افتراضي
flutter emulators --launch @Pixel_6_API_33
```

#### ❌ مشكلة: "Gradle build failed"
**الحل:**
```bash
cd android
./gradlew clean
cd ..
flutter clean
flutter pub get
flutter run
```

#### ❌ مشكلة: "Flutter SDK not found"
**الحل:**
1. تأكد من تثبيت Flutter SDK
2. أضف مسار Flutter لمتغيرات البيئة:
   ```
   PATH = C:\flutter\bin
   ```

#### ❌ مشكلة: "Android licenses not accepted"
**الحل:**
```bash
flutter doctor --android-licenses
# اضغط 'y' لقبول جميع التراخيص
```

### 7️⃣ **تشغيل التطبيق خطوة بخطوة**

#### الخطوة 1: فتح Terminal
```bash
# انتقل لمجلد المشروع
cd C:\Users\<USER>\OneDrive\Desktop\deepseek_project
```

#### الخطوة 2: التحقق من الحالة
```bash
flutter doctor
```

#### الخطوة 3: تحديث التبعيات
```bash
flutter pub get
```

#### الخطوة 4: عرض الأجهزة
```bash
flutter devices
```

#### الخطوة 5: تشغيل التطبيق
```bash
flutter run
```

### 8️⃣ **أوامر مفيدة أثناء التشغيل**

عندما يكون التطبيق يعمل، يمكنك استخدام:

- **r** - إعادة تحميل سريع (Hot Reload)
- **R** - إعادة تشغيل كامل (Hot Restart)
- **h** - عرض المساعدة
- **d** - فصل التطبيق
- **q** - إنهاء التطبيق

### 9️⃣ **مواصفات الأداء المتوقعة**

#### 📊 على المحاكي:
- **وقت البدء**: 10-30 ثانية
- **استهلاك RAM**: 200-500 MB
- **معدل الإطارات**: 30-60 FPS
- **حجم التطبيق**: ~50 MB

#### 📱 على الجهاز الحقيقي:
- **وقت البدء**: 3-10 ثوان
- **استهلاك RAM**: 100-200 MB
- **معدل الإطارات**: 60 FPS
- **حجم التطبيق**: ~30 MB

### 🔟 **ميزات التطبيق المتاحة للاختبار**

#### 🤖 الذكاء الاصطناعي:
- محادثة تفاعلية مع AI
- أدوات ذكية متنوعة
- معالجة النصوص والصور

#### 🎨 واجهة المستخدم:
- تصميم حديث ومتجاوب
- ألوان متدرجة جذابة
- رسوم متحركة سلسة

#### ☁️ الخدمات السحابية:
- تخزين سحابي
- مزامنة البيانات
- إشعارات فورية

#### 👥 التعاون:
- مساحات عمل
- دردشة جماعية
- مشاركة الملفات

#### 📊 التحليلات:
- تحليلات ذكية
- لوحات معلومات
- تقارير مفصلة

### 1️⃣1️⃣ **نصائح للحصول على أفضل أداء**

#### ⚡ تحسين الأداء:
- استخدم جهاز حقيقي بدلاً من المحاكي
- أغلق التطبيقات الأخرى أثناء التطوير
- استخدم SSD بدلاً من HDD
- تأكد من وجود ذاكرة كافية

#### 🔧 إعدادات المحاكي:
- **RAM**: 4GB على الأقل
- **Storage**: 8GB على الأقل
- **Graphics**: Hardware - GLES 2.0
- **Multi-Core CPU**: مفعل

### 1️⃣2️⃣ **استكشاف الأخطاء**

#### 🔍 سجلات مفيدة:
```bash
# عرض سجلات مفصلة
flutter run --verbose

# عرض سجلات الجهاز
flutter logs

# فحص الأخطاء
flutter analyze
```

#### 📞 الحصول على المساعدة:
- **التوثيق الرسمي**: https://docs.flutter.dev
- **مجتمع Flutter**: https://flutter.dev/community
- **Stack Overflow**: البحث عن "Flutter"

---

## 🎉 **مبروك! التطبيق جاهز للتشغيل!**

بعد اتباع هذه الخطوات، ستتمكن من تشغيل تطبيق DeepSeek الذكي على المحاكي والاستمتاع بجميع ميزاته المتقدمة!

**📱 استمتع بتجربة التطبيق!**
