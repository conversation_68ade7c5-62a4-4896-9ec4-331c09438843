import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/offline/offline_service.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';

/// مؤشر حالة الاتصال
class ConnectionStatusWidget extends ConsumerStatefulWidget {
  const ConnectionStatusWidget({super.key});

  @override
  ConsumerState<ConnectionStatusWidget> createState() => _ConnectionStatusWidgetState();
}

class _ConnectionStatusWidgetState extends ConsumerState<ConnectionStatusWidget>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  bool _isVisible = false;

  @override
  void initState() {
    super.initState();
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );
    
    _checkConnectionStatus();
    _startPeriodicCheck();
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  void _checkConnectionStatus() {
    final isOnline = OfflineService.isOnline;
    setState(() {
      _isVisible = !isOnline;
    });

    if (!isOnline) {
      _pulseController.repeat(reverse: true);
    } else {
      _pulseController.stop();
    }
  }

  void _startPeriodicCheck() {
    // فحص دوري كل 5 ثوان
    Future.delayed(const Duration(seconds: 5), () {
      if (mounted) {
        _checkConnectionStatus();
        _startPeriodicCheck();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    if (!_isVisible) return const SizedBox.shrink();

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            margin: const EdgeInsets.all(8),
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: AppColors.warning.withOpacity(0.9),
              borderRadius: BorderRadius.circular(20),
              boxShadow: [
                BoxShadow(
                  color: AppColors.warning.withOpacity(0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.wifi_off,
                  color: AppColors.white,
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  'وضع عدم الاتصال',
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }
}

/// مؤشر مفصل لحالة الاتصال
class DetailedConnectionStatus extends ConsumerWidget {
  const DetailedConnectionStatus({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final stats = OfflineService.getStats();

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: stats.isOnline 
              ? AppColors.success.withOpacity(0.3)
              : AppColors.warning.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                stats.isOnline ? Icons.wifi : Icons.wifi_off,
                color: stats.isOnline ? AppColors.success : AppColors.warning,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                stats.isOnline ? 'متصل بالإنترنت' : 'غير متصل',
                style: AppTextStyles.heading.copyWith(
                  color: stats.isOnline ? AppColors.success : AppColors.warning,
                  fontSize: 18,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          _buildStatRow(
            'الردود المحفوظة',
            '${stats.cachedResponsesCount}',
            Icons.storage,
          ),
          
          _buildStatRow(
            'المهام المعلقة',
            '${stats.pendingTasksCount}',
            Icons.pending_actions,
          ),
          
          _buildStatRow(
            'حجم الذاكرة المؤقتة',
            stats.cacheSizeFormatted,
            Icons.memory,
          ),
          
          if (!stats.isOnline) ...[
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.warning.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    color: AppColors.warning,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'أنت تعمل في وضع عدم الاتصال. بعض الميزات محدودة.',
                      style: AppTextStyles.caption.copyWith(
                        color: AppColors.warning,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, color: AppColors.lightPurple, size: 18),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              label,
              style: AppTextStyles.body.copyWith(color: AppColors.white),
            ),
          ),
          Text(
            value,
            style: AppTextStyles.body.copyWith(
              color: AppColors.primaryPurple,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
