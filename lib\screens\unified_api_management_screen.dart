import 'package:flutter/material.dart';
import '../core/services/unified_api_gateway.dart';
import '../core/services/api_provider_service.dart';
import '../core/services/real_tools_service.dart';
import '../core/models/api_provider_model.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../widgets/animated_button.dart';

/// شاشة إدارة APIs الموحدة
class UnifiedApiManagementScreen extends StatefulWidget {
  const UnifiedApiManagementScreen({super.key});

  @override
  State<UnifiedApiManagementScreen> createState() =>
      _UnifiedApiManagementScreenState();
}

class _UnifiedApiManagementScreenState extends State<UnifiedApiManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  List<ApiProvider> _providers = [];
  List<AvailableModel> _models = [];
  ApiProvider? _activeProvider;
  AvailableModel? _activeModel;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      await UnifiedApiGateway.initialize();
      _providers = UnifiedApiGateway.getAvailableProviders();
      _activeProvider = UnifiedApiGateway.getActiveProvider();
      _activeModel = UnifiedApiGateway.getActiveModel();
      _models = UnifiedApiGateway.getAvailableModels();

      // تحديث حالة الأدوات عند تحميل البيانات
      await RealToolsService.refreshToolsAvailability();
    } catch (e) {
      _showError('خطأ في تحميل البيانات: $e');
    }

    setState(() => _isLoading = false);
  }

  Future<void> _refreshModels() async {
    setState(() => _isLoading = true);

    try {
      await UnifiedApiGateway.refreshAllModels();
      _models = UnifiedApiGateway.getAvailableModels();

      // تحديث حالة الأدوات بعد تحديث النماذج
      await RealToolsService.refreshToolsAvailability();

      _showSuccess('تم تحديث النماذج والأدوات بنجاح');
    } catch (e) {
      _showError('خطأ في تحديث النماذج: $e');
    }

    setState(() => _isLoading = false);
  }

  Future<void> _setActiveProvider(String providerId, String? modelId) async {
    try {
      await UnifiedApiGateway.setActiveProvider(providerId, modelId: modelId);

      // تحديث حالة الأدوات بعد تغيير المقدم
      await RealToolsService.refreshToolsAvailability();

      await _loadData();
      _showSuccess('تم تعيين المقدم النشط وتحديث الأدوات');
    } catch (e) {
      _showError('خطأ في تعيين المقدم: $e');
    }
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.darkPurple.withValues(alpha: 0.2),
              AppColors.background,
              AppColors.primaryPurple.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: Column(
          children: [
            _buildHeader(),
            _buildTabBar(),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _buildProvidersTab(),
                  _buildModelsTab(),
                  _buildStatsTab(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 60, 24, 24),
      child: Row(
        children: [
          AnimatedButton(
            onTap: () => Navigator.pop(context),
            gradient: LinearGradient(
              colors: [
                AppColors.darkGrey.withValues(alpha: 0.5),
                AppColors.midGrey.withValues(alpha: 0.5),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            padding: const EdgeInsets.all(12),
            child: const Icon(
              Icons.arrow_back_ios_new,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'بوابة APIs الموحدة',
                  style: AppTextStyles.heading.copyWith(
                    color: Colors.white,
                    fontSize: 24,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'إدارة مقدمي الخدمة والنماذج',
                  style: AppTextStyles.body.copyWith(color: Colors.white70),
                ),
              ],
            ),
          ),
          AnimatedButton(
            onTap: _isLoading ? null : _refreshModels,
            gradient: LinearGradient(
              colors: [AppColors.primaryPurple, AppColors.lightPurple],
            ),
            borderRadius: BorderRadius.circular(12),
            padding: const EdgeInsets.all(12),
            child:
                _isLoading
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                    : const Icon(Icons.refresh, color: Colors.white, size: 20),
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 24),
      decoration: BoxDecoration(
        color: AppColors.darkGrey,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.lightGrey),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: LinearGradient(
            colors: [AppColors.primaryPurple, AppColors.lightPurple],
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white60,
        labelStyle: AppTextStyles.body.copyWith(fontWeight: FontWeight.bold),
        unselectedLabelStyle: AppTextStyles.body,
        tabs: const [
          Tab(icon: Icon(Icons.api), text: 'المقدمين'),
          Tab(icon: Icon(Icons.model_training), text: 'النماذج'),
          Tab(icon: Icon(Icons.analytics), text: 'الإحصائيات'),
        ],
      ),
    );
  }

  Widget _buildProvidersTab() {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.api, color: AppColors.primaryPurple, size: 24),
              const SizedBox(width: 8),
              Text(
                'مقدمو الخدمة',
                style: AppTextStyles.heading.copyWith(
                  color: Colors.white,
                  fontSize: 20,
                ),
              ),
              const Spacer(),
              if (_activeProvider != null)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [AppColors.primaryPurple, AppColors.lightPurple],
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'النشط: ${_activeProvider!.displayName}',
                    style: AppTextStyles.caption.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : ListView.builder(
                      itemCount: _providers.length,
                      itemBuilder: (context, index) {
                        final provider = _providers[index];
                        final isActive = _activeProvider?.id == provider.id;

                        return _buildProviderCard(provider, isActive);
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildProviderCard(ApiProvider provider, bool isActive) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors:
              isActive
                  ? [
                    AppColors.primaryPurple.withValues(alpha: 0.3),
                    AppColors.lightPurple.withValues(alpha: 0.2),
                  ]
                  : [AppColors.darkGrey, AppColors.midGrey],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isActive ? AppColors.primaryPurple : AppColors.lightGrey,
          width: isActive ? 2 : 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getProviderColor(
                      provider.type,
                    ).withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getProviderIcon(provider.type),
                    color: _getProviderColor(provider.type),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        provider.displayName,
                        style: AppTextStyles.body.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        provider.description ?? 'لا يوجد وصف',
                        style: AppTextStyles.caption.copyWith(
                          color: Colors.white70,
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: provider.isWorking ? Colors.green : Colors.red,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        provider.isWorking ? 'متصل' : 'غير متصل',
                        style: AppTextStyles.caption.copyWith(
                          color: Colors.white,
                          fontSize: 10,
                        ),
                      ),
                    ),
                    if (isActive) ...[
                      const SizedBox(width: 8),
                      Icon(
                        Icons.check_circle,
                        color: AppColors.primaryPurple,
                        size: 20,
                      ),
                    ],
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Row(
              children: [
                Expanded(
                  child: Text(
                    'النماذج المتاحة: ${provider.supportedModels.length}',
                    style: AppTextStyles.caption.copyWith(
                      color: Colors.white60,
                    ),
                  ),
                ),
                if (!isActive && provider.isWorking)
                  AnimatedButton(
                    onTap: () => _setActiveProvider(provider.id, null),
                    gradient: LinearGradient(
                      colors: [AppColors.primaryPurple, AppColors.lightPurple],
                    ),
                    borderRadius: BorderRadius.circular(8),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 6,
                    ),
                    child: Text(
                      'تفعيل',
                      style: AppTextStyles.caption.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildModelsTab() {
    final providerModels = <String, List<AvailableModel>>{};

    // تجميع النماذج حسب المقدم
    for (final model in _models) {
      final provider = _providers.firstWhere(
        (p) => p.id == model.providerId,
        orElse:
            () => ApiProvider.create(
              name: 'unknown',
              displayName: 'غير معروف',
              baseUrl: '',
              type: ApiProviderType.openai,
            ),
      );

      if (!providerModels.containsKey(provider.displayName)) {
        providerModels[provider.displayName] = [];
      }
      providerModels[provider.displayName]!.add(model);
    }

    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.model_training,
                color: AppColors.primaryPurple,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'النماذج المتاحة',
                style: AppTextStyles.heading.copyWith(
                  color: Colors.white,
                  fontSize: 20,
                ),
              ),
              const Spacer(),
              if (_activeModel != null)
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [AppColors.primaryPurple, AppColors.lightPurple],
                    ),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    'النشط: ${_activeModel!.displayName}',
                    style: AppTextStyles.caption.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : ListView.builder(
                      itemCount: providerModels.keys.length,
                      itemBuilder: (context, index) {
                        final providerName = providerModels.keys.elementAt(
                          index,
                        );
                        final models = providerModels[providerName]!;

                        return _buildProviderModelsSection(
                          providerName,
                          models,
                        );
                      },
                    ),
          ),
        ],
      ),
    );
  }

  Widget _buildProviderModelsSection(
    String providerName,
    List<AvailableModel> models,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: 24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                colors: [AppColors.darkGrey, AppColors.midGrey],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Row(
              children: [
                Text(
                  providerName,
                  style: AppTextStyles.body.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: AppColors.primaryPurple.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${models.length} نموذج',
                    style: AppTextStyles.caption.copyWith(color: Colors.white),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          ...models.map((model) => _buildModelCard(model)),
        ],
      ),
    );
  }

  Widget _buildModelCard(AvailableModel model) {
    final isActive = _activeModel?.id == model.id;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors:
              isActive
                  ? [
                    AppColors.primaryPurple.withValues(alpha: 0.3),
                    AppColors.lightPurple.withValues(alpha: 0.2),
                  ]
                  : [
                    AppColors.darkGrey.withValues(alpha: 0.5),
                    AppColors.midGrey.withValues(alpha: 0.3),
                  ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              isActive
                  ? AppColors.primaryPurple
                  : AppColors.lightGrey.withValues(alpha: 0.3),
          width: isActive ? 2 : 1,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        title: Text(
          model.displayName,
          style: AppTextStyles.body.copyWith(
            color: Colors.white,
            fontWeight: isActive ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'المعرف: ${model.id}',
              style: AppTextStyles.caption.copyWith(color: Colors.white60),
            ),
            if (model.maxTokens != null)
              Text(
                'الحد الأقصى للرموز: ${model.maxTokens}',
                style: AppTextStyles.caption.copyWith(color: Colors.white60),
              ),
          ],
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (model.capabilities.isNotEmpty)
              ...model.capabilities.entries.map((capability) {
                if (capability.value == true) {
                  return Container(
                    margin: const EdgeInsets.only(left: 4),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 6,
                      vertical: 2,
                    ),
                    decoration: BoxDecoration(
                      color: _getCapabilityColor(capability.key),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _getCapabilityLabel(capability.key),
                      style: AppTextStyles.caption.copyWith(
                        color: Colors.white,
                        fontSize: 10,
                      ),
                    ),
                  );
                }
                return const SizedBox.shrink();
              }),
            const SizedBox(width: 8),
            if (isActive)
              Icon(Icons.check_circle, color: AppColors.primaryPurple, size: 20)
            else
              AnimatedButton(
                onTap: () {
                  final provider = _providers.firstWhere(
                    (p) => p.id == model.providerId,
                  );
                  _setActiveProvider(provider.id, model.id);
                },
                gradient: LinearGradient(
                  colors: [AppColors.primaryPurple, AppColors.lightPurple],
                ),
                borderRadius: BorderRadius.circular(6),
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                child: Text(
                  'تفعيل',
                  style: AppTextStyles.caption.copyWith(
                    color: Colors.white,
                    fontSize: 10,
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatsTab() {
    final stats = UnifiedApiGateway.getUsageStats();

    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: AppColors.primaryPurple, size: 24),
              const SizedBox(width: 8),
              Text(
                'إحصائيات الاستخدام',
                style: AppTextStyles.heading.copyWith(
                  color: Colors.white,
                  fontSize: 20,
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          Expanded(
            child: GridView.count(
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              children: [
                _buildStatCard(
                  'المقدم النشط',
                  stats['active_provider'] ?? 'غير محدد',
                  Icons.api,
                  AppColors.primaryPurple,
                ),
                _buildStatCard(
                  'النموذج النشط',
                  stats['active_model'] ?? 'غير محدد',
                  Icons.model_training,
                  AppColors.lightPurple,
                ),
                _buildStatCard(
                  'إجمالي المقدمين',
                  '${stats['total_providers']}',
                  Icons.dns,
                  Colors.blue,
                ),
                _buildStatCard(
                  'المقدمين النشطين',
                  '${stats['active_providers']}',
                  Icons.check_circle,
                  Colors.green,
                ),
                _buildStatCard(
                  'إجمالي النماذج',
                  '${stats['total_models']}',
                  Icons.storage,
                  Colors.orange,
                ),
                _buildStatCard(
                  'النماذج المتاحة',
                  '${stats['available_models']}',
                  Icons.verified,
                  Colors.teal,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.darkGrey, AppColors.midGrey],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.lightGrey),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: color, size: 24),
            ),
            const SizedBox(height: 12),
            Text(
              value,
              style: AppTextStyles.heading.copyWith(
                color: Colors.white,
                fontSize: 18,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: AppTextStyles.caption.copyWith(color: Colors.white70),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Color _getProviderColor(ApiProviderType type) {
    switch (type) {
      case ApiProviderType.openai:
        return Colors.green;
      case ApiProviderType.openrouter:
        return Colors.purple;
      case ApiProviderType.gemini:
        return Colors.blue;
      case ApiProviderType.anthropic:
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getProviderIcon(ApiProviderType type) {
    switch (type) {
      case ApiProviderType.openai:
        return Icons.psychology;
      case ApiProviderType.openrouter:
        return Icons.router;
      case ApiProviderType.gemini:
        return Icons.diamond;
      case ApiProviderType.anthropic:
        return Icons.android;
      default:
        return Icons.api;
    }
  }

  Color _getCapabilityColor(String capability) {
    switch (capability.toLowerCase()) {
      case 'text':
        return Colors.blue;
      case 'vision':
        return Colors.green;
      case 'code':
        return Colors.orange;
      case 'function_calling':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  String _getCapabilityLabel(String capability) {
    switch (capability.toLowerCase()) {
      case 'text':
        return 'نص';
      case 'vision':
        return 'رؤية';
      case 'code':
        return 'كود';
      case 'function_calling':
        return 'وظائف';
      default:
        return capability;
    }
  }
}
