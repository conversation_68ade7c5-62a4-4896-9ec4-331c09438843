import 'package:equatable/equatable.dart';

/// نموذج المستخدم
class UserModel extends Equatable {
  final String id;
  final String name;
  final String email;
  final String? avatar;
  final DateTime createdAt;
  final DateTime lastLoginAt;
  final UserPreferences preferences;
  final UserStats stats;

  const UserModel({
    required this.id,
    required this.name,
    required this.email,
    this.avatar,
    required this.createdAt,
    required this.lastLoginAt,
    required this.preferences,
    required this.stats,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      email: json['email'] ?? '',
      avatar: json['avatar'],
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      lastLoginAt: DateTime.parse(json['last_login_at'] ?? DateTime.now().toIso8601String()),
      preferences: UserPreferences.fromJson(json['preferences'] ?? {}),
      stats: UserStats.fromJson(json['stats'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'avatar': avatar,
      'created_at': createdAt.toIso8601String(),
      'last_login_at': lastLoginAt.toIso8601String(),
      'preferences': preferences.toJson(),
      'stats': stats.toJson(),
    };
  }

  UserModel copyWith({
    String? id,
    String? name,
    String? email,
    String? avatar,
    DateTime? createdAt,
    DateTime? lastLoginAt,
    UserPreferences? preferences,
    UserStats? stats,
  }) {
    return UserModel(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      avatar: avatar ?? this.avatar,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
      preferences: preferences ?? this.preferences,
      stats: stats ?? this.stats,
    );
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        avatar,
        createdAt,
        lastLoginAt,
        preferences,
        stats,
      ];
}

/// تفضيلات المستخدم
class UserPreferences extends Equatable {
  final String preferredLanguage;
  final String preferredModel;
  final double defaultTemperature;
  final int defaultMaxTokens;
  final bool enableAutoSave;
  final bool enableNotifications;

  const UserPreferences({
    required this.preferredLanguage,
    required this.preferredModel,
    required this.defaultTemperature,
    required this.defaultMaxTokens,
    required this.enableAutoSave,
    required this.enableNotifications,
  });

  factory UserPreferences.defaultPreferences() {
    return const UserPreferences(
      preferredLanguage: 'ar',
      preferredModel: 'gpt-3.5-turbo',
      defaultTemperature: 0.7,
      defaultMaxTokens: 2048,
      enableAutoSave: true,
      enableNotifications: true,
    );
  }

  factory UserPreferences.fromJson(Map<String, dynamic> json) {
    return UserPreferences(
      preferredLanguage: json['preferred_language'] ?? 'ar',
      preferredModel: json['preferred_model'] ?? 'gpt-3.5-turbo',
      defaultTemperature: (json['default_temperature'] ?? 0.7).toDouble(),
      defaultMaxTokens: json['default_max_tokens'] ?? 2048,
      enableAutoSave: json['enable_auto_save'] ?? true,
      enableNotifications: json['enable_notifications'] ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'preferred_language': preferredLanguage,
      'preferred_model': preferredModel,
      'default_temperature': defaultTemperature,
      'default_max_tokens': defaultMaxTokens,
      'enable_auto_save': enableAutoSave,
      'enable_notifications': enableNotifications,
    };
  }

  UserPreferences copyWith({
    String? preferredLanguage,
    String? preferredModel,
    double? defaultTemperature,
    int? defaultMaxTokens,
    bool? enableAutoSave,
    bool? enableNotifications,
  }) {
    return UserPreferences(
      preferredLanguage: preferredLanguage ?? this.preferredLanguage,
      preferredModel: preferredModel ?? this.preferredModel,
      defaultTemperature: defaultTemperature ?? this.defaultTemperature,
      defaultMaxTokens: defaultMaxTokens ?? this.defaultMaxTokens,
      enableAutoSave: enableAutoSave ?? this.enableAutoSave,
      enableNotifications: enableNotifications ?? this.enableNotifications,
    );
  }

  @override
  List<Object?> get props => [
        preferredLanguage,
        preferredModel,
        defaultTemperature,
        defaultMaxTokens,
        enableAutoSave,
        enableNotifications,
      ];
}

/// إحصائيات المستخدم
class UserStats extends Equatable {
  final int totalConversations;
  final int totalMessages;
  final int totalTokensUsed;
  final int totalImagesGenerated;
  final int totalTextsAnalyzed;
  final int totalPlansCreated;
  final DateTime lastActivityAt;

  const UserStats({
    required this.totalConversations,
    required this.totalMessages,
    required this.totalTokensUsed,
    required this.totalImagesGenerated,
    required this.totalTextsAnalyzed,
    required this.totalPlansCreated,
    required this.lastActivityAt,
  });

  factory UserStats.initial() {
    return UserStats(
      totalConversations: 0,
      totalMessages: 0,
      totalTokensUsed: 0,
      totalImagesGenerated: 0,
      totalTextsAnalyzed: 0,
      totalPlansCreated: 0,
      lastActivityAt: DateTime.now(),
    );
  }

  factory UserStats.fromJson(Map<String, dynamic> json) {
    return UserStats(
      totalConversations: json['total_conversations'] ?? 0,
      totalMessages: json['total_messages'] ?? 0,
      totalTokensUsed: json['total_tokens_used'] ?? 0,
      totalImagesGenerated: json['total_images_generated'] ?? 0,
      totalTextsAnalyzed: json['total_texts_analyzed'] ?? 0,
      totalPlansCreated: json['total_plans_created'] ?? 0,
      lastActivityAt: DateTime.parse(json['last_activity_at'] ?? DateTime.now().toIso8601String()),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_conversations': totalConversations,
      'total_messages': totalMessages,
      'total_tokens_used': totalTokensUsed,
      'total_images_generated': totalImagesGenerated,
      'total_texts_analyzed': totalTextsAnalyzed,
      'total_plans_created': totalPlansCreated,
      'last_activity_at': lastActivityAt.toIso8601String(),
    };
  }

  UserStats copyWith({
    int? totalConversations,
    int? totalMessages,
    int? totalTokensUsed,
    int? totalImagesGenerated,
    int? totalTextsAnalyzed,
    int? totalPlansCreated,
    DateTime? lastActivityAt,
  }) {
    return UserStats(
      totalConversations: totalConversations ?? this.totalConversations,
      totalMessages: totalMessages ?? this.totalMessages,
      totalTokensUsed: totalTokensUsed ?? this.totalTokensUsed,
      totalImagesGenerated: totalImagesGenerated ?? this.totalImagesGenerated,
      totalTextsAnalyzed: totalTextsAnalyzed ?? this.totalTextsAnalyzed,
      totalPlansCreated: totalPlansCreated ?? this.totalPlansCreated,
      lastActivityAt: lastActivityAt ?? this.lastActivityAt,
    );
  }

  @override
  List<Object?> get props => [
        totalConversations,
        totalMessages,
        totalTokensUsed,
        totalImagesGenerated,
        totalTextsAnalyzed,
        totalPlansCreated,
        lastActivityAt,
      ];
}
