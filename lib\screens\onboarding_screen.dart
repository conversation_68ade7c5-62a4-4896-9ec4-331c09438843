import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../widgets/deepseek_logo.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../utils/app_animations.dart';

class OnboardingScreen extends StatefulWidget {
  const OnboardingScreen({Key? key}) : super(key: key);

  @override
  State<OnboardingScreen> createState() => _OnboardingScreenState();
}

class _OnboardingScreenState extends State<OnboardingScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _logoController;
  late Animation<double> _logoScale;
  int _currentPage = 0;

  final List<Map<String, String>> _pages = [
    {
      'title': 'اكتشف الذكاء مع DeepSeek AI',
      'desc': 'حلول ذكاء اصطناعي متقدمة تفتح لك آفاقاً جديدة.',
    },
    {
      'title': 'ابدأ رحلتك الذكية',
      'desc': 'تجربة مستخدم سلسة وواجهة عصرية تدعم العربية والإنجليزية.',
    },
    {
      'title': 'أمان وخصوصية',
      'desc': 'بياناتك محمية دائماً مع أحدث تقنيات الأمان.',
    },
  ];

  @override
  void initState() {
    super.initState();
    _logoController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);
    _logoScale = Tween<double>(begin: 0.95, end: 1.05).animate(
      CurvedAnimation(parent: _logoController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _logoController.dispose();
    super.dispose();
  }

  void _nextPage() {
    setState(() {
      if (_currentPage < _pages.length - 1) {
        _currentPage++;
      } else {
        Navigator.pushReplacementNamed(context, '/auth');
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const Spacer(flex: 2),
              AnimatedBuilder(
                animation: _logoScale,
                builder: (context, child) {
                  return Transform.scale(scale: _logoScale.value, child: child);
                },
                child: const DeepSeekLogo(size: 120),
              ),
              const SizedBox(height: 32),
              AppAnimations.fadeIn(
                child: Column(
                  children: [
                    Text(
                      _pages[_currentPage]['title']!,
                      style: AppTextStyles.heading1.copyWith(fontSize: 24),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      _pages[_currentPage]['desc']!,
                      style: AppTextStyles.body.copyWith(color: Colors.white70),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),
              const Spacer(flex: 3),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: List.generate(
                  _pages.length,
                  (index) => AnimatedContainer(
                    duration: const Duration(milliseconds: 300),
                    margin: const EdgeInsets.symmetric(horizontal: 4),
                    width: _currentPage == index ? 22 : 10,
                    height: 10,
                    decoration: BoxDecoration(
                      color:
                          _currentPage == index
                              ? AppColors.primaryPurple
                              : AppColors.darkGrey,
                      borderRadius: BorderRadius.circular(8),
                      boxShadow:
                          _currentPage == index
                              ? [
                                BoxShadow(
                                  color: AppColors.primaryPurple.withOpacity(
                                    0.4,
                                  ),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                ),
                              ]
                              : [],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 32),
              SizedBox(
                width: 64,
                height: 64,
                child: AnimatedButton(
                  onTap: _nextPage,
                  gradient: LinearGradient(
                    colors: [AppColors.primaryPurple, AppColors.lightPurple],
                  ),
                  borderRadius: BorderRadius.circular(32),
                  padding: const EdgeInsets.all(0),
                  child: Icon(
                    _currentPage == _pages.length - 1
                        ? Icons.check
                        : Icons.arrow_forward,
                    color: Colors.white,
                    size: 32,
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],
          ),
        ),
      ),
    );
  }
}
