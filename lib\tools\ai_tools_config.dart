/// إعدادات وتكوين أدوات الذكاء الاصطناعي
class AIToolsConfig {
  /// إعدادات إنشاء الصور
  static const imageGenerationConfig = {
    'defaultSize': '1024x1024',
    'defaultQuality': 'standard',
    'defaultStyle': 'vivid',
    'supportedSizes': [
      '256x256',
      '512x512',
      '1024x1024',
      '1792x1024',
      '1024x1792',
    ],
    'supportedQualities': ['standard', 'hd'],
    'supportedStyles': ['vivid', 'natural'],
    'maxPromptLength': 4000,
    'cacheEnabled': true,
    'cacheExpiry': 3600, // ثانية
  };

  /// إعدادات تلخيص النصوص
  static const textSummarizationConfig = {
    'defaultStyle': 'comprehensive',
    'defaultMaxLength': 300,
    'supportedStyles': ['comprehensive', 'brief', 'bullet_points', 'executive'],
    'minTextLength': 100,
    'maxTextLength': 50000,
    'supportedLanguages': ['ar', 'en'],
    'cacheEnabled': true,
    'cacheExpiry': 1800, // ثانية
  };

  /// إعدادات تحليل البيانات
  static const dataAnalysisConfig = {
    'defaultAnalysisType': 'comprehensive',
    'supportedAnalysisTypes': [
      'comprehensive',
      'statistical',
      'trend',
      'predictive',
      'comparative',
    ],
    'maxDataSize': 100000, // حرف
    'includeChartsDefault': false,
    'includeRecommendationsDefault': true,
    'cacheEnabled': true,
    'cacheExpiry': 2400, // ثانية
  };

  /// إعدادات إنشاء الخطط
  static const planCreationConfig = {
    'defaultTimeframeDays': 30,
    'defaultPriority': 'medium',
    'supportedPriorities': ['low', 'medium', 'high', 'urgent'],
    'minTimeframeDays': 1,
    'maxTimeframeDays': 365,
    'maxGoalLength': 1000,
    'maxConstraintsLength': 2000,
    'maxResourcesCount': 20,
    'cacheEnabled': true,
    'cacheExpiry': 3600, // ثانية
  };

  /// إعدادات مساعدة الكتابة
  static const writingAssistanceConfig = {
    'defaultStyle': 'creative',
    'defaultLanguage': 'ar',
    'defaultTargetLength': 500,
    'supportedStyles': [
      'creative',
      'professional',
      'academic',
      'technical',
      'casual',
      'persuasive',
    ],
    'supportedLanguages': ['ar', 'en'],
    'minTargetLength': 50,
    'maxTargetLength': 5000,
    'maxTopicLength': 500,
    'cacheEnabled': true,
    'cacheExpiry': 1800, // ثانية
  };

  /// إعدادات المحادثة الذكية
  static const smartChatConfig = {
    'defaultModel': 'gpt-3.5-turbo',
    'defaultTemperature': 0.7,
    'defaultMaxTokens': 2048,
    'supportedModels': ['gpt-3.5-turbo', 'gpt-4', 'gpt-4-turbo-preview'],
    'minTemperature': 0.0,
    'maxTemperature': 2.0,
    'minMaxTokens': 1,
    'maxMaxTokens': 4096,
    'maxContextMessages': 10,
    'cacheEnabled': false, // المحادثات لا تُخزن مؤقتاً
  };

  /// إعدادات عامة
  static const generalConfig = {
    'maxRetries': 3,
    'retryDelay': 2000, // ميلي ثانية
    'requestTimeout': 60000, // ميلي ثانية
    'enableLogging': true,
    'enableAnalytics': false,
    'enableErrorReporting': true,
    'cacheCleanupInterval': 3600, // ثانية
    'maxCacheSize': 100, // عدد العناصر
  };

  /// إعدادات الأمان
  static const securityConfig = {
    'enableInputValidation': true,
    'enableOutputFiltering': true,
    'maxInputLength': 100000,
    'blockedWords': [
      // قائمة الكلمات المحظورة
    ],
    'enableRateLimiting': true,
    'maxRequestsPerMinute': 60,
    'enableApiKeyRotation': false,
  };

  /// إعدادات واجهة المستخدم
  static const uiConfig = {
    'showProgressIndicators': true,
    'showTokenUsage': true,
    'showProcessingTime': true,
    'enableDarkMode': true,
    'animationDuration': 300, // ميلي ثانية
    'showSuccessMessages': true,
    'showErrorMessages': true,
    'autoSaveResults': true,
  };

  /// الحصول على إعدادات أداة محددة
  static Map<String, dynamic> getToolConfig(String toolId) {
    switch (toolId) {
      case 'image_generation':
        return Map<String, dynamic>.from(imageGenerationConfig);
      case 'text_summarization':
        return Map<String, dynamic>.from(textSummarizationConfig);
      case 'data_analysis':
        return Map<String, dynamic>.from(dataAnalysisConfig);
      case 'plan_creation':
        return Map<String, dynamic>.from(planCreationConfig);
      case 'writing_assistance':
        return Map<String, dynamic>.from(writingAssistanceConfig);
      case 'smart_chat':
        return Map<String, dynamic>.from(smartChatConfig);
      default:
        return {};
    }
  }

  /// التحقق من صحة المعاملات
  static bool validateImageGenerationParams({
    required String prompt,
    String? size,
    String? quality,
    String? style,
  }) {
    if (prompt.isEmpty ||
        prompt.length > (imageGenerationConfig['maxPromptLength']! as int)) {
      return false;
    }

    if (size != null &&
        !(imageGenerationConfig['supportedSizes']! as List<String>).contains(
          size,
        )) {
      return false;
    }

    if (quality != null &&
        !(imageGenerationConfig['supportedQualities']! as List<String>)
            .contains(quality)) {
      return false;
    }

    if (style != null &&
        !(imageGenerationConfig['supportedStyles']! as List<String>).contains(
          style,
        )) {
      return false;
    }

    return true;
  }

  static bool validateTextSummarizationParams({
    required String text,
    String? style,
    int? maxLength,
  }) {
    if (text.length < (textSummarizationConfig['minTextLength']! as int) ||
        text.length > (textSummarizationConfig['maxTextLength']! as int)) {
      return false;
    }

    if (style != null &&
        !(textSummarizationConfig['supportedStyles']! as List<String>).contains(
          style,
        )) {
      return false;
    }

    if (maxLength != null && (maxLength < 50 || maxLength > 2000)) {
      return false;
    }

    return true;
  }

  static bool validateDataAnalysisParams({
    required String data,
    String? analysisType,
  }) {
    if (data.isEmpty ||
        data.length > (dataAnalysisConfig['maxDataSize']! as int)) {
      return false;
    }

    if (analysisType != null &&
        !(dataAnalysisConfig['supportedAnalysisTypes']! as List<String>)
            .contains(analysisType)) {
      return false;
    }

    return true;
  }

  static bool validatePlanCreationParams({
    required String goal,
    int? timeframeDays,
    String? priority,
    List<String>? resources,
  }) {
    if (goal.isEmpty ||
        goal.length > (planCreationConfig['maxGoalLength']! as int)) {
      return false;
    }

    if (timeframeDays != null &&
        (timeframeDays < (planCreationConfig['minTimeframeDays']! as int) ||
            timeframeDays > (planCreationConfig['maxTimeframeDays']! as int))) {
      return false;
    }

    if (priority != null &&
        !(planCreationConfig['supportedPriorities']! as List<String>).contains(
          priority,
        )) {
      return false;
    }

    if (resources != null &&
        resources.length > (planCreationConfig['maxResourcesCount']! as int)) {
      return false;
    }

    return true;
  }

  static bool validateWritingAssistanceParams({
    required String topic,
    String? style,
    String? language,
    int? targetLength,
  }) {
    if (topic.isEmpty ||
        topic.length > (writingAssistanceConfig['maxTopicLength']! as int)) {
      return false;
    }

    if (style != null &&
        !(writingAssistanceConfig['supportedStyles']! as List<String>).contains(
          style,
        )) {
      return false;
    }

    if (language != null &&
        !(writingAssistanceConfig['supportedLanguages']! as List<String>)
            .contains(language)) {
      return false;
    }

    if (targetLength != null &&
        (targetLength < (writingAssistanceConfig['minTargetLength']! as int) ||
            targetLength >
                (writingAssistanceConfig['maxTargetLength']! as int))) {
      return false;
    }

    return true;
  }

  static bool validateSmartChatParams({
    required String message,
    String? model,
    double? temperature,
    int? maxTokens,
  }) {
    if (message.isEmpty ||
        message.length > (generalConfig['maxInputLength']! as int)) {
      return false;
    }

    if (model != null &&
        !(smartChatConfig['supportedModels']! as List<String>).contains(
          model,
        )) {
      return false;
    }

    if (temperature != null &&
        (temperature < (smartChatConfig['minTemperature']! as double) ||
            temperature > (smartChatConfig['maxTemperature']! as double))) {
      return false;
    }

    if (maxTokens != null &&
        (maxTokens < (smartChatConfig['minMaxTokens']! as int) ||
            maxTokens > (smartChatConfig['maxMaxTokens']! as int))) {
      return false;
    }

    return true;
  }

  /// الحصول على القيم الافتراضية لأداة محددة
  static Map<String, dynamic> getDefaultParams(String toolId) {
    switch (toolId) {
      case 'image_generation':
        return {
          'size': imageGenerationConfig['defaultSize'],
          'quality': imageGenerationConfig['defaultQuality'],
          'style': imageGenerationConfig['defaultStyle'],
        };
      case 'text_summarization':
        return {
          'style': textSummarizationConfig['defaultStyle'],
          'maxLength': textSummarizationConfig['defaultMaxLength'],
        };
      case 'data_analysis':
        return {
          'analysisType': dataAnalysisConfig['defaultAnalysisType'],
          'includeCharts': dataAnalysisConfig['includeChartsDefault'],
          'includeRecommendations':
              dataAnalysisConfig['includeRecommendationsDefault'],
        };
      case 'plan_creation':
        return {
          'timeframeDays': planCreationConfig['defaultTimeframeDays'],
          'priority': planCreationConfig['defaultPriority'],
        };
      case 'writing_assistance':
        return {
          'style': writingAssistanceConfig['defaultStyle'],
          'language': writingAssistanceConfig['defaultLanguage'],
          'targetLength': writingAssistanceConfig['defaultTargetLength'],
        };
      case 'smart_chat':
        return {
          'model': smartChatConfig['defaultModel'],
          'temperature': smartChatConfig['defaultTemperature'],
          'maxTokens': smartChatConfig['defaultMaxTokens'],
        };
      default:
        return {};
    }
  }
}
