import 'package:flutter/material.dart';

/// نظام الألوان الحديث المستلهم من التصميم الجديد
class AppColors {
  // الألوان الأساسية الجديدة - مستوحاة من التصميم المرسل
  static const Color primaryPurple = Color(0xFF6C5CE7); // بنفسجي عميق
  static const Color electricBlue = Color(0xFF00D4FF); // أزرق كهربائي
  static const Color lightPurple = Color(0xFFA855F7); // بنفسجي فاتح
  static const Color glowPink = Color(0xFFEC4899); // وردي متوهج
  static const Color darkPurple = Color(0xFF5A4FCF); // بنفسجي داكن
  static const Color background = Color(0xFF0F0F23); // أسود عميق
  static const Color white = Color(0xFFFFFFFF);
  static const Color black = Color(0xFF000000);
  static const Color darkGrey = Color(0xFF1A1A2E); // رمادي داكن
  static const Color cardBackground = Color(0xFF16213E); // خلفية الكروت
  static const Color lightGrey = Color(0xFF8E8E93); // رمادي فاتح
  static const Color midGrey = Color(0xFF2A2D47); // رمادي متوسط
  static const Color textSecondary = Color(0xFFB0B0B0); // نص ثانوي

  // ألوان الحالة
  static const Color error = Color(0xFFF44336);
  static const Color success = Color(0xFF4CAF50);
  static const Color warning = Color(0xFFFFC107);
  static const Color info = Color(0xFF2196F3);

  // ألوان إضافية مستلهمة من التصميم الجديد
  static const Color accentBlue = Color(0xFF3B82F6); // أزرق حيوي
  static const Color accentPink = Color(0xFFEC4899); // وردي جذاب
  static const Color accentGreen = Color(0xFF10B981); // أخضر حديث
  static const Color accentOrange = Color(0xFFF59E0B); // برتقالي دافئ
  static const Color accentRed = Color(0xFFEF4444); // أحمر حديث
  static const Color accentTeal = Color(0xFF06B6D4); // سماوي
  static const Color accentIndigo = Color(0xFF6366F1); // نيلي حديث

  // ألوان زجاجية للتأثيرات الحديثة
  static const Color glassBackground = Color(0x1AFFFFFF); // خلفية زجاجية
  static const Color glassBorder = Color(0x30FFFFFF); // حدود زجاجية
  static const Color cardBackground = Color(0xFF1A1B3A); // خلفية البطاقات
  static const Color mutedText = Color(0xFF9CA3AF); // نص خفيف

  // تدرجات لونية حديثة مستوحاة من التصميم الجديد
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primaryPurple, electricBlue],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient glowGradient = LinearGradient(
    colors: [lightPurple, glowPink],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient modernCardGradient = LinearGradient(
    colors: [darkGrey, midGrey],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // تدرج زجاجي (Glassmorphism)
  static const LinearGradient glassGradient = LinearGradient(
    colors: [
      Color(0x20FFFFFF),
      Color(0x10FFFFFF),
    ],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [Color(0xFF3B82F6), Color(0xFF06B6D4)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient accentGradient = LinearGradient(
    colors: [Color(0xFF10B981), Color(0xFF3B82F6)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient backgroundGradient = LinearGradient(
    colors: [Color(0xFF0F0F23), Color(0xFF1A1B3A)],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  static const LinearGradient glassGradient = LinearGradient(
    colors: [Color(0x20FFFFFF), Color(0x10FFFFFF)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // تدرجات خاصة للبطاقات
  static const LinearGradient cardGradient1 = LinearGradient(
    colors: [Color(0xFF8B5CF6), Color(0xFF3B82F6)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient cardGradient2 = LinearGradient(
    colors: [Color(0xFFEC4899), Color(0xFFF59E0B)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient cardGradient3 = LinearGradient(
    colors: [Color(0xFF06B6D4), Color(0xFF10B981)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient darkGradient = LinearGradient(
    colors: [darkPurple, background],
    begin: Alignment.topCenter,
    end: Alignment.bottomCenter,
  );

  static const LinearGradient greyGradient = LinearGradient(
    colors: [darkGrey, midGrey],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient blueGradient = LinearGradient(
    colors: [Color(0xFF6B7AED), accentIndigo],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient greenGradient = LinearGradient(
    colors: [accentGreen, Color(0xFF27AE60)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient orangeGradient = LinearGradient(
    colors: [accentOrange, Color(0xFFE67E22)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient redGradient = LinearGradient(
    colors: [Color(0xFFEC7063), accentRed],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient tealGradient = LinearGradient(
    colors: [accentTeal, Color(0xFF17A2B8)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  static const LinearGradient pinkGradient = LinearGradient(
    colors: [accentPink, Color(0xFFC44569)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );

  // تدرجات شعاعية
  static const RadialGradient primaryRadialGradient = RadialGradient(
    colors: [lightPurple, primaryPurple, darkPurple],
    center: Alignment.center,
    radius: 1.0,
  );

  // ألوان بشفافية
  static Color primaryWithOpacity(double opacity) =>
      primaryPurple.withValues(alpha: opacity);
  static Color lightPurpleWithOpacity(double opacity) =>
      lightPurple.withValues(alpha: opacity);
  static Color darkPurpleWithOpacity(double opacity) =>
      darkPurple.withValues(alpha: opacity);
  static Color whiteWithOpacity(double opacity) => white.withValues(alpha: opacity);

  // ظلال مخصصة
  static List<BoxShadow> primaryShadow = [
    BoxShadow(
      color: primaryPurple.withValues(alpha: 0.3),
      blurRadius: 20,
      offset: const Offset(0, 10),
    ),
  ];

  static List<BoxShadow> darkShadow = [
    BoxShadow(
      color: Colors.black.withValues(alpha: 0.3),
      blurRadius: 15,
      offset: const Offset(0, 5),
    ),
  ];

  static List<BoxShadow> glowShadow = [
    BoxShadow(
      color: lightPurple.withOpacity(0.5),
      blurRadius: 30,
      spreadRadius: 5,
    ),
  ];
}
