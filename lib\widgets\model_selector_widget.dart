import 'package:flutter/material.dart';
import '../core/services/unified_api_gateway.dart';
import '../core/models/api_provider_model.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import 'animated_button.dart';

/// ويدجت اختيار النموذج المحسن
class ModelSelectorWidget extends StatefulWidget {
  final Function(String providerId, String modelId)? onModelSelected;
  final bool showProviderInfo;
  final bool compactMode;

  const ModelSelectorWidget({
    super.key,
    this.onModelSelected,
    this.showProviderInfo = true,
    this.compactMode = false,
  });

  @override
  State<ModelSelectorWidget> createState() => _ModelSelectorWidgetState();
}

class _ModelSelectorWidgetState extends State<ModelSelectorWidget> {
  List<ApiProvider> _providers = [];
  List<AvailableModel> _models = [];
  ApiProvider? _selectedProvider;
  AvailableModel? _selectedModel;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    try {
      _providers = UnifiedApiGateway.getAvailableProviders();
      _selectedProvider = UnifiedApiGateway.getActiveProvider();
      _selectedModel = UnifiedApiGateway.getActiveModel();

      if (_selectedProvider != null) {
        _models = UnifiedApiGateway.getAvailableModels(_selectedProvider!.id);
      }
    } catch (e) {
      debugPrint('خطأ في تحميل البيانات: $e');
    }

    setState(() => _isLoading = false);
  }

  Future<void> _selectProvider(ApiProvider provider) async {
    setState(() {
      _selectedProvider = provider;
      _selectedModel = null;
      _isLoading = true;
    });

    try {
      _models = UnifiedApiGateway.getAvailableModels(provider.id);
      if (_models.isNotEmpty) {
        _selectedModel = _models.first;
        await UnifiedApiGateway.setActiveProvider(
          provider.id,
          modelId: _selectedModel!.id,
        );
        widget.onModelSelected?.call(provider.id, _selectedModel!.id);
      }
    } catch (e) {
      debugPrint('خطأ في اختيار المقدم: $e');
    }

    setState(() => _isLoading = false);
  }

  Future<void> _selectModel(AvailableModel model) async {
    if (_selectedProvider == null) return;

    setState(() => _selectedModel = model);

    try {
      await UnifiedApiGateway.setActiveProvider(
        _selectedProvider!.id,
        modelId: model.id,
      );
      widget.onModelSelected?.call(_selectedProvider!.id, model.id);
    } catch (e) {
      debugPrint('خطأ في اختيار النموذج: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (widget.compactMode) {
      return _buildCompactSelector();
    }

    return _buildFullSelector();
  }

  Widget _buildCompactSelector() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.darkGrey, AppColors.midGrey],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.lightGrey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.model_training,
                color: AppColors.primaryPurple,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'النموذج النشط',
                style: AppTextStyles.body.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              if (_isLoading)
                const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
            ],
          ),
          const SizedBox(height: 12),
          if (_selectedProvider != null && _selectedModel != null) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppColors.primaryPurple.withValues(alpha: 0.3),
                    AppColors.lightPurple.withValues(alpha: 0.2),
                  ],
                ),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: AppColors.primaryPurple),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: _getProviderColor(
                            _selectedProvider!.type,
                          ).withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Icon(
                          _getProviderIcon(_selectedProvider!.type),
                          color: _getProviderColor(_selectedProvider!.type),
                          size: 16,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _selectedProvider!.displayName,
                          style: AppTextStyles.caption.copyWith(
                            color: Colors.white70,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    _selectedModel!.displayName,
                    style: AppTextStyles.body.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ] else ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppColors.darkGrey.withValues(alpha: 0.5),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppColors.lightGrey.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                'لا يوجد نموذج نشط',
                style: AppTextStyles.body.copyWith(color: Colors.white60),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildFullSelector() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.darkGrey, AppColors.midGrey],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.lightGrey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 20),
          if (widget.showProviderInfo) ...[
            _buildProviderSelector(),
            const SizedBox(height: 20),
          ],
          _buildModelSelector(),
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [AppColors.primaryPurple, AppColors.lightPurple],
            ),
            borderRadius: BorderRadius.circular(8),
          ),
          child: const Icon(
            Icons.model_training,
            color: Colors.white,
            size: 20,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'اختيار النموذج',
                style: AppTextStyles.heading.copyWith(
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
              Text(
                'اختر مقدم الخدمة والنموذج المناسب',
                style: AppTextStyles.caption.copyWith(color: Colors.white70),
              ),
            ],
          ),
        ),
        if (_isLoading)
          const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ),
      ],
    );
  }

  Widget _buildProviderSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'مقدم الخدمة',
          style: AppTextStyles.body.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 12,
          runSpacing: 12,
          children:
              _providers.map((provider) {
                final isSelected = _selectedProvider?.id == provider.id;
                final isWorking = provider.isWorking;

                return AnimatedButton(
                  onTap: isWorking ? () => _selectProvider(provider) : null,
                  gradient: LinearGradient(
                    colors:
                        isSelected
                            ? [AppColors.primaryPurple, AppColors.lightPurple]
                            : isWorking
                            ? [AppColors.darkGrey, AppColors.midGrey]
                            : [Colors.grey.shade800, Colors.grey.shade700],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  padding: const EdgeInsets.all(12),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: _getProviderColor(
                            provider.type,
                          ).withValues(alpha: isWorking ? 0.2 : 0.1),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          _getProviderIcon(provider.type),
                          color: _getProviderColor(
                            provider.type,
                          ).withValues(alpha: isWorking ? 1.0 : 0.5),
                          size: 20,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        provider.displayName,
                        style: AppTextStyles.caption.copyWith(
                          color: isWorking ? Colors.white : Colors.white38,
                          fontWeight:
                              isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      const SizedBox(height: 4),
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 6,
                          vertical: 2,
                        ),
                        decoration: BoxDecoration(
                          color: isWorking ? Colors.green : Colors.red,
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Text(
                          isWorking ? 'متصل' : 'غير متصل',
                          style: AppTextStyles.caption.copyWith(
                            color: Colors.white,
                            fontSize: 10,
                          ),
                        ),
                      ),
                    ],
                  ),
                );
              }).toList(),
        ),
      ],
    );
  }

  Widget _buildModelSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'النماذج المتاحة',
              style: AppTextStyles.body.copyWith(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
            const Spacer(),
            if (_selectedProvider != null)
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.primaryPurple.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  '${_models.length} نموذج',
                  style: AppTextStyles.caption.copyWith(color: Colors.white),
                ),
              ),
          ],
        ),
        const SizedBox(height: 12),
        if (_models.isEmpty) ...[
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppColors.lightGrey.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.orange, size: 20),
                const SizedBox(width: 8),
                Text(
                  _selectedProvider == null
                      ? 'اختر مقدم خدمة أولاً'
                      : 'لا توجد نماذج متاحة',
                  style: AppTextStyles.body.copyWith(color: Colors.white60),
                ),
              ],
            ),
          ),
        ] else ...[
          ...(_models.map((model) => _buildModelCard(model))),
        ],
      ],
    );
  }

  Widget _buildModelCard(AvailableModel model) {
    final isSelected = _selectedModel?.id == model.id;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors:
              isSelected
                  ? [
                    AppColors.primaryPurple.withValues(alpha: 0.3),
                    AppColors.lightPurple.withValues(alpha: 0.2),
                  ]
                  : [
                    AppColors.darkGrey.withValues(alpha: 0.5),
                    AppColors.midGrey.withValues(alpha: 0.3),
                  ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color:
              isSelected
                  ? AppColors.primaryPurple
                  : AppColors.lightGrey.withValues(alpha: 0.3),
          width: isSelected ? 2 : 1,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        title: Text(
          model.displayName,
          style: AppTextStyles.body.copyWith(
            color: Colors.white,
            fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
          ),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              model.id,
              style: AppTextStyles.caption.copyWith(color: Colors.white60),
            ),
            if (model.maxTokens != null) ...[
              const SizedBox(height: 4),
              Text(
                'الحد الأقصى: ${model.maxTokens} رمز',
                style: AppTextStyles.caption.copyWith(color: Colors.white60),
              ),
            ],
            if (model.capabilities.isNotEmpty) ...[
              const SizedBox(height: 8),
              Wrap(
                spacing: 4,
                runSpacing: 4,
                children:
                    model.capabilities.entries
                        .where((e) => e.value == true)
                        .map<Widget>(
                          (capability) => Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: _getCapabilityColor(capability.key),
                              borderRadius: BorderRadius.circular(8),
                            ),
                            child: Text(
                              _getCapabilityLabel(capability.key),
                              style: AppTextStyles.caption.copyWith(
                                color: Colors.white,
                                fontSize: 10,
                              ),
                            ),
                          ),
                        )
                        .toList(),
              ),
            ],
          ],
        ),
        trailing:
            isSelected
                ? Icon(
                  Icons.check_circle,
                  color: AppColors.primaryPurple,
                  size: 24,
                )
                : AnimatedButton(
                  onTap: () => _selectModel(model),
                  gradient: LinearGradient(
                    colors: [AppColors.primaryPurple, AppColors.lightPurple],
                  ),
                  borderRadius: BorderRadius.circular(8),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  child: Text(
                    'اختيار',
                    style: AppTextStyles.caption.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
        onTap: () => _selectModel(model),
      ),
    );
  }

  Color _getProviderColor(ApiProviderType type) {
    switch (type) {
      case ApiProviderType.openai:
        return Colors.green;
      case ApiProviderType.openrouter:
        return Colors.purple;
      case ApiProviderType.gemini:
        return Colors.blue;
      case ApiProviderType.anthropic:
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  IconData _getProviderIcon(ApiProviderType type) {
    switch (type) {
      case ApiProviderType.openai:
        return Icons.psychology;
      case ApiProviderType.openrouter:
        return Icons.router;
      case ApiProviderType.gemini:
        return Icons.diamond;
      case ApiProviderType.anthropic:
        return Icons.android;
      default:
        return Icons.api;
    }
  }

  Color _getCapabilityColor(String capability) {
    switch (capability.toLowerCase()) {
      case 'text':
        return Colors.blue;
      case 'vision':
        return Colors.green;
      case 'code':
        return Colors.orange;
      case 'function_calling':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }

  String _getCapabilityLabel(String capability) {
    switch (capability.toLowerCase()) {
      case 'text':
        return 'نص';
      case 'vision':
        return 'رؤية';
      case 'code':
        return 'كود';
      case 'function_calling':
        return 'وظائف';
      default:
        return capability;
    }
  }
}
