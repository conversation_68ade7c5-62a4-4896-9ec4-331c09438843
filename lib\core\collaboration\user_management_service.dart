import 'package:flutter/foundation.dart';
import '../services/storage_service.dart';

/// خدمة إدارة المستخدمين والتعاون
class UserManagementService {
  static const String _usersKey = 'collaboration_users';
  static const String _teamsKey = 'collaboration_teams';
  static const String _invitationsKey = 'collaboration_invitations';
  static const String _currentUserKey = 'current_user_profile';

  static bool _isInitialized = false;
  static Map<String, CollaborationUser> _users = {};
  static Map<String, Team> _teams = {};
  static List<TeamInvitation> _invitations = [];
  static CollaborationUser? _currentUser;

  /// تهيئة خدمة إدارة المستخدمين
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadUsers();
      await _loadTeams();
      await _loadInvitations();
      await _loadCurrentUser();

      _isInitialized = true;
      debugPrint('👥 تم تهيئة خدمة إدارة المستخدمين');

      // إنشاء مستخدم افتراضي إذا لم يوجد
      if (_currentUser == null) {
        await _createDefaultUser();
      }
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة إدارة المستخدمين: $e');
    }
  }

  /// إنشاء مستخدم جديد
  static Future<CollaborationUser> createUser({
    required String name,
    required String email,
    String? avatar,
    UserRole role = UserRole.member,
  }) async {
    final user = CollaborationUser(
      id: 'user_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      email: email,
      avatar: avatar,
      role: role,
      isOnline: true,
      lastSeen: DateTime.now(),
      joinedAt: DateTime.now(),
    );

    _users[user.id] = user;
    await _saveUsers();

    debugPrint('✅ تم إنشاء مستخدم جديد: $name');
    return user;
  }

  /// تحديث ملف المستخدم
  static Future<bool> updateUser(String userId, {
    String? name,
    String? email,
    String? avatar,
    UserRole? role,
    UserStatus? status,
  }) async {
    try {
      final user = _users[userId];
      if (user == null) return false;

      final updatedUser = CollaborationUser(
        id: user.id,
        name: name ?? user.name,
        email: email ?? user.email,
        avatar: avatar ?? user.avatar,
        role: role ?? user.role,
        status: status ?? user.status,
        isOnline: user.isOnline,
        lastSeen: user.lastSeen,
        joinedAt: user.joinedAt,
      );

      _users[userId] = updatedUser;
      await _saveUsers();

      if (userId == _currentUser?.id) {
        _currentUser = updatedUser;
        await _saveCurrentUser();
      }

      debugPrint('✅ تم تحديث المستخدم: $userId');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في تحديث المستخدم: $e');
      return false;
    }
  }

  /// تحديث حالة المستخدم (متصل/غير متصل)
  static Future<void> updateUserOnlineStatus(String userId, bool isOnline) async {
    final user = _users[userId];
    if (user == null) return;

    final updatedUser = CollaborationUser(
      id: user.id,
      name: user.name,
      email: user.email,
      avatar: user.avatar,
      role: user.role,
      status: user.status,
      isOnline: isOnline,
      lastSeen: isOnline ? DateTime.now() : user.lastSeen,
      joinedAt: user.joinedAt,
    );

    _users[userId] = updatedUser;
    await _saveUsers();
  }

  /// إنشاء فريق جديد
  static Future<Team> createTeam({
    required String name,
    required String description,
    String? avatar,
    List<String>? memberIds,
  }) async {
    final team = Team(
      id: 'team_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      description: description,
      avatar: avatar,
      ownerId: _currentUser?.id ?? '',
      memberIds: memberIds ?? [],
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // إضافة المنشئ كعضو
    if (_currentUser != null && !team.memberIds.contains(_currentUser!.id)) {
      team.memberIds.add(_currentUser!.id);
    }

    _teams[team.id] = team;
    await _saveTeams();

    debugPrint('✅ تم إنشاء فريق جديد: $name');
    return team;
  }

  /// إضافة عضو إلى الفريق
  static Future<bool> addTeamMember(String teamId, String userId) async {
    try {
      final team = _teams[teamId];
      if (team == null) return false;

      if (!team.memberIds.contains(userId)) {
        team.memberIds.add(userId);

        final updatedTeam = Team(
          id: team.id,
          name: team.name,
          description: team.description,
          avatar: team.avatar,
          ownerId: team.ownerId,
          memberIds: team.memberIds,
          createdAt: team.createdAt,
          updatedAt: DateTime.now(),
        );

        _teams[teamId] = updatedTeam;
        await _saveTeams();

        debugPrint('✅ تم إضافة عضو للفريق: $userId');
        return true;
      }
      return true;
    } catch (e) {
      debugPrint('❌ فشل في إضافة عضو للفريق: $e');
      return false;
    }
  }

  /// إزالة عضو من الفريق
  static Future<bool> removeTeamMember(String teamId, String userId) async {
    try {
      final team = _teams[teamId];
      if (team == null) return false;

      team.memberIds.remove(userId);

      final updatedTeam = Team(
        id: team.id,
        name: team.name,
        description: team.description,
        avatar: team.avatar,
        ownerId: team.ownerId,
        memberIds: team.memberIds,
        createdAt: team.createdAt,
        updatedAt: DateTime.now(),
      );

      _teams[teamId] = updatedTeam;
      await _saveTeams();

      debugPrint('✅ تم إزالة عضو من الفريق: $userId');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في إزالة عضو من الفريق: $e');
      return false;
    }
  }

  /// إرسال دعوة للانضمام للفريق
  static Future<TeamInvitation> sendTeamInvitation({
    required String teamId,
    required String inviteeEmail,
    String? message,
  }) async {
    final invitation = TeamInvitation(
      id: 'inv_${DateTime.now().millisecondsSinceEpoch}',
      teamId: teamId,
      inviterId: _currentUser?.id ?? '',
      inviteeEmail: inviteeEmail,
      message: message,
      status: InvitationStatus.pending,
      sentAt: DateTime.now(),
    );

    _invitations.add(invitation);
    await _saveInvitations();

    debugPrint('📧 تم إرسال دعوة للفريق: $inviteeEmail');
    return invitation;
  }

  /// قبول دعوة الفريق
  static Future<bool> acceptTeamInvitation(String invitationId) async {
    try {
      final invitationIndex = _invitations.indexWhere((inv) => inv.id == invitationId);
      if (invitationIndex == -1) return false;

      final invitation = _invitations[invitationIndex];

      // تحديث حالة الدعوة
      final updatedInvitation = TeamInvitation(
        id: invitation.id,
        teamId: invitation.teamId,
        inviterId: invitation.inviterId,
        inviteeEmail: invitation.inviteeEmail,
        message: invitation.message,
        status: InvitationStatus.accepted,
        sentAt: invitation.sentAt,
        respondedAt: DateTime.now(),
      );

      _invitations[invitationIndex] = updatedInvitation;

      // إضافة المستخدم للفريق
      if (_currentUser != null) {
        await addTeamMember(invitation.teamId, _currentUser!.id);
      }

      await _saveInvitations();

      debugPrint('✅ تم قبول دعوة الفريق: $invitationId');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في قبول دعوة الفريق: $e');
      return false;
    }
  }

  /// رفض دعوة الفريق
  static Future<bool> rejectTeamInvitation(String invitationId) async {
    try {
      final invitationIndex = _invitations.indexWhere((inv) => inv.id == invitationId);
      if (invitationIndex == -1) return false;

      final invitation = _invitations[invitationIndex];

      final updatedInvitation = TeamInvitation(
        id: invitation.id,
        teamId: invitation.teamId,
        inviterId: invitation.inviterId,
        inviteeEmail: invitation.inviteeEmail,
        message: invitation.message,
        status: InvitationStatus.rejected,
        sentAt: invitation.sentAt,
        respondedAt: DateTime.now(),
      );

      _invitations[invitationIndex] = updatedInvitation;
      await _saveInvitations();

      debugPrint('❌ تم رفض دعوة الفريق: $invitationId');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في رفض دعوة الفريق: $e');
      return false;
    }
  }

  /// البحث عن المستخدمين
  static List<CollaborationUser> searchUsers(String query) {
    if (query.isEmpty) return _users.values.toList();

    return _users.values.where((user) {
      return user.name.toLowerCase().contains(query.toLowerCase()) ||
             user.email.toLowerCase().contains(query.toLowerCase());
    }).toList();
  }

  /// الحصول على فرق المستخدم
  static List<Team> getUserTeams(String userId) {
    return _teams.values.where((team) => team.memberIds.contains(userId)).toList();
  }

  /// الحصول على أعضاء الفريق
  static List<CollaborationUser> getTeamMembers(String teamId) {
    final team = _teams[teamId];
    if (team == null) return [];

    return team.memberIds
        .map((id) => _users[id])
        .where((user) => user != null)
        .cast<CollaborationUser>()
        .toList();
  }

  /// الحصول على الدعوات المعلقة
  static List<TeamInvitation> getPendingInvitations({String? teamId}) {
    var invitations = _invitations.where((inv) => inv.status == InvitationStatus.pending);

    if (teamId != null) {
      invitations = invitations.where((inv) => inv.teamId == teamId);
    }

    return invitations.toList();
  }

  /// إنشاء مستخدم افتراضي
  static Future<void> _createDefaultUser() async {
    _currentUser = await createUser(
      name: 'المستخدم الافتراضي',
      email: '<EMAIL>',
      role: UserRole.admin,
    );
    await _saveCurrentUser();
  }

  // Private methods for data persistence

  static Future<void> _saveUsers() async {
    final data = _users.map((key, value) => MapEntry(key, value.toMap()));
    await StorageService.saveData(_usersKey, data);
  }

  static Future<void> _loadUsers() async {
    try {
      final data = await StorageService.getData(_usersKey);
      if (data != null && data is Map) {
        _users = data.map((key, value) =>
            MapEntry(key, CollaborationUser.fromMap(value)));
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل المستخدمين: $e');
    }
  }

  static Future<void> _saveTeams() async {
    final data = _teams.map((key, value) => MapEntry(key, value.toMap()));
    await StorageService.saveData(_teamsKey, data);
  }

  static Future<void> _loadTeams() async {
    try {
      final data = await StorageService.getData(_teamsKey);
      if (data != null && data is Map) {
        _teams = data.map((key, value) => MapEntry(key, Team.fromMap(value)));
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل الفرق: $e');
    }
  }

  static Future<void> _saveInvitations() async {
    final data = _invitations.map((inv) => inv.toMap()).toList();
    await StorageService.saveData(_invitationsKey, data);
  }

  static Future<void> _loadInvitations() async {
    try {
      final data = await StorageService.getData(_invitationsKey);
      if (data != null && data is List) {
        _invitations = data.map((item) => TeamInvitation.fromMap(item)).toList();
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل الدعوات: $e');
    }
  }

  static Future<void> _saveCurrentUser() async {
    if (_currentUser != null) {
      await StorageService.saveData(_currentUserKey, _currentUser!.toMap());
    }
  }

  static Future<void> _loadCurrentUser() async {
    try {
      final data = await StorageService.getData(_currentUserKey);
      if (data != null) {
        _currentUser = CollaborationUser.fromMap(data);
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل المستخدم الحالي: $e');
    }
  }

  // Getters
  static bool get isInitialized => _isInitialized;
  static CollaborationUser? get currentUser => _currentUser;
  static List<CollaborationUser> get allUsers => _users.values.toList();
  static List<Team> get allTeams => _teams.values.toList();
  static List<TeamInvitation> get allInvitations => _invitations;
  static int get usersCount => _users.length;
  static int get teamsCount => _teams.length;
}

/// مستخدم التعاون
class CollaborationUser {
  final String id;
  final String name;
  final String email;
  final String? avatar;
  final UserRole role;
  final UserStatus status;
  final bool isOnline;
  final DateTime lastSeen;
  final DateTime joinedAt;

  CollaborationUser({
    required this.id,
    required this.name,
    required this.email,
    this.avatar,
    this.role = UserRole.member,
    this.status = UserStatus.available,
    this.isOnline = false,
    required this.lastSeen,
    required this.joinedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'avatar': avatar,
      'role': role.toString(),
      'status': status.toString(),
      'isOnline': isOnline,
      'lastSeen': lastSeen.toIso8601String(),
      'joinedAt': joinedAt.toIso8601String(),
    };
  }

  factory CollaborationUser.fromMap(Map<String, dynamic> map) {
    return CollaborationUser(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      avatar: map['avatar'],
      role: UserRole.values.firstWhere(
        (e) => e.toString() == map['role'],
        orElse: () => UserRole.member,
      ),
      status: UserStatus.values.firstWhere(
        (e) => e.toString() == map['status'],
        orElse: () => UserStatus.available,
      ),
      isOnline: map['isOnline'] ?? false,
      lastSeen: DateTime.parse(map['lastSeen']),
      joinedAt: DateTime.parse(map['joinedAt']),
    );
  }
}

/// فريق العمل
class Team {
  final String id;
  final String name;
  final String description;
  final String? avatar;
  final String ownerId;
  final List<String> memberIds;
  final DateTime createdAt;
  final DateTime updatedAt;

  Team({
    required this.id,
    required this.name,
    required this.description,
    this.avatar,
    required this.ownerId,
    required this.memberIds,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'avatar': avatar,
      'ownerId': ownerId,
      'memberIds': memberIds,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory Team.fromMap(Map<String, dynamic> map) {
    return Team(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      avatar: map['avatar'],
      ownerId: map['ownerId'] ?? '',
      memberIds: List<String>.from(map['memberIds'] ?? []),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }
}

/// دعوة الفريق
class TeamInvitation {
  final String id;
  final String teamId;
  final String inviterId;
  final String inviteeEmail;
  final String? message;
  final InvitationStatus status;
  final DateTime sentAt;
  final DateTime? respondedAt;

  TeamInvitation({
    required this.id,
    required this.teamId,
    required this.inviterId,
    required this.inviteeEmail,
    this.message,
    required this.status,
    required this.sentAt,
    this.respondedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'teamId': teamId,
      'inviterId': inviterId,
      'inviteeEmail': inviteeEmail,
      'message': message,
      'status': status.toString(),
      'sentAt': sentAt.toIso8601String(),
      'respondedAt': respondedAt?.toIso8601String(),
    };
  }

  factory TeamInvitation.fromMap(Map<String, dynamic> map) {
    return TeamInvitation(
      id: map['id'] ?? '',
      teamId: map['teamId'] ?? '',
      inviterId: map['inviterId'] ?? '',
      inviteeEmail: map['inviteeEmail'] ?? '',
      message: map['message'],
      status: InvitationStatus.values.firstWhere(
        (e) => e.toString() == map['status'],
        orElse: () => InvitationStatus.pending,
      ),
      sentAt: DateTime.parse(map['sentAt']),
      respondedAt: map['respondedAt'] != null
          ? DateTime.parse(map['respondedAt'])
          : null,
    );
  }
}

/// دور المستخدم
enum UserRole {
  admin,
  moderator,
  member,
  viewer,
}

/// حالة المستخدم
enum UserStatus {
  available,
  busy,
  away,
  doNotDisturb,
}

/// حالة الدعوة
enum InvitationStatus {
  pending,
  accepted,
  rejected,
  expired,
}
