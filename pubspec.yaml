name: deepseek_project
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.7.2

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # HTTP & API
  http: ^1.2.1
  dio: ^5.4.0

  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9

  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Security
  flutter_secure_storage: ^9.0.0
  crypto: ^3.0.3

  # UI & Animations
  animations: ^2.0.8
  lottie: ^2.7.0
  shimmer: ^3.0.0
  flutter_staggered_animations: ^1.1.1

  # Utils
  intl: ^0.19.0
  uuid: ^4.2.1

  # File & Media Handling
  image_picker: ^1.0.7
  file_picker: ^8.0.0+1  # تم تفعيله
  permission_handler: ^11.1.0
  path_provider: ^2.1.2
  equatable: ^2.0.5
  cached_network_image: ^3.3.1

  # Image Processing (مؤقتاً معطل)
  # image: ^4.1.7

  # Document Processing (مؤقتاً معطل)
  # pdf_text: ^0.2.1
  # archive: ^3.4.10

  # Notifications (مفعلة الآن)
  flutter_local_notifications: ^17.2.3

  # Voice Services
  speech_to_text: ^7.0.0
  flutter_tts: ^4.0.2

  # Network & Connectivity
  connectivity_plus: ^6.0.0
  url_launcher: ^6.2.4

  # Icons
  cupertino_icons: ^1.0.8
  font_awesome_flutter: ^10.6.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^5.0.0

  # Code Generation
  hive_generator: ^2.0.1
  build_runner: ^2.4.7

  # Testing
  mockito: ^5.4.4
  integration_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
    - assets/fonts/

  # Fonts
  # fonts:
  #   - family: Urbanist
  #     fonts:
  #       - asset: assets/fonts/Urbanist-Regular.ttf
  #       - asset: assets/fonts/Urbanist-Medium.ttf
  #         weight: 500
  #       - asset: assets/fonts/Urbanist-SemiBold.ttf
  #         weight: 600
  #       - asset: assets/fonts/Urbanist-Bold.ttf
  #         weight: 700

  # To add assets to your application, add an assets section, like this:
  # assets:
  #   - images/a_dot_burr.jpeg
  #   - images/a_dot_ham.jpeg

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
