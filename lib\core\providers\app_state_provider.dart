import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/app_state.dart';
import '../models/user_model.dart';
import '../models/conversation_model.dart';

/// مزود حالة التطبيق الرئيسي
final appStateProvider = StateNotifierProvider<AppStateNotifier, AppState>((ref) {
  return AppStateNotifier();
});

/// مزود المستخدم الحالي
final currentUserProvider = StateProvider<UserModel?>((ref) => null);

/// مزود المحادثات
final conversationsProvider = StateNotifierProvider<ConversationsNotifier, List<ConversationModel>>((ref) {
  return ConversationsNotifier();
});

/// مزود حالة الاتصال
final connectivityProvider = StateProvider<bool>((ref) => true);

/// مزود الإعدادات
final settingsProvider = StateNotifierProvider<SettingsNotifier, AppSettings>((ref) {
  return SettingsNotifier();
});

/// مزود حالة التحميل العام
final loadingProvider = StateProvider<bool>((ref) => false);

/// مزود الأخطاء
final errorProvider = StateProvider<String?>((ref) => null);

class AppStateNotifier extends StateNotifier<AppState> {
  AppStateNotifier() : super(AppState.initial()) {
    _loadInitialState();
  }

  Future<void> _loadInitialState() async {
    final prefs = await SharedPreferences.getInstance();
    final isFirstLaunch = prefs.getBool('is_first_launch') ?? true;
    final isLoggedIn = prefs.getBool('is_logged_in') ?? false;

    state = state.copyWith(
      isFirstLaunch: isFirstLaunch,
      isLoggedIn: isLoggedIn,
      isLoading: false,
    );
  }

  void setFirstLaunch(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('is_first_launch', value);
    state = state.copyWith(isFirstLaunch: value);
  }

  void setLoggedIn(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('is_logged_in', value);
    state = state.copyWith(isLoggedIn: value);
  }

  void setLoading(bool value) {
    state = state.copyWith(isLoading: value);
  }

  void setError(String? error) {
    state = state.copyWith(error: error);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

class ConversationsNotifier extends StateNotifier<List<ConversationModel>> {
  ConversationsNotifier() : super([]) {
    _loadConversations();
  }

  Future<void> _loadConversations() async {
    // تحميل المحادثات من التخزين المحلي
    // سيتم تنفيذها لاحقاً مع Hive
  }

  void addConversation(ConversationModel conversation) {
    state = [...state, conversation];
    _saveConversations();
  }

  void updateConversation(String id, ConversationModel conversation) {
    state = state.map((c) => c.id == id ? conversation : c).toList();
    _saveConversations();
  }

  void deleteConversation(String id) {
    state = state.where((c) => c.id != id).toList();
    _saveConversations();
  }

  void clearAllConversations() {
    state = [];
    _saveConversations();
  }

  Future<void> _saveConversations() async {
    // حفظ المحادثات في التخزين المحلي
    // سيتم تنفيذها لاحقاً مع Hive
  }
}

class SettingsNotifier extends StateNotifier<AppSettings> {
  SettingsNotifier() : super(AppSettings.defaultSettings()) {
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final prefs = await SharedPreferences.getInstance();

    state = AppSettings(
      isDarkMode: prefs.getBool('dark_mode') ?? true,
      language: prefs.getString('language') ?? 'ar',
      fontSize: prefs.getDouble('font_size') ?? 16.0,
      enableNotifications: prefs.getBool('notifications') ?? true,
      enableAnimations: prefs.getBool('animations') ?? true,
      autoSave: prefs.getBool('auto_save') ?? true,
      maxTokens: prefs.getInt('max_tokens') ?? 2048,
      temperature: prefs.getDouble('temperature') ?? 0.7,
    );
  }

  void updateDarkMode(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('dark_mode', value);
    state = state.copyWith(isDarkMode: value);
  }

  void updateLanguage(String value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('language', value);
    state = state.copyWith(language: value);
  }

  void updateFontSize(double value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('font_size', value);
    state = state.copyWith(fontSize: value);
  }

  void updateNotifications(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('notifications', value);
    state = state.copyWith(enableNotifications: value);
  }

  void updateAnimations(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('animations', value);
    state = state.copyWith(enableAnimations: value);
  }

  void updateAutoSave(bool value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('auto_save', value);
    state = state.copyWith(autoSave: value);
  }

  void updateMaxTokens(int value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setInt('max_tokens', value);
    state = state.copyWith(maxTokens: value);
  }

  void updateTemperature(double value) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setDouble('temperature', value);
    state = state.copyWith(temperature: value);
  }
}
