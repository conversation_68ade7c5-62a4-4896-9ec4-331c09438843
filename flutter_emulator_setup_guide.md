# 🚀 دليل إعداد Flutter للمحاكي - بدون مشاكل

## 📋 المتطلبات الأساسية

### 1. تحديث Flutter إلى أحدث إصدار
```bash
flutter upgrade
flutter doctor
```

### 2. إعداد Android Studio
- تأكد من تثبيت Android Studio أحدث إصدار
- تثبيت Android SDK Tools
- تثبيت Android NDK أحدث إصدار (27.0.12077973 أو أحدث)

### 3. إعداد متغيرات البيئة
```bash
# إضافة إلى PATH
ANDROID_HOME=C:\Users\<USER>\AppData\Local\Android\Sdk
ANDROID_SDK_ROOT=C:\Users\<USER>\AppData\Local\Android\Sdk
```

## 🔧 إعداد المحاكي المثالي

### 1. إن<PERSON>اء محاكي جديد
```bash
# عرض المحاكيات المتاحة
flutter emulators

# إنشاء محاكي جديد
flutter emulators --create --name flutter_dev
```

### 2. إعدادات المحاكي الموصى بها
- **API Level**: 33 أو أحدث (Android 13+)
- **RAM**: 4GB أو أكثر
- **Storage**: 8GB أو أكثر
- **Graphics**: Hardware - GLES 2.0

### 3. تشغيل المحاكي
```bash
# تشغيل محاكي محدد
flutter emulators --launch flutter_dev

# أو تشغيل أي محاكي متاح
emulator -avd flutter_dev
```

## 📁 قالب مشروع Flutter مثالي

### 1. إنشاء مشروع جديد
```bash
flutter create --org com.yourcompany my_flutter_app
cd my_flutter_app
```

### 2. ملف pubspec.yaml محسن
```yaml
name: my_flutter_app
description: A Flutter application optimized for emulator.

publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  
  # Core packages
  cupertino_icons: ^1.0.8
  
  # State Management
  provider: ^6.1.2
  riverpod: ^2.5.1
  flutter_riverpod: ^2.5.1
  
  # HTTP & API
  http: ^1.2.1
  dio: ^5.4.3
  
  # Local Storage
  shared_preferences: ^2.2.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Security
  flutter_secure_storage: ^9.2.2
  crypto: ^3.0.3
  
  # UI & Animations
  animations: ^2.0.11
  lottie: ^3.1.2
  
  # Utils
  intl: ^0.19.0
  uuid: ^4.4.0
  
  # Media (للمحاكي)
  image_picker: ^1.1.2
  cached_network_image: ^3.3.1
  
  # Permissions
  permission_handler: ^11.3.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
  
  fonts:
    - family: Cairo
      fonts:
        - asset: assets/fonts/Cairo-Regular.ttf
        - asset: assets/fonts/Cairo-Bold.ttf
          weight: 700
```

### 3. إعدادات Android محسنة

#### android/app/build.gradle.kts
```kotlin
plugins {
    id("com.android.application")
    id("kotlin-android")
    id("dev.flutter.flutter-gradle-plugin")
}

android {
    namespace = "com.yourcompany.my_flutter_app"
    compileSdk = 34
    ndkVersion = "27.0.12077973"

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
        isCoreLibraryDesugaringEnabled = true
    }

    kotlinOptions {
        jvmTarget = JavaVersion.VERSION_17.toString()
    }

    defaultConfig {
        applicationId = "com.yourcompany.my_flutter_app"
        minSdk = 23
        targetSdk = 34
        versionCode = 1
        versionName = "1.0"
        multiDexEnabled = true
    }

    buildTypes {
        release {
            signingConfig = signingConfigs.getByName("debug")
            isMinifyEnabled = false
            proguardFiles(
                getDefaultProguardFile("proguard-android-optimize.txt"),
                "proguard-rules.pro"
            )
        }
    }
}

flutter {
    source = "../.."
}

dependencies {
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
    implementation("androidx.multidex:multidex:2.0.1")
}
```

#### android/gradle.properties
```properties
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=1024m -XX:+HeapDumpOnOutOfMemoryError
android.useAndroidX=true
android.enableJetifier=true
android.enableR8=true
android.nonTransitiveRClass=true
android.defaults.buildfeatures.buildconfig=true
android.nonFinalResIds=false
```

## 🚀 سكريبت تشغيل سريع

### run_emulator.bat (Windows)
```batch
@echo off
echo 🚀 بدء تشغيل Flutter على المحاكي...

echo 📱 التحقق من المحاكيات المتاحة...
flutter devices

echo 🔄 تنظيف المشروع...
flutter clean

echo 📦 تحديث الاعتماديات...
flutter pub get

echo 🏗️ بناء وتشغيل التطبيق...
flutter run -d emulator-5554 --hot

pause
```

### run_emulator.sh (Linux/Mac)
```bash
#!/bin/bash
echo "🚀 بدء تشغيل Flutter على المحاكي..."

echo "📱 التحقق من المحاكيات المتاحة..."
flutter devices

echo "🔄 تنظيف المشروع..."
flutter clean

echo "📦 تحديث الاعتماديات..."
flutter pub get

echo "🏗️ بناء وتشغيل التطبيق..."
flutter run -d emulator-5554 --hot
```

## 🛡️ حل المشاكل الشائعة

### 1. مشكلة Android NDK
```kotlin
// في android/app/build.gradle.kts
android {
    ndkVersion = "27.0.12077973" // أحدث إصدار
}
```

### 2. مشكلة Core Library Desugaring
```kotlin
android {
    compileOptions {
        isCoreLibraryDesugaringEnabled = true
    }
}

dependencies {
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
}
```

### 3. مشكلة الذاكرة
```properties
# في android/gradle.properties
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=1024m
```

### 4. مشكلة المكتبات القديمة
```yaml
# تجنب هذه المكتبات في المحاكي
# flutter_local_notifications: ^16.x.x  # استخدم ^17.x.x أو أحدث
# file_picker: ^6.x.x  # استخدم ^8.x.x أو أحدث
```

## 📋 قائمة فحص قبل التشغيل

### ✅ قبل إنشاء مشروع جديد:
- [ ] تحديث Flutter: `flutter upgrade`
- [ ] فحص البيئة: `flutter doctor`
- [ ] تشغيل المحاكي: `flutter emulators --launch <name>`
- [ ] التأكد من الاتصال: `flutter devices`

### ✅ عند إنشاء مشروع جديد:
- [ ] استخدام قالب محسن للمحاكي
- [ ] إعداد android/app/build.gradle.kts صحيح
- [ ] إضافة gradle.properties محسن
- [ ] تجنب المكتبات المشكلة

### ✅ عند التشغيل:
- [ ] تنظيف المشروع: `flutter clean`
- [ ] تحديث الاعتماديات: `flutter pub get`
- [ ] التشغيل: `flutter run`

## 🎯 نصائح للأداء الأمثل

### 1. إعدادات المحاكي
- استخدم Hardware Graphics
- خصص 4GB RAM على الأقل
- فعل Hardware Keyboard

### 2. إعدادات Flutter
```bash
# للتطوير السريع
flutter run --hot

# للاختبار
flutter run --profile

# للإنتاج
flutter run --release
```

### 3. مراقبة الأداء
```bash
# فتح DevTools
flutter pub global activate devtools
flutter pub global run devtools
```

## 🔄 صيانة دورية

### أسبوعياً:
```bash
flutter upgrade
flutter doctor
flutter clean
```

### شهرياً:
```bash
# تحديث Android SDK
sdkmanager --update

# تنظيف cache
flutter pub cache clean
```

## 📞 الدعم والمساعدة

### مصادر مفيدة:
- [Flutter Documentation](https://docs.flutter.dev/)
- [Android Developer Guide](https://developer.android.com/)
- [Flutter Community](https://flutter.dev/community)

### أوامر مفيدة للتشخيص:
```bash
flutter doctor -v
flutter config
flutter devices
adb devices
```

---

**ملاحظة**: احفظ هذا الدليل واتبعه في كل مرة تنشئ مشروع Flutter جديد لضمان عمل سلس مع المحاكي! 🚀
