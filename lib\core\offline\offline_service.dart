import 'package:flutter/foundation.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import '../services/storage_service.dart';
import '../services/enhanced_ai_service.dart';

/// خدمة العمل بدون إنترنت
class OfflineService {
  static const String _offlineDataKey = 'offline_data';
  static const String _cachedResponsesKey = 'cached_responses';
  static const String _offlineQueueKey = 'offline_queue';

  static bool _isOnline = true;
  static bool _isInitialized = false;
  static List<OfflineTask> _pendingTasks = [];
  static Map<String, String> _cachedResponses = {};

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // تحميل البيانات المحفوظة
      await _loadOfflineData();
      await _loadCachedResponses();
      await _loadPendingTasks();

      // مراقبة حالة الاتصال
      Connectivity().onConnectivityChanged.listen((List<ConnectivityResult> results) {
        if (results.isNotEmpty) {
          _onConnectivityChanged(results.first);
        }
      });

      // فحص الحالة الحالية
      final connectivityResult = await Connectivity().checkConnectivity();
      _isOnline = connectivityResult.isNotEmpty && connectivityResult.first != ConnectivityResult.none;

      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة العمل بدون إنترنت - الحالة: ${_isOnline ? "متصل" : "غير متصل"}');

      // معالجة المهام المعلقة إذا كان متصلاً
      if (_isOnline) {
        await _processPendingTasks();
      }
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة العمل بدون إنترنت: $e');
    }
  }

  /// مراقبة تغيير حالة الاتصال
  static void _onConnectivityChanged(ConnectivityResult result) async {
    final wasOnline = _isOnline;
    _isOnline = result != ConnectivityResult.none;

    debugPrint('🌐 تغيرت حالة الاتصال: ${_isOnline ? "متصل" : "غير متصل"}');

    // إذا عاد الاتصال، معالجة المهام المعلقة
    if (!wasOnline && _isOnline) {
      await _processPendingTasks();
    }
  }

  /// التحقق من حالة الاتصال
  static bool get isOnline => _isOnline;
  static bool get isOffline => !_isOnline;

  /// حفظ رد في الذاكرة المؤقتة
  static Future<void> cacheResponse(String query, String response) async {
    try {
      _cachedResponses[_normalizeQuery(query)] = response;
      await _saveCachedResponses();
      debugPrint('💾 تم حفظ الرد في الذاكرة المؤقتة');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ الرد: $e');
    }
  }

  /// البحث عن رد محفوظ
  static String? getCachedResponse(String query) {
    final normalizedQuery = _normalizeQuery(query);
    return _cachedResponses[normalizedQuery];
  }

  /// إضافة مهمة للقائمة المعلقة
  static Future<void> addPendingTask(OfflineTask task) async {
    try {
      _pendingTasks.add(task);
      await _savePendingTasks();
      debugPrint('📝 تم إضافة مهمة للقائمة المعلقة: ${task.type}');
    } catch (e) {
      debugPrint('❌ خطأ في إضافة المهمة المعلقة: $e');
    }
  }

  /// معالجة المهام المعلقة
  static Future<void> _processPendingTasks() async {
    if (_pendingTasks.isEmpty) return;

    debugPrint('🔄 بدء معالجة ${_pendingTasks.length} مهمة معلقة');

    final tasksToProcess = List<OfflineTask>.from(_pendingTasks);
    _pendingTasks.clear();

    for (final task in tasksToProcess) {
      try {
        await _processTask(task);
        debugPrint('✅ تم معالجة المهمة: ${task.type}');
      } catch (e) {
        debugPrint('❌ فشل في معالجة المهمة ${task.type}: $e');
        // إعادة إضافة المهمة للمحاولة لاحقاً
        _pendingTasks.add(task);
      }
    }

    await _savePendingTasks();
  }

  /// معالجة مهمة واحدة
  static Future<void> _processTask(OfflineTask task) async {
    switch (task.type) {
      case TaskType.chatMessage:
        await _processChatTask(task);
        break;
      case TaskType.imageAnalysis:
        await _processImageAnalysisTask(task);
        break;
      case TaskType.textSummarization:
        await _processTextSummarizationTask(task);
        break;
      case TaskType.dataAnalysis:
        await _processDataAnalysisTask(task);
        break;
      default:
        debugPrint('⚠️ نوع مهمة غير معروف: ${task.type}');
    }
  }

  /// معالجة مهمة الدردشة
  static Future<void> _processChatTask(OfflineTask task) async {
    final response = await EnhancedAIService.sendMessage(
      message: task.data['message'] ?? '',
      conversationId: task.data['conversationId'] ?? '',
    );

    // حفظ الرد في الذاكرة المؤقتة
    await cacheResponse(task.data['message'] ?? '', response.content);
  }

  /// معالجة مهمة تحليل الصور
  static Future<void> _processImageAnalysisTask(OfflineTask task) async {
    // تطبيق تحليل الصور عند عودة الاتصال
    // يمكن إضافة المنطق هنا
  }

  /// معالجة مهمة تلخيص النصوص
  static Future<void> _processTextSummarizationTask(OfflineTask task) async {
    // تطبيق تلخيص النصوص عند عودة الاتصال
    // يمكن إضافة المنطق هنا
  }

  /// معالجة مهمة تحليل البيانات
  static Future<void> _processDataAnalysisTask(OfflineTask task) async {
    // تطبيق تحليل البيانات عند عودة الاتصال
    // يمكن إضافة المنطق هنا
  }

  /// تطبيع النص للبحث
  static String _normalizeQuery(String query) {
    return query.toLowerCase().trim().replaceAll(RegExp(r'\s+'), ' ');
  }

  /// حفظ البيانات المؤقتة
  static Future<void> _saveCachedResponses() async {
    await StorageService.saveData(_cachedResponsesKey, _cachedResponses);
  }

  /// تحميل البيانات المؤقتة
  static Future<void> _loadCachedResponses() async {
    try {
      final data = await StorageService.getData(_cachedResponsesKey);
      if (data != null) {
        _cachedResponses = Map<String, String>.from(data);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات المؤقتة: $e');
    }
  }

  /// حفظ المهام المعلقة
  static Future<void> _savePendingTasks() async {
    final tasksData = _pendingTasks.map((task) => task.toMap()).toList();
    await StorageService.saveData(_offlineQueueKey, tasksData);
  }

  /// تحميل المهام المعلقة
  static Future<void> _loadPendingTasks() async {
    try {
      final data = await StorageService.getData(_offlineQueueKey);
      if (data != null && data is List) {
        _pendingTasks = data.map((taskData) => OfflineTask.fromMap(taskData)).toList();
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل المهام المعلقة: $e');
    }
  }

  /// تحميل البيانات المحفوظة
  static Future<void> _loadOfflineData() async {
    try {
      final data = await StorageService.getData(_offlineDataKey);
      if (data != null) {
        // تحميل البيانات المحفوظة للعمل بدون إنترنت
        debugPrint('📂 تم تحميل البيانات المحفوظة');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل البيانات المحفوظة: $e');
    }
  }

  /// حفظ البيانات الأساسية للعمل بدون إنترنت
  static Future<void> saveEssentialData(Map<String, dynamic> data) async {
    try {
      await StorageService.saveData(_offlineDataKey, data);
      debugPrint('💾 تم حفظ البيانات الأساسية');
    } catch (e) {
      debugPrint('❌ خطأ في حفظ البيانات الأساسية: $e');
    }
  }

  /// مسح الذاكرة المؤقتة
  static Future<void> clearCache() async {
    try {
      _cachedResponses.clear();
      await StorageService.saveData(_cachedResponsesKey, null);
      debugPrint('🧹 تم مسح الذاكرة المؤقتة');
    } catch (e) {
      debugPrint('❌ خطأ في مسح الذاكرة المؤقتة: $e');
    }
  }

  /// مسح المهام المعلقة
  static Future<void> clearPendingTasks() async {
    try {
      _pendingTasks.clear();
      await StorageService.saveData(_offlineQueueKey, null);
      debugPrint('🧹 تم مسح المهام المعلقة');
    } catch (e) {
      debugPrint('❌ خطأ في مسح المهام المعلقة: $e');
    }
  }

  /// الحصول على إحصائيات العمل بدون إنترنت
  static OfflineStats getStats() {
    return OfflineStats(
      isOnline: _isOnline,
      cachedResponsesCount: _cachedResponses.length,
      pendingTasksCount: _pendingTasks.length,
      cacheSize: _calculateCacheSize(),
    );
  }

  /// حساب حجم الذاكرة المؤقتة
  static int _calculateCacheSize() {
    int totalSize = 0;
    for (final response in _cachedResponses.values) {
      totalSize += response.length;
    }
    return totalSize;
  }

  /// إنشاء رد افتراضي للعمل بدون إنترنت
  static String getOfflineResponse(String query) {
    // ردود افتراضية للاستعلامات الشائعة
    final offlineResponses = {
      'مرحبا': 'مرحباً بك! أنا حالياً أعمل في وضع عدم الاتصال. يمكنني مساعدتك بالمعلومات المحفوظة محلياً.',
      'كيف حالك': 'أنا بخير، شكراً لسؤالك! أعمل حالياً بدون اتصال بالإنترنت.',
      'ما اسمك': 'أنا DeepSeek AI، مساعدك الذكي. أعمل حالياً في الوضع المحلي.',
      'مساعدة': 'يمكنني مساعدتك في الوضع المحلي بالمعلومات المحفوظة. عند عودة الاتصال، ستتوفر جميع الميزات.',
    };

    final normalizedQuery = _normalizeQuery(query);

    // البحث عن رد مطابق
    for (final entry in offlineResponses.entries) {
      if (normalizedQuery.contains(_normalizeQuery(entry.key))) {
        return entry.value;
      }
    }

    return 'عذراً، أنا أعمل حالياً بدون اتصال بالإنترنت. سيتم معالجة طلبك عند عودة الاتصال. يمكنك المحاولة مع استعلامات بسيطة مثل "مرحبا" أو "مساعدة".';
  }
}

/// نموذج المهمة المعلقة
class OfflineTask {
  final String id;
  final TaskType type;
  final Map<String, dynamic> data;
  final DateTime createdAt;

  OfflineTask({
    required this.id,
    required this.type,
    required this.data,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.toString(),
      'data': data,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory OfflineTask.fromMap(Map<String, dynamic> map) {
    return OfflineTask(
      id: map['id'] ?? '',
      type: TaskType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => TaskType.chatMessage,
      ),
      data: Map<String, dynamic>.from(map['data'] ?? {}),
      createdAt: DateTime.parse(map['createdAt'] ?? DateTime.now().toIso8601String()),
    );
  }
}

/// أنواع المهام
enum TaskType {
  chatMessage,
  imageAnalysis,
  textSummarization,
  dataAnalysis,
  codeGeneration,
  translation,
}

/// إحصائيات العمل بدون إنترنت
class OfflineStats {
  final bool isOnline;
  final int cachedResponsesCount;
  final int pendingTasksCount;
  final int cacheSize;

  OfflineStats({
    required this.isOnline,
    required this.cachedResponsesCount,
    required this.pendingTasksCount,
    required this.cacheSize,
  });

  String get cacheSizeFormatted {
    if (cacheSize < 1024) return '$cacheSize بايت';
    if (cacheSize < 1024 * 1024) return '${(cacheSize / 1024).toStringAsFixed(1)} كيلوبايت';
    return '${(cacheSize / (1024 * 1024)).toStringAsFixed(1)} ميجابايت';
  }
}
