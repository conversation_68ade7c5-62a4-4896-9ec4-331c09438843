import 'package:flutter/material.dart';
import '../services/ai_service.dart';
import '../services/error_handler.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../widgets/animated_icons.dart';

// مثال على استخدام خدمة الذكاء الاصطناعي المحسنة
class AIServiceExample extends StatefulWidget {
  const AIServiceExample({Key? key}) : super(key: key);

  @override
  State<AIServiceExample> createState() => _AIServiceExampleState();
}

class _AIServiceExampleState extends State<AIServiceExample> {
  final TextEditingController _inputController = TextEditingController();
  String _result = '';
  bool _isLoading = false;
  String _selectedFunction = 'summarize';

  // معرف المحادثة للحفاظ على السياق
  final String _conversationId =
      DateTime.now().millisecondsSinceEpoch.toString();

  // الوظائف المتاحة
  final Map<String, Map<String, dynamic>> _functions = {
    'summarize': {
      'title': 'تلخيص النص',
      'icon': Icons.summarize,
      'color': AppColors.accentBlue,
      'action': (String input) => AIService.summarizeText(input),
    },
    'write': {
      'title': 'مساعدة في الكتابة',
      'icon': Icons.edit,
      'color': AppColors.accentGreen,
      'action':
          (String input) =>
              AIService.helpMeWrite(input, style: 'creative', language: 'ar'),
    },
    'plan': {
      'title': 'إنشاء خطة',
      'icon': Icons.calendar_today,
      'color': AppColors.accentOrange,
      'action': (String input) => AIService.makeAPlan(input, timeframe: 30),
    },
    'analyze': {
      'title': 'تحليل البيانات',
      'icon': Icons.analytics,
      'color': AppColors.accentRed,
      'action':
          (String input) =>
              AIService.analyzeData(input, analysisType: 'statistical'),
    },
    'chat': {
      'title': 'محادثة',
      'icon': Icons.chat,
      'color': AppColors.primaryPurple,
      'action':
          (String input) => AIService.chat(
            input,
            conversationId: DateTime.now().millisecondsSinceEpoch.toString(),
          ),
    },
  };

  Future<void> _executeFunction() async {
    final input = _inputController.text.trim();
    if (input.isEmpty) {
      ErrorHandler.showSnackBarError(context, Exception('الرجاء إدخال نص'));
      return;
    }

    setState(() {
      _isLoading = true;
      _result = '';
    });

    try {
      // تنفيذ الوظيفة المحددة مع معالجة الأخطاء
      final result = await ErrorHandler.handleWithRetry(
        operation: () async {
          final function = _functions[_selectedFunction]!;
          final action =
              function['action'] as Future<AIResult> Function(String);
          return await action(input);
        },
        maxRetries: 3,
        onRetry: (error, attempt) {
          debugPrint('محاولة رقم $attempt بعد خطأ: $error');
        },
      );

      setState(() {
        _result = result.content;
        _isLoading = false;
      });

      // عرض معلومات إضافية إذا كانت متاحة
      if (result.tokensUsed != null) {
        debugPrint('عدد الرموز المستخدمة: ${result.tokensUsed}');
      }
      if (result.processingTime != null) {
        debugPrint(
          'وقت المعالجة: ${result.processingTime!.inMilliseconds} مللي ثانية',
        );
      }
    } catch (error) {
      setState(() {
        _isLoading = false;
      });

      // معالجة الخطأ وعرضه للمستخدم
      ErrorHandler.showError(context, error);
      ErrorHandler.logError(error);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.darkPurple,
        title: Text(
          'مثال على خدمة الذكاء الاصطناعي',
          style: AppTextStyles.heading2,
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // اختيار الوظيفة
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.darkGrey,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'اختر الوظيفة:',
                    style: AppTextStyles.body.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  Wrap(
                    spacing: 8,
                    runSpacing: 8,
                    children:
                        _functions.entries.map((entry) {
                          final isSelected = _selectedFunction == entry.key;
                          final function = entry.value;

                          return ChoiceChip(
                            label: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  function['icon'] as IconData,
                                  size: 16,
                                  color:
                                      isSelected
                                          ? Colors.white
                                          : function['color'] as Color,
                                ),
                                const SizedBox(width: 4),
                                Text(function['title'] as String),
                              ],
                            ),
                            selected: isSelected,
                            selectedColor: function['color'] as Color,
                            backgroundColor: AppColors.midGrey,
                            labelStyle: TextStyle(
                              color: isSelected ? Colors.white : Colors.white70,
                            ),
                            onSelected: (selected) {
                              if (selected) {
                                setState(() {
                                  _selectedFunction = entry.key;
                                });
                              }
                            },
                          );
                        }).toList(),
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // حقل الإدخال
            Container(
              decoration: BoxDecoration(
                color: AppColors.darkGrey,
                borderRadius: BorderRadius.circular(16),
              ),
              child: TextField(
                controller: _inputController,
                maxLines: 5,
                decoration: InputDecoration(
                  hintText: 'أدخل النص هنا...',
                  hintStyle: TextStyle(color: Colors.white54),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                ),
                style: const TextStyle(color: Colors.white),
              ),
            ),

            const SizedBox(height: 16),

            // زر التنفيذ
            ElevatedButton(
              onPressed: _isLoading ? null : _executeFunction,
              style: ElevatedButton.styleFrom(
                backgroundColor:
                    _functions[_selectedFunction]!['color'] as Color,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(16),
                ),
              ),
              child:
                  _isLoading
                      ? const SizedBox(
                        height: 20,
                        width: 20,
                        child: CircularProgressIndicator(
                          color: Colors.white,
                          strokeWidth: 2,
                        ),
                      )
                      : Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            _functions[_selectedFunction]!['icon'] as IconData,
                            color: Colors.white,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'تنفيذ ${_functions[_selectedFunction]!['title']}',
                            style: AppTextStyles.button,
                          ),
                        ],
                      ),
            ),

            const SizedBox(height: 16),

            // عرض النتيجة
            if (_result.isNotEmpty)
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: AppColors.darkGrey,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: (_functions[_selectedFunction]!['color'] as Color)
                          .withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          PulsingIcon(
                            icon: Icons.check_circle,
                            color: AppColors.success,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'النتيجة:',
                            style: AppTextStyles.body.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const Spacer(),
                          IconButton(
                            icon: const Icon(Icons.copy, size: 20),
                            color: Colors.white54,
                            onPressed: () {
                              // نسخ النتيجة
                              // Clipboard.setData(ClipboardData(text: _result));
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(
                                  content: Text('تم نسخ النتيجة'),
                                  duration: Duration(seconds: 2),
                                ),
                              );
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Expanded(
                        child: SingleChildScrollView(
                          child: Text(_result, style: AppTextStyles.body),
                        ),
                      ),
                    ],
                  ),
                ),
              ),

            // عرض سجل المحادثة (للمحادثات فقط)
            if (_selectedFunction == 'chat' && _result.isNotEmpty)
              TextButton(
                onPressed: () {
                  final history = AIService.getConversationHistory(
                    _conversationId,
                  );
                  showDialog(
                    context: context,
                    builder:
                        (context) => Dialog(
                          backgroundColor: AppColors.darkGrey,
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'سجل المحادثة',
                                  style: AppTextStyles.heading2,
                                ),
                                const SizedBox(height: 16),
                                ...history.map(
                                  (message) => Padding(
                                    padding: const EdgeInsets.only(bottom: 8),
                                    child: Row(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        Text(
                                          message.role == 'user'
                                              ? 'أنت: '
                                              : 'AI: ',
                                          style: AppTextStyles.body.copyWith(
                                            fontWeight: FontWeight.bold,
                                            color:
                                                message.role == 'user'
                                                    ? AppColors.accentBlue
                                                    : AppColors.accentGreen,
                                          ),
                                        ),
                                        Expanded(
                                          child: Text(
                                            message.content,
                                            style: AppTextStyles.body,
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                  );
                },
                child: Text(
                  'عرض سجل المحادثة',
                  style: TextStyle(color: AppColors.lightPurple),
                ),
              ),
          ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _inputController.dispose();
    super.dispose();
  }
}
