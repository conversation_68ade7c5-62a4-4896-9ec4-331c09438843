import 'package:flutter/foundation.dart';
import '../storage/storage_service.dart';

/// خدمة قاعدة البيانات السحابية المتقدمة
class CloudDatabaseService {
  static const String _collectionsKey = 'cloud_collections';
  static const String _documentsKey = 'cloud_documents';
  static const String _syncQueueKey = 'sync_queue';
  
  static bool _isInitialized = false;
  static bool _isConnected = false;
  static Map<String, CloudCollection> _collections = {};
  static List<SyncOperation> _syncQueue = [];

  /// تهيئة خدمة قاعدة البيانات السحابية
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadCollections();
      await _loadSyncQueue();
      await _checkConnection();
      
      _isInitialized = true;
      debugPrint('🗄️ تم تهيئة خدمة قاعدة البيانات السحابية');
      
      // بدء المزامنة التلقائية
      _startAutoSync();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة قاعدة البيانات السحابية: $e');
    }
  }

  /// إنشاء مجموعة جديدة
  static Future<CloudCollection> createCollection(String name) async {
    final collection = CloudCollection(
      id: 'col_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
    
    _collections[collection.id] = collection;
    await _saveCollections();
    
    debugPrint('✅ تم إنشاء مجموعة: $name');
    return collection;
  }

  /// الحصول على مجموعة
  static CloudCollection? getCollection(String collectionId) {
    return _collections[collectionId];
  }

  /// الحصول على جميع المجموعات
  static List<CloudCollection> getAllCollections() {
    return _collections.values.toList();
  }

  /// إضافة مستند إلى مجموعة
  static Future<CloudDocumentResult> addDocument({
    required String collectionId,
    required Map<String, dynamic> data,
    String? documentId,
  }) async {
    try {
      final collection = _collections[collectionId];
      if (collection == null) {
        return CloudDocumentResult(
          success: false,
          error: 'المجموعة غير موجودة',
        );
      }

      final docId = documentId ?? 'doc_${DateTime.now().millisecondsSinceEpoch}';
      final document = CloudDocument(
        id: docId,
        collectionId: collectionId,
        data: data,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      // حفظ المستند محلياً
      await _saveDocument(document);
      
      // إضافة إلى قائمة المزامنة
      if (_isConnected) {
        await _syncDocument(document, SyncOperationType.create);
      } else {
        _addToSyncQueue(SyncOperation(
          id: 'sync_${DateTime.now().millisecondsSinceEpoch}',
          type: SyncOperationType.create,
          collectionId: collectionId,
          documentId: docId,
          data: data,
          createdAt: DateTime.now(),
        ));
      }

      debugPrint('✅ تم إضافة مستند: $docId');
      
      return CloudDocumentResult(
        success: true,
        document: document,
      );
      
    } catch (e) {
      debugPrint('❌ فشل في إضافة المستند: $e');
      return CloudDocumentResult(
        success: false,
        error: 'فشل في إضافة المستند: $e',
      );
    }
  }

  /// تحديث مستند
  static Future<CloudDocumentResult> updateDocument({
    required String collectionId,
    required String documentId,
    required Map<String, dynamic> data,
  }) async {
    try {
      final existingDoc = await getDocument(collectionId, documentId);
      if (existingDoc == null) {
        return CloudDocumentResult(
          success: false,
          error: 'المستند غير موجود',
        );
      }

      final updatedDocument = CloudDocument(
        id: documentId,
        collectionId: collectionId,
        data: data,
        createdAt: existingDoc.createdAt,
        updatedAt: DateTime.now(),
      );

      // حفظ المستند محلياً
      await _saveDocument(updatedDocument);
      
      // إضافة إلى قائمة المزامنة
      if (_isConnected) {
        await _syncDocument(updatedDocument, SyncOperationType.update);
      } else {
        _addToSyncQueue(SyncOperation(
          id: 'sync_${DateTime.now().millisecondsSinceEpoch}',
          type: SyncOperationType.update,
          collectionId: collectionId,
          documentId: documentId,
          data: data,
          createdAt: DateTime.now(),
        ));
      }

      debugPrint('✅ تم تحديث المستند: $documentId');
      
      return CloudDocumentResult(
        success: true,
        document: updatedDocument,
      );
      
    } catch (e) {
      debugPrint('❌ فشل في تحديث المستند: $e');
      return CloudDocumentResult(
        success: false,
        error: 'فشل في تحديث المستند: $e',
      );
    }
  }

  /// حذف مستند
  static Future<bool> deleteDocument(String collectionId, String documentId) async {
    try {
      // حذف المستند محلياً
      await _deleteDocument(collectionId, documentId);
      
      // إضافة إلى قائمة المزامنة
      if (_isConnected) {
        await _syncDeleteDocument(collectionId, documentId);
      } else {
        _addToSyncQueue(SyncOperation(
          id: 'sync_${DateTime.now().millisecondsSinceEpoch}',
          type: SyncOperationType.delete,
          collectionId: collectionId,
          documentId: documentId,
          data: {},
          createdAt: DateTime.now(),
        ));
      }

      debugPrint('✅ تم حذف المستند: $documentId');
      return true;
      
    } catch (e) {
      debugPrint('❌ فشل في حذف المستند: $e');
      return false;
    }
  }

  /// الحصول على مستند
  static Future<CloudDocument?> getDocument(String collectionId, String documentId) async {
    try {
      final data = await StorageService.getData('${_documentsKey}_${collectionId}_$documentId');
      if (data != null) {
        return CloudDocument.fromMap(data);
      }
      return null;
    } catch (e) {
      debugPrint('❌ فشل في جلب المستند: $e');
      return null;
    }
  }

  /// الحصول على جميع مستندات مجموعة
  static Future<List<CloudDocument>> getDocuments(String collectionId) async {
    try {
      final documents = <CloudDocument>[];
      
      // محاكاة جلب المستندات
      for (int i = 0; i < 10; i++) {
        final doc = CloudDocument(
          id: 'doc_$i',
          collectionId: collectionId,
          data: {
            'title': 'مستند $i',
            'content': 'محتوى المستند $i',
            'index': i,
          },
          createdAt: DateTime.now().subtract(Duration(days: i)),
          updatedAt: DateTime.now().subtract(Duration(hours: i)),
        );
        documents.add(doc);
      }
      
      return documents;
    } catch (e) {
      debugPrint('❌ فشل في جلب المستندات: $e');
      return [];
    }
  }

  /// البحث في المستندات
  static Future<List<CloudDocument>> searchDocuments({
    required String collectionId,
    required String query,
    Map<String, dynamic>? filters,
  }) async {
    try {
      final allDocuments = await getDocuments(collectionId);
      
      // تطبيق البحث والفلاتر
      final filteredDocuments = allDocuments.where((doc) {
        // البحث في النص
        final searchMatch = doc.data.values.any((value) =>
            value.toString().toLowerCase().contains(query.toLowerCase()));
        
        // تطبيق الفلاتر
        bool filterMatch = true;
        if (filters != null) {
          for (final entry in filters.entries) {
            if (doc.data[entry.key] != entry.value) {
              filterMatch = false;
              break;
            }
          }
        }
        
        return searchMatch && filterMatch;
      }).toList();
      
      debugPrint('🔍 تم العثور على ${filteredDocuments.length} مستند');
      return filteredDocuments;
      
    } catch (e) {
      debugPrint('❌ فشل في البحث: $e');
      return [];
    }
  }

  /// مزامنة البيانات مع السحابة
  static Future<DatabaseSyncResult> syncDatabase() async {
    try {
      debugPrint('🔄 بدء مزامنة قاعدة البيانات...');
      
      int syncedCount = 0;
      int failedCount = 0;

      // معالجة قائمة المزامنة
      for (final operation in List.from(_syncQueue)) {
        try {
          bool success = false;
          
          switch (operation.type) {
            case SyncOperationType.create:
              final document = CloudDocument(
                id: operation.documentId,
                collectionId: operation.collectionId,
                data: operation.data,
                createdAt: operation.createdAt,
                updatedAt: operation.createdAt,
              );
              success = await _syncDocument(document, SyncOperationType.create);
              break;
              
            case SyncOperationType.update:
              final document = CloudDocument(
                id: operation.documentId,
                collectionId: operation.collectionId,
                data: operation.data,
                createdAt: operation.createdAt,
                updatedAt: DateTime.now(),
              );
              success = await _syncDocument(document, SyncOperationType.update);
              break;
              
            case SyncOperationType.delete:
              success = await _syncDeleteDocument(operation.collectionId, operation.documentId);
              break;
          }
          
          if (success) {
            _syncQueue.remove(operation);
            syncedCount++;
          } else {
            failedCount++;
          }
        } catch (e) {
          failedCount++;
          debugPrint('❌ فشل في مزامنة العملية: $e');
        }
      }

      await _saveSyncQueue();
      
      debugPrint('✅ اكتملت مزامنة قاعدة البيانات - نجح: $syncedCount، فشل: $failedCount');
      
      return DatabaseSyncResult(
        success: true,
        syncedCount: syncedCount,
        failedCount: failedCount,
        syncTime: DateTime.now(),
      );
      
    } catch (e) {
      debugPrint('❌ فشل في مزامنة قاعدة البيانات: $e');
      return DatabaseSyncResult(
        success: false,
        error: 'فشل في مزامنة قاعدة البيانات: $e',
      );
    }
  }

  /// الاستماع للتغييرات في الوقت الفعلي
  static Stream<CloudDocument> listenToDocument(String collectionId, String documentId) {
    // محاكاة الاستماع للتغييرات
    return Stream.periodic(const Duration(seconds: 5), (count) {
      return CloudDocument(
        id: documentId,
        collectionId: collectionId,
        data: {
          'title': 'مستند محدث',
          'content': 'تم التحديث في ${DateTime.now()}',
          'updateCount': count,
        },
        createdAt: DateTime.now().subtract(const Duration(hours: 1)),
        updatedAt: DateTime.now(),
      );
    });
  }

  /// الاستماع لتغييرات المجموعة
  static Stream<List<CloudDocument>> listenToCollection(String collectionId) {
    // محاكاة الاستماع لتغييرات المجموعة
    return Stream.periodic(const Duration(seconds: 10), (count) async {
      return await getDocuments(collectionId);
    }).asyncMap((future) => future);
  }

  // Private methods

  /// فحص حالة الاتصال
  static Future<void> _checkConnection() async {
    try {
      // محاكاة فحص الاتصال
      await Future.delayed(const Duration(milliseconds: 500));
      _isConnected = true;
    } catch (e) {
      _isConnected = false;
    }
  }

  /// بدء المزامنة التلقائية
  static void _startAutoSync() {
    // مزامنة كل 3 دقائق
    Stream.periodic(const Duration(minutes: 3)).listen((_) async {
      if (_isConnected && _syncQueue.isNotEmpty) {
        await syncDatabase();
      }
    });
  }

  /// حفظ المجموعات
  static Future<void> _saveCollections() async {
    final data = _collections.map((key, value) => MapEntry(key, value.toMap()));
    await StorageService.saveData(_collectionsKey, data);
  }

  /// تحميل المجموعات
  static Future<void> _loadCollections() async {
    try {
      final data = await StorageService.getData(_collectionsKey);
      if (data != null && data is Map) {
        _collections = data.map((key, value) => 
            MapEntry(key, CloudCollection.fromMap(value)));
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل المجموعات: $e');
    }
  }

  /// حفظ مستند
  static Future<void> _saveDocument(CloudDocument document) async {
    await StorageService.saveData(
      '${_documentsKey}_${document.collectionId}_${document.id}',
      document.toMap(),
    );
  }

  /// حذف مستند
  static Future<void> _deleteDocument(String collectionId, String documentId) async {
    await StorageService.deleteData('${_documentsKey}_${collectionId}_$documentId');
  }

  /// مزامنة مستند
  static Future<bool> _syncDocument(CloudDocument document, SyncOperationType type) async {
    try {
      // محاكاة مزامنة المستند
      await Future.delayed(const Duration(milliseconds: 500));
      debugPrint('🔄 تم مزامنة المستند: ${document.id}');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في مزامنة المستند: $e');
      return false;
    }
  }

  /// مزامنة حذف مستند
  static Future<bool> _syncDeleteDocument(String collectionId, String documentId) async {
    try {
      // محاكاة مزامنة حذف المستند
      await Future.delayed(const Duration(milliseconds: 500));
      debugPrint('🔄 تم مزامنة حذف المستند: $documentId');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في مزامنة حذف المستند: $e');
      return false;
    }
  }

  /// إضافة عملية إلى قائمة المزامنة
  static void _addToSyncQueue(SyncOperation operation) {
    _syncQueue.add(operation);
    _saveSyncQueue();
  }

  /// حفظ قائمة المزامنة
  static Future<void> _saveSyncQueue() async {
    final data = _syncQueue.map((op) => op.toMap()).toList();
    await StorageService.saveData(_syncQueueKey, data);
  }

  /// تحميل قائمة المزامنة
  static Future<void> _loadSyncQueue() async {
    try {
      final data = await StorageService.getData(_syncQueueKey);
      if (data != null && data is List) {
        _syncQueue = data.map((item) => SyncOperation.fromMap(item)).toList();
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل قائمة المزامنة: $e');
    }
  }

  // Getters
  static bool get isInitialized => _isInitialized;
  static bool get isConnected => _isConnected;
  static int get pendingSyncCount => _syncQueue.length;
}

/// مجموعة سحابية
class CloudCollection {
  final String id;
  final String name;
  final DateTime createdAt;
  final DateTime updatedAt;

  CloudCollection({
    required this.id,
    required this.name,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory CloudCollection.fromMap(Map<String, dynamic> map) {
    return CloudCollection(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }
}

/// مستند سحابي
class CloudDocument {
  final String id;
  final String collectionId;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  final DateTime updatedAt;

  CloudDocument({
    required this.id,
    required this.collectionId,
    required this.data,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'collectionId': collectionId,
      'data': data,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory CloudDocument.fromMap(Map<String, dynamic> map) {
    return CloudDocument(
      id: map['id'] ?? '',
      collectionId: map['collectionId'] ?? '',
      data: Map<String, dynamic>.from(map['data'] ?? {}),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }
}

/// عملية مزامنة
class SyncOperation {
  final String id;
  final SyncOperationType type;
  final String collectionId;
  final String documentId;
  final Map<String, dynamic> data;
  final DateTime createdAt;

  SyncOperation({
    required this.id,
    required this.type,
    required this.collectionId,
    required this.documentId,
    required this.data,
    required this.createdAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.toString(),
      'collectionId': collectionId,
      'documentId': documentId,
      'data': data,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  factory SyncOperation.fromMap(Map<String, dynamic> map) {
    return SyncOperation(
      id: map['id'] ?? '',
      type: SyncOperationType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => SyncOperationType.create,
      ),
      collectionId: map['collectionId'] ?? '',
      documentId: map['documentId'] ?? '',
      data: Map<String, dynamic>.from(map['data'] ?? {}),
      createdAt: DateTime.parse(map['createdAt']),
    );
  }
}

/// نوع عملية المزامنة
enum SyncOperationType {
  create,
  update,
  delete,
}

/// نتيجة عملية المستند
class CloudDocumentResult {
  final bool success;
  final CloudDocument? document;
  final String? error;

  CloudDocumentResult({
    required this.success,
    this.document,
    this.error,
  });
}

/// نتيجة مزامنة قاعدة البيانات
class DatabaseSyncResult {
  final bool success;
  final int syncedCount;
  final int failedCount;
  final DateTime? syncTime;
  final String? error;

  DatabaseSyncResult({
    required this.success,
    this.syncedCount = 0,
    this.failedCount = 0,
    this.syncTime,
    this.error,
  });
}
