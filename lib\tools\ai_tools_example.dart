import 'package:flutter/material.dart';
import 'ai_tools_hub.dart';

/// مثال شامل لاستخدام جميع أدوات الذكاء الاصطناعي
class AIToolsExample {
  
  /// مثال على إنشاء الصور
  static Future<void> imageGenerationExample() async {
    print('🎨 مثال إنشاء الصور:');
    
    try {
      // إنشاء صورة بسيطة
      print('إنشاء صورة بسيطة...');
      final simpleImage = await AIToolsHub.generateImage(
        prompt: 'منظر طبيعي خلاب لغروب الشمس على البحر',
      );
      print('✅ تم إنشاء الصورة: $simpleImage');
      
      // تحسين وصف الصورة
      print('\nتحسين وصف الصورة...');
      final enhancedPrompt = await AIToolsHub.enhanceImagePrompt(
        'صورة قطة لطيفة',
      );
      print('✅ الوصف المحسن: $enhancedPrompt');
      
      // إنشاء صورة بالوصف المحسن
      print('\nإنشاء صورة بالوصف المحسن...');
      final enhancedImage = await AIToolsHub.generateImage(
        prompt: enhancedPrompt,
        size: '1024x1024',
        quality: 'hd',
        style: 'vivid',
      );
      print('✅ تم إنشاء الصورة المحسنة: $enhancedImage');
      
    } catch (e) {
      print('❌ خطأ في إنشاء الصور: $e');
    }
  }
  
  /// مثال على تلخيص النصوص
  static Future<void> textSummarizationExample() async {
    print('\n📝 مثال تلخيص النصوص:');
    
    const longText = '''
    الذكاء الاصطناعي هو محاكاة الذكاء البشري في الآلات المبرمجة للتفكير والتعلم مثل البشر.
    يمكن تطبيق هذا المصطلح أيضًا على أي آلة تظهر سمات مرتبطة بالعقل البشري مثل التعلم وحل المشكلات.
    الخاصية المثالية للذكاء الاصطناعي هي قدرته على ترشيد واتخاذ الإجراءات التي لديها أفضل فرصة لتحقيق هدف محدد.
    يشمل الذكاء الاصطناعي التعلم الآلي، حيث تتعلم برامج الكمبيوتر تلقائيًا وتتكيف مع البيانات الجديدة دون مساعدة بشرية.
    تقنيات التعلم العميق تمكن هذا التعلم التلقائي من خلال امتصاص كميات هائلة من البيانات غير المنظمة مثل النصوص والصور أو الفيديو.
    ''';
    
    try {
      // تلخيص شامل
      print('تلخيص شامل...');
      final comprehensiveSummary = await AIToolsHub.summarizeText(
        text: longText,
        style: 'comprehensive',
        maxLength: 150,
      );
      print('✅ التلخيص الشامل: $comprehensiveSummary');
      
      // تلخيص في نقاط
      print('\nتلخيص في نقاط...');
      final bulletSummary = await AIToolsHub.summarizeText(
        text: longText,
        style: 'bullet_points',
        maxLength: 100,
      );
      print('✅ التلخيص في نقاط: $bulletSummary');
      
    } catch (e) {
      print('❌ خطأ في تلخيص النصوص: $e');
    }
  }
  
  /// مثال على تحليل البيانات
  static Future<void> dataAnalysisExample() async {
    print('\n📊 مثال تحليل البيانات:');
    
    const sampleData = '''
    الشهر,المبيعات,العملاء,المنتجات
    يناير,15000,120,45
    فبراير,18000,145,52
    مارس,22000,180,60
    أبريل,25000,210,68
    مايو,28000,240,75
    يونيو,32000,280,82
    ''';
    
    try {
      // تحليل إحصائي
      print('تحليل إحصائي...');
      final statisticalAnalysis = await AIToolsHub.analyzeData(
        data: sampleData,
        analysisType: 'statistical',
        includeCharts: true,
        includeRecommendations: true,
      );
      print('✅ التحليل الإحصائي: ${statisticalAnalysis['analysis']}');
      print('📈 الرموز المستخدمة: ${statisticalAnalysis['tokens_used']}');
      
      // تحليل الاتجاهات
      print('\nتحليل الاتجاهات...');
      final trendAnalysis = await AIToolsHub.analyzeData(
        data: sampleData,
        analysisType: 'trend',
        includeRecommendations: true,
      );
      print('✅ تحليل الاتجاهات: ${trendAnalysis['analysis']}');
      
    } catch (e) {
      print('❌ خطأ في تحليل البيانات: $e');
    }
  }
  
  /// مثال على إنشاء الخطط
  static Future<void> planCreationExample() async {
    print('\n📅 مثال إنشاء الخطط:');
    
    try {
      // خطة تعلم البرمجة
      print('إنشاء خطة تعلم البرمجة...');
      final learningPlan = await AIToolsHub.createPlan(
        goal: 'تعلم البرمجة بلغة Flutter',
        timeframeDays: 60,
        constraints: 'ساعتان يومياً فقط',
        priority: 'high',
        resources: ['كمبيوتر', 'إنترنت', 'كتب برمجة'],
      );
      print('✅ خطة التعلم: ${learningPlan['plan']}');
      
      // خطة مشروع تجاري
      print('\nإنشاء خطة مشروع تجاري...');
      final businessPlan = await AIToolsHub.createPlan(
        goal: 'بدء متجر إلكتروني',
        timeframeDays: 90,
        constraints: 'ميزانية محدودة 10000 ريال',
        priority: 'medium',
        resources: ['فريق صغير', 'منصة تجارة إلكترونية'],
      );
      print('✅ خطة المشروع: ${businessPlan['plan']}');
      
    } catch (e) {
      print('❌ خطأ في إنشاء الخطط: $e');
    }
  }
  
  /// مثال على مساعدة الكتابة
  static Future<void> writingAssistanceExample() async {
    print('\n✍️ مثال مساعدة الكتابة:');
    
    try {
      // كتابة إبداعية
      print('كتابة إبداعية...');
      final creativeWriting = await AIToolsHub.assistWriting(
        topic: 'مستقبل التكنولوجيا في حياتنا',
        style: 'creative',
        targetLength: 300,
        audience: 'الجمهور العام',
        tone: 'ملهم ومتفائل',
      );
      print('✅ الكتابة الإبداعية: ${creativeWriting['content']}');
      
      // كتابة أكاديمية
      print('\nكتابة أكاديمية...');
      final academicWriting = await AIToolsHub.assistWriting(
        topic: 'تأثير الذكاء الاصطناعي على التعليم',
        style: 'academic',
        targetLength: 500,
        audience: 'طلاب الجامعة',
        tone: 'علمي ومنطقي',
      );
      print('✅ الكتابة الأكاديمية: ${academicWriting['content']}');
      
    } catch (e) {
      print('❌ خطأ في مساعدة الكتابة: $e');
    }
  }
  
  /// مثال على المحادثة الذكية
  static Future<void> smartChatExample() async {
    print('\n💬 مثال المحادثة الذكية:');
    
    try {
      // محادثة بسيطة
      print('محادثة بسيطة...');
      final response1 = await AIToolsHub.smartChat(
        message: 'مرحباً، ما هي أفضل طريقة لتعلم البرمجة؟',
        conversationId: 'example_conversation',
      );
      print('✅ الرد الأول: ${response1.content}');
      
      // محادثة مع سياق
      print('\nمحادثة مع سياق...');
      final response2 = await AIToolsHub.smartChat(
        message: 'هل يمكنك أن تنصحني بلغة برمجة محددة؟',
        conversationId: 'example_conversation',
        context: [response1], // إضافة السياق السابق
      );
      print('✅ الرد الثاني: ${response2.content}');
      
    } catch (e) {
      print('❌ خطأ في المحادثة الذكية: $e');
    }
  }
  
  /// مثال على الميزات المتقدمة
  static Future<void> advancedFeaturesExample() async {
    print('\n🔧 مثال الميزات المتقدمة:');
    
    try {
      // الحصول على قائمة الأدوات
      print('الحصول على قائمة الأدوات...');
      final tools = AIToolsHub.getAvailableTools();
      print('✅ الأدوات المتاحة:');
      for (final tool in tools) {
        print('   - ${tool['name']}: ${tool['description']}');
      }
      
      // التحقق من توفر الأدوات
      print('\nالتحقق من توفر الأدوات...');
      final availability = await AIToolsHub.checkToolsAvailability();
      print('✅ حالة الأدوات:');
      availability.forEach((toolId, isAvailable) {
        final status = isAvailable ? '✅ متاح' : '❌ غير متاح';
        print('   - $toolId: $status');
      });
      
      // الحصول على أمثلة الاستخدام
      print('\nالحصول على أمثلة الاستخدام...');
      final examples = AIToolsHub.getUsageExamples();
      print('✅ أمثلة إنشاء الصور:');
      for (final example in examples['image_generation']!) {
        print('   - $example');
      }
      
      // إحصائيات الاستخدام
      print('\nإحصائيات الاستخدام...');
      final stats = AIToolsHub.getUsageStats();
      print('✅ الإحصائيات:');
      stats.forEach((key, value) {
        print('   - $key: $value');
      });
      
      // مسح الذاكرة المؤقتة
      print('\nمسح الذاكرة المؤقتة...');
      AIToolsHub.clearCache();
      print('✅ تم مسح الذاكرة المؤقتة');
      
    } catch (e) {
      print('❌ خطأ في الميزات المتقدمة: $e');
    }
  }
  
  /// تشغيل جميع الأمثلة
  static Future<void> runAllExamples() async {
    print('🚀 بدء تشغيل جميع أمثلة أدوات الذكاء الاصطناعي...\n');
    
    await imageGenerationExample();
    await textSummarizationExample();
    await dataAnalysisExample();
    await planCreationExample();
    await writingAssistanceExample();
    await smartChatExample();
    await advancedFeaturesExample();
    
    print('\n🎉 تم الانتهاء من جميع الأمثلة!');
  }
}

/// مثال على استخدام الأدوات في تطبيق Flutter
class AIToolsExampleWidget extends StatefulWidget {
  const AIToolsExampleWidget({Key? key}) : super(key: key);

  @override
  State<AIToolsExampleWidget> createState() => _AIToolsExampleWidgetState();
}

class _AIToolsExampleWidgetState extends State<AIToolsExampleWidget> {
  String _result = '';
  bool _isLoading = false;

  Future<void> _testImageGeneration() async {
    setState(() {
      _isLoading = true;
      _result = 'جاري إنشاء الصورة...';
    });

    try {
      final imageUrl = await AIToolsHub.generateImage(
        prompt: 'منظر طبيعي خلاب لغروب الشمس على البحر',
      );
      setState(() {
        _result = 'تم إنشاء الصورة: $imageUrl';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _result = 'خطأ: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _testTextSummarization() async {
    setState(() {
      _isLoading = true;
      _result = 'جاري تلخيص النص...';
    });

    try {
      const text = '''
      الذكاء الاصطناعي هو محاكاة الذكاء البشري في الآلات المبرمجة للتفكير والتعلم مثل البشر.
      يمكن تطبيق هذا المصطلح أيضًا على أي آلة تظهر سمات مرتبطة بالعقل البشري مثل التعلم وحل المشكلات.
      ''';
      
      final summary = await AIToolsHub.summarizeText(
        text: text,
        style: 'comprehensive',
        maxLength: 100,
      );
      
      setState(() {
        _result = 'التلخيص: $summary';
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _result = 'خطأ: $e';
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('مثال أدوات الذكاء الاصطناعي'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            ElevatedButton(
              onPressed: _isLoading ? null : _testImageGeneration,
              child: const Text('اختبار إنشاء الصور'),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: _isLoading ? null : _testTextSummarization,
              child: const Text('اختبار تلخيص النصوص'),
            ),
            const SizedBox(height: 32),
            if (_isLoading)
              const CircularProgressIndicator()
            else
              Expanded(
                child: SingleChildScrollView(
                  child: Text(
                    _result,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
              ),
          ],
        ),
      ),
    );
  }
}
