import 'package:equatable/equatable.dart';
import 'package:uuid/uuid.dart';

/// نموذج المحادثة
class ConversationModel extends Equatable {
  final String id;
  final String title;
  final List<MessageModel> messages;
  final DateTime createdAt;
  final DateTime updatedAt;
  final ConversationType type;
  final Map<String, dynamic> metadata;

  const ConversationModel({
    required this.id,
    required this.title,
    required this.messages,
    required this.createdAt,
    required this.updatedAt,
    required this.type,
    required this.metadata,
  });

  factory ConversationModel.create({
    String? title,
    ConversationType type = ConversationType.chat,
    Map<String, dynamic>? metadata,
  }) {
    final now = DateTime.now();
    return ConversationModel(
      id: const Uuid().v4(),
      title: title ?? 'محادثة جديدة',
      messages: [],
      createdAt: now,
      updatedAt: now,
      type: type,
      metadata: metadata ?? {},
    );
  }

  factory ConversationModel.fromJson(Map<String, dynamic> json) {
    return ConversationModel(
      id: json['id'] ?? '',
      title: json['title'] ?? '',
      messages: (json['messages'] as List<dynamic>?)
              ?.map((m) => MessageModel.fromJson(m))
              .toList() ??
          [],
      createdAt: DateTime.parse(json['created_at'] ?? DateTime.now().toIso8601String()),
      updatedAt: DateTime.parse(json['updated_at'] ?? DateTime.now().toIso8601String()),
      type: ConversationType.values.firstWhere(
        (t) => t.name == json['type'],
        orElse: () => ConversationType.chat,
      ),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'messages': messages.map((m) => m.toJson()).toList(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'type': type.name,
      'metadata': metadata,
    };
  }

  ConversationModel copyWith({
    String? id,
    String? title,
    List<MessageModel>? messages,
    DateTime? createdAt,
    DateTime? updatedAt,
    ConversationType? type,
    Map<String, dynamic>? metadata,
  }) {
    return ConversationModel(
      id: id ?? this.id,
      title: title ?? this.title,
      messages: messages ?? this.messages,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      type: type ?? this.type,
      metadata: metadata ?? this.metadata,
    );
  }

  ConversationModel addMessage(MessageModel message) {
    return copyWith(
      messages: [...messages, message],
      updatedAt: DateTime.now(),
    );
  }

  ConversationModel updateMessage(String messageId, MessageModel updatedMessage) {
    final updatedMessages = messages.map((m) => m.id == messageId ? updatedMessage : m).toList();
    return copyWith(
      messages: updatedMessages,
      updatedAt: DateTime.now(),
    );
  }

  ConversationModel deleteMessage(String messageId) {
    final updatedMessages = messages.where((m) => m.id != messageId).toList();
    return copyWith(
      messages: updatedMessages,
      updatedAt: DateTime.now(),
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        messages,
        createdAt,
        updatedAt,
        type,
        metadata,
      ];
}

/// نموذج الرسالة
class MessageModel extends Equatable {
  final String id;
  final String content;
  final MessageRole role;
  final DateTime timestamp;
  final MessageStatus status;
  final Map<String, dynamic> metadata;
  final List<MessageAttachment> attachments;

  const MessageModel({
    required this.id,
    required this.content,
    required this.role,
    required this.timestamp,
    required this.status,
    required this.metadata,
    required this.attachments,
  });

  factory MessageModel.create({
    required String content,
    required MessageRole role,
    MessageStatus status = MessageStatus.sent,
    Map<String, dynamic>? metadata,
    List<MessageAttachment>? attachments,
  }) {
    return MessageModel(
      id: const Uuid().v4(),
      content: content,
      role: role,
      timestamp: DateTime.now(),
      status: status,
      metadata: metadata ?? {},
      attachments: attachments ?? [],
    );
  }

  factory MessageModel.fromJson(Map<String, dynamic> json) {
    return MessageModel(
      id: json['id'] ?? '',
      content: json['content'] ?? '',
      role: MessageRole.values.firstWhere(
        (r) => r.name == json['role'],
        orElse: () => MessageRole.user,
      ),
      timestamp: DateTime.parse(json['timestamp'] ?? DateTime.now().toIso8601String()),
      status: MessageStatus.values.firstWhere(
        (s) => s.name == json['status'],
        orElse: () => MessageStatus.sent,
      ),
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
      attachments: (json['attachments'] as List<dynamic>?)
              ?.map((a) => MessageAttachment.fromJson(a))
              .toList() ??
          [],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'role': role.name,
      'timestamp': timestamp.toIso8601String(),
      'status': status.name,
      'metadata': metadata,
      'attachments': attachments.map((a) => a.toJson()).toList(),
    };
  }

  MessageModel copyWith({
    String? id,
    String? content,
    MessageRole? role,
    DateTime? timestamp,
    MessageStatus? status,
    Map<String, dynamic>? metadata,
    List<MessageAttachment>? attachments,
  }) {
    return MessageModel(
      id: id ?? this.id,
      content: content ?? this.content,
      role: role ?? this.role,
      timestamp: timestamp ?? this.timestamp,
      status: status ?? this.status,
      metadata: metadata ?? this.metadata,
      attachments: attachments ?? this.attachments,
    );
  }

  @override
  List<Object?> get props => [
        id,
        content,
        role,
        timestamp,
        status,
        metadata,
        attachments,
      ];
}

/// مرفق الرسالة
class MessageAttachment extends Equatable {
  final String id;
  final String name;
  final String type;
  final String url;
  final int size;
  final Map<String, dynamic> metadata;

  const MessageAttachment({
    required this.id,
    required this.name,
    required this.type,
    required this.url,
    required this.size,
    required this.metadata,
  });

  factory MessageAttachment.fromJson(Map<String, dynamic> json) {
    return MessageAttachment(
      id: json['id'] ?? '',
      name: json['name'] ?? '',
      type: json['type'] ?? '',
      url: json['url'] ?? '',
      size: json['size'] ?? 0,
      metadata: Map<String, dynamic>.from(json['metadata'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'type': type,
      'url': url,
      'size': size,
      'metadata': metadata,
    };
  }

  @override
  List<Object?> get props => [id, name, type, url, size, metadata];
}

/// أنواع المحادثات
enum ConversationType {
  chat,
  summarize,
  analyze,
  plan,
  writeAssist,
  imageGeneration,
}

/// أدوار الرسائل
enum MessageRole {
  user,
  assistant,
  system,
}

/// حالات الرسائل
enum MessageStatus {
  sending,
  sent,
  delivered,
  failed,
  typing,
}
