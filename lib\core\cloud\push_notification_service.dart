import 'package:flutter/foundation.dart';
import '../storage/storage_service.dart';

/// خدمة الإشعارات الفورية السحابية
class PushNotificationService {
  static const String _tokenKey = 'fcm_token';
  static const String _subscriptionsKey = 'notification_subscriptions';
  static const String _notificationHistoryKey = 'notification_history';
  
  static bool _isInitialized = false;
  static String? _fcmToken;
  static List<String> _subscriptions = [];
  static List<PushNotification> _notificationHistory = [];

  /// تهيئة خدمة الإشعارات الفورية
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadToken();
      await _loadSubscriptions();
      await _loadNotificationHistory();
      await _requestPermissions();
      await _generateToken();
      
      _isInitialized = true;
      debugPrint('🔔 تم تهيئة خدمة الإشعارات الفورية');
      
      // بدء الاستماع للإشعارات
      _startListening();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الإشعارات الفورية: $e');
    }
  }

  /// طلب أذونات الإشعارات
  static Future<bool> _requestPermissions() async {
    try {
      // محاكاة طلب الأذونات
      await Future.delayed(const Duration(milliseconds: 500));
      debugPrint('✅ تم منح أذونات الإشعارات');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في طلب أذونات الإشعارات: $e');
      return false;
    }
  }

  /// توليد رمز FCM
  static Future<void> _generateToken() async {
    try {
      // محاكاة توليد رمز FCM
      _fcmToken = 'fcm_token_${DateTime.now().millisecondsSinceEpoch}';
      await _saveToken();
      debugPrint('🔑 تم توليد رمز FCM: $_fcmToken');
    } catch (e) {
      debugPrint('❌ فشل في توليد رمز FCM: $e');
    }
  }

  /// الاشتراك في موضوع
  static Future<bool> subscribeToTopic(String topic) async {
    try {
      if (!_subscriptions.contains(topic)) {
        _subscriptions.add(topic);
        await _saveSubscriptions();
        
        // محاكاة الاشتراك في الموضوع
        await Future.delayed(const Duration(milliseconds: 300));
        
        debugPrint('✅ تم الاشتراك في الموضوع: $topic');
        return true;
      }
      return true;
    } catch (e) {
      debugPrint('❌ فشل في الاشتراك في الموضوع: $e');
      return false;
    }
  }

  /// إلغاء الاشتراك من موضوع
  static Future<bool> unsubscribeFromTopic(String topic) async {
    try {
      _subscriptions.remove(topic);
      await _saveSubscriptions();
      
      // محاكاة إلغاء الاشتراك من الموضوع
      await Future.delayed(const Duration(milliseconds: 300));
      
      debugPrint('✅ تم إلغاء الاشتراك من الموضوع: $topic');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في إلغاء الاشتراك من الموضوع: $e');
      return false;
    }
  }

  /// إرسال إشعار مخصص
  static Future<bool> sendCustomNotification({
    required String title,
    required String body,
    String? imageUrl,
    Map<String, dynamic>? data,
    List<String>? targetTokens,
    String? topic,
  }) async {
    try {
      final notification = PushNotification(
        id: 'notif_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        body: body,
        imageUrl: imageUrl,
        data: data ?? {},
        topic: topic,
        targetTokens: targetTokens ?? [],
        sentAt: DateTime.now(),
        status: NotificationStatus.sent,
      );

      // محاكاة إرسال الإشعار
      await Future.delayed(const Duration(milliseconds: 500));
      
      // إضافة إلى التاريخ
      _notificationHistory.add(notification);
      await _saveNotificationHistory();
      
      debugPrint('📤 تم إرسال الإشعار: $title');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في إرسال الإشعار: $e');
      return false;
    }
  }

  /// إرسال إشعار مجدول
  static Future<bool> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledTime,
    String? imageUrl,
    Map<String, dynamic>? data,
  }) async {
    try {
      final notification = PushNotification(
        id: 'scheduled_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        body: body,
        imageUrl: imageUrl,
        data: data ?? {},
        scheduledTime: scheduledTime,
        sentAt: DateTime.now(),
        status: NotificationStatus.scheduled,
      );

      // محاكاة جدولة الإشعار
      await Future.delayed(const Duration(milliseconds: 300));
      
      // إضافة إلى التاريخ
      _notificationHistory.add(notification);
      await _saveNotificationHistory();
      
      debugPrint('⏰ تم جدولة الإشعار: $title');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في جدولة الإشعار: $e');
      return false;
    }
  }

  /// إرسال إشعار للمجموعة
  static Future<bool> sendGroupNotification({
    required String title,
    required String body,
    required List<String> userIds,
    String? imageUrl,
    Map<String, dynamic>? data,
  }) async {
    try {
      final notification = PushNotification(
        id: 'group_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        body: body,
        imageUrl: imageUrl,
        data: data ?? {},
        targetTokens: userIds,
        sentAt: DateTime.now(),
        status: NotificationStatus.sent,
      );

      // محاكاة إرسال الإشعار للمجموعة
      await Future.delayed(const Duration(milliseconds: 800));
      
      // إضافة إلى التاريخ
      _notificationHistory.add(notification);
      await _saveNotificationHistory();
      
      debugPrint('👥 تم إرسال الإشعار للمجموعة: ${userIds.length} مستخدم');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في إرسال الإشعار للمجموعة: $e');
      return false;
    }
  }

  /// إرسال إشعار تفاعلي
  static Future<bool> sendInteractiveNotification({
    required String title,
    required String body,
    required List<NotificationAction> actions,
    String? imageUrl,
    Map<String, dynamic>? data,
  }) async {
    try {
      final notification = PushNotification(
        id: 'interactive_${DateTime.now().millisecondsSinceEpoch}',
        title: title,
        body: body,
        imageUrl: imageUrl,
        data: data ?? {},
        actions: actions,
        sentAt: DateTime.now(),
        status: NotificationStatus.sent,
      );

      // محاكاة إرسال الإشعار التفاعلي
      await Future.delayed(const Duration(milliseconds: 600));
      
      // إضافة إلى التاريخ
      _notificationHistory.add(notification);
      await _saveNotificationHistory();
      
      debugPrint('🎯 تم إرسال الإشعار التفاعلي: $title');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في إرسال الإشعار التفاعلي: $e');
      return false;
    }
  }

  /// الحصول على تاريخ الإشعارات
  static List<PushNotification> getNotificationHistory({
    int? limit,
    NotificationStatus? status,
  }) {
    var history = _notificationHistory;
    
    if (status != null) {
      history = history.where((notif) => notif.status == status).toList();
    }
    
    // ترتيب حسب التاريخ (الأحدث أولاً)
    history.sort((a, b) => b.sentAt.compareTo(a.sentAt));
    
    if (limit != null && limit > 0) {
      history = history.take(limit).toList();
    }
    
    return history;
  }

  /// الحصول على إحصائيات الإشعارات
  static NotificationStats getNotificationStats() {
    final total = _notificationHistory.length;
    final sent = _notificationHistory.where((n) => n.status == NotificationStatus.sent).length;
    final delivered = _notificationHistory.where((n) => n.status == NotificationStatus.delivered).length;
    final failed = _notificationHistory.where((n) => n.status == NotificationStatus.failed).length;
    final scheduled = _notificationHistory.where((n) => n.status == NotificationStatus.scheduled).length;
    
    return NotificationStats(
      totalSent: total,
      delivered: delivered,
      failed: failed,
      scheduled: scheduled,
      deliveryRate: total > 0 ? (delivered / total) * 100 : 0,
    );
  }

  /// حذف إشعار من التاريخ
  static Future<bool> deleteNotification(String notificationId) async {
    try {
      _notificationHistory.removeWhere((notif) => notif.id == notificationId);
      await _saveNotificationHistory();
      debugPrint('🗑️ تم حذف الإشعار: $notificationId');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في حذف الإشعار: $e');
      return false;
    }
  }

  /// مسح جميع الإشعارات
  static Future<bool> clearAllNotifications() async {
    try {
      _notificationHistory.clear();
      await _saveNotificationHistory();
      debugPrint('🧹 تم مسح جميع الإشعارات');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في مسح الإشعارات: $e');
      return false;
    }
  }

  /// بدء الاستماع للإشعارات
  static void _startListening() {
    // محاكاة استقبال إشعارات دورية
    Stream.periodic(const Duration(minutes: 10)).listen((_) {
      _simulateIncomingNotification();
    });
  }

  /// محاكاة استقبال إشعار
  static void _simulateIncomingNotification() {
    final notifications = [
      'لديك رسالة جديدة',
      'تم إنجاز مهمة بنجاح',
      'تحديث جديد متاح',
      'تذكير: لديك موعد قريب',
      'تم رفع ملف جديد',
    ];
    
    final randomTitle = notifications[DateTime.now().millisecond % notifications.length];
    
    final notification = PushNotification(
      id: 'incoming_${DateTime.now().millisecondsSinceEpoch}',
      title: randomTitle,
      body: 'تفاصيل الإشعار هنا',
      data: {'type': 'auto_generated'},
      sentAt: DateTime.now(),
      status: NotificationStatus.delivered,
    );
    
    _notificationHistory.add(notification);
    _saveNotificationHistory();
    
    debugPrint('📨 تم استقبال إشعار جديد: $randomTitle');
  }

  // Private methods

  /// حفظ الرمز
  static Future<void> _saveToken() async {
    if (_fcmToken != null) {
      await StorageService.saveData(_tokenKey, _fcmToken);
    }
  }

  /// تحميل الرمز
  static Future<void> _loadToken() async {
    try {
      _fcmToken = await StorageService.getData(_tokenKey);
    } catch (e) {
      debugPrint('❌ فشل في تحميل الرمز: $e');
    }
  }

  /// حفظ الاشتراكات
  static Future<void> _saveSubscriptions() async {
    await StorageService.saveData(_subscriptionsKey, _subscriptions);
  }

  /// تحميل الاشتراكات
  static Future<void> _loadSubscriptions() async {
    try {
      final data = await StorageService.getData(_subscriptionsKey);
      if (data != null && data is List) {
        _subscriptions = List<String>.from(data);
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل الاشتراكات: $e');
    }
  }

  /// حفظ تاريخ الإشعارات
  static Future<void> _saveNotificationHistory() async {
    final data = _notificationHistory.map((notif) => notif.toMap()).toList();
    await StorageService.saveData(_notificationHistoryKey, data);
  }

  /// تحميل تاريخ الإشعارات
  static Future<void> _loadNotificationHistory() async {
    try {
      final data = await StorageService.getData(_notificationHistoryKey);
      if (data != null && data is List) {
        _notificationHistory = data.map((item) => PushNotification.fromMap(item)).toList();
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل تاريخ الإشعارات: $e');
    }
  }

  // Getters
  static bool get isInitialized => _isInitialized;
  static String? get fcmToken => _fcmToken;
  static List<String> get subscriptions => List.unmodifiable(_subscriptions);
  static int get notificationCount => _notificationHistory.length;
}

/// إشعار فوري
class PushNotification {
  final String id;
  final String title;
  final String body;
  final String? imageUrl;
  final Map<String, dynamic> data;
  final String? topic;
  final List<String> targetTokens;
  final List<NotificationAction> actions;
  final DateTime? scheduledTime;
  final DateTime sentAt;
  final NotificationStatus status;

  PushNotification({
    required this.id,
    required this.title,
    required this.body,
    this.imageUrl,
    required this.data,
    this.topic,
    this.targetTokens = const [],
    this.actions = const [],
    this.scheduledTime,
    required this.sentAt,
    required this.status,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'imageUrl': imageUrl,
      'data': data,
      'topic': topic,
      'targetTokens': targetTokens,
      'actions': actions.map((action) => action.toMap()).toList(),
      'scheduledTime': scheduledTime?.toIso8601String(),
      'sentAt': sentAt.toIso8601String(),
      'status': status.toString(),
    };
  }

  factory PushNotification.fromMap(Map<String, dynamic> map) {
    return PushNotification(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      imageUrl: map['imageUrl'],
      data: Map<String, dynamic>.from(map['data'] ?? {}),
      topic: map['topic'],
      targetTokens: List<String>.from(map['targetTokens'] ?? []),
      actions: (map['actions'] as List? ?? [])
          .map((action) => NotificationAction.fromMap(action))
          .toList(),
      scheduledTime: map['scheduledTime'] != null 
          ? DateTime.parse(map['scheduledTime']) 
          : null,
      sentAt: DateTime.parse(map['sentAt']),
      status: NotificationStatus.values.firstWhere(
        (e) => e.toString() == map['status'],
        orElse: () => NotificationStatus.sent,
      ),
    );
  }
}

/// إجراء الإشعار
class NotificationAction {
  final String id;
  final String title;
  final String? icon;

  NotificationAction({
    required this.id,
    required this.title,
    this.icon,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'icon': icon,
    };
  }

  factory NotificationAction.fromMap(Map<String, dynamic> map) {
    return NotificationAction(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      icon: map['icon'],
    );
  }
}

/// حالة الإشعار
enum NotificationStatus {
  sent,
  delivered,
  failed,
  scheduled,
}

/// إحصائيات الإشعارات
class NotificationStats {
  final int totalSent;
  final int delivered;
  final int failed;
  final int scheduled;
  final double deliveryRate;

  NotificationStats({
    required this.totalSent,
    required this.delivered,
    required this.failed,
    required this.scheduled,
    required this.deliveryRate,
  });
}
