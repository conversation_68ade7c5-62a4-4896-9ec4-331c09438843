import 'package:flutter/material.dart';

/// نموذج API خاص بميزة معينة
class FeatureApiConfig {
  final String featureId;
  final String featureName;
  final String featureNameAr;
  final IconData icon;
  final Color color;
  final String? apiKey;
  final String? baseUrl;
  final String? model;
  final String? provider;
  final bool isEnabled;
  final Map<String, dynamic> customSettings;
  final DateTime? lastUpdated;

  FeatureApiConfig({
    required this.featureId,
    required this.featureName,
    required this.featureNameAr,
    required this.icon,
    required this.color,
    this.apiKey,
    this.baseUrl,
    this.model,
    this.provider,
    this.isEnabled = false,
    this.customSettings = const {},
    this.lastUpdated,
  });

  FeatureApiConfig copyWith({
    String? featureId,
    String? featureName,
    String? featureNameAr,
    IconData? icon,
    Color? color,
    String? apiKey,
    String? baseUrl,
    String? model,
    String? provider,
    bool? isEnabled,
    Map<String, dynamic>? customSettings,
    DateTime? lastUpdated,
  }) {
    return FeatureApiConfig(
      featureId: featureId ?? this.featureId,
      featureName: featureName ?? this.featureName,
      featureNameAr: featureNameAr ?? this.featureNameAr,
      icon: icon ?? this.icon,
      color: color ?? this.color,
      apiKey: apiKey ?? this.apiKey,
      baseUrl: baseUrl ?? this.baseUrl,
      model: model ?? this.model,
      provider: provider ?? this.provider,
      isEnabled: isEnabled ?? this.isEnabled,
      customSettings: customSettings ?? this.customSettings,
      lastUpdated: lastUpdated ?? this.lastUpdated,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'featureId': featureId,
      'featureName': featureName,
      'featureNameAr': featureNameAr,
      'icon': icon.codePoint,
      'color': color.value,
      'apiKey': apiKey,
      'baseUrl': baseUrl,
      'model': model,
      'provider': provider,
      'isEnabled': isEnabled,
      'customSettings': customSettings,
      'lastUpdated': lastUpdated?.toIso8601String(),
    };
  }

  factory FeatureApiConfig.fromMap(Map<String, dynamic> map) {
    return FeatureApiConfig(
      featureId: map['featureId'] ?? '',
      featureName: map['featureName'] ?? '',
      featureNameAr: map['featureNameAr'] ?? '',
      icon: IconData(map['icon'] ?? Icons.api.codePoint, fontFamily: 'MaterialIcons'),
      color: Color(map['color'] ?? Colors.blue.value),
      apiKey: map['apiKey'],
      baseUrl: map['baseUrl'],
      model: map['model'],
      provider: map['provider'],
      isEnabled: map['isEnabled'] ?? false,
      customSettings: Map<String, dynamic>.from(map['customSettings'] ?? {}),
      lastUpdated: map['lastUpdated'] != null 
          ? DateTime.parse(map['lastUpdated']) 
          : null,
    );
  }

  bool get isConfigured => apiKey != null && apiKey!.isNotEmpty;
  
  String get statusText {
    if (!isConfigured) return 'غير مُعد';
    if (!isEnabled) return 'معطل';
    return 'نشط';
  }

  Color get statusColor {
    if (!isConfigured) return Colors.grey;
    if (!isEnabled) return Colors.orange;
    return Colors.green;
  }
}

/// أنواع الميزات المدعومة
enum FeatureType {
  textSummarization,
  imageGeneration,
  dataAnalysis,
  planCreation,
  writingAssistance,
  smartTranslation,
  imageAnalysis,
  voiceChat,
  smartBrowsing,
  codeGeneration,
}

/// إعدادات مخصصة لكل نوع ميزة
class FeatureSettings {
  static Map<String, dynamic> getDefaultSettings(FeatureType type) {
    switch (type) {
      case FeatureType.textSummarization:
        return {
          'maxLength': 500,
          'style': 'comprehensive',
          'language': 'ar',
        };
      case FeatureType.imageGeneration:
        return {
          'size': '1024x1024',
          'quality': 'standard',
          'style': 'natural',
        };
      case FeatureType.dataAnalysis:
        return {
          'chartTypes': ['bar', 'line', 'pie'],
          'maxDataPoints': 1000,
        };
      case FeatureType.planCreation:
        return {
          'timeframe': 'monthly',
          'detailLevel': 'medium',
        };
      case FeatureType.writingAssistance:
        return {
          'tone': 'professional',
          'length': 'medium',
          'language': 'ar',
        };
      case FeatureType.smartTranslation:
        return {
          'sourceLanguage': 'auto',
          'targetLanguage': 'ar',
          'preserveFormatting': true,
        };
      case FeatureType.imageAnalysis:
        return {
          'detailLevel': 'high',
          'includeText': true,
          'includeObjects': true,
        };
      case FeatureType.voiceChat:
        return {
          'voice': 'alloy',
          'speed': 1.0,
          'language': 'ar',
        };
      case FeatureType.smartBrowsing:
        return {
          'maxPages': 10,
          'includeImages': false,
          'summarize': true,
        };
      case FeatureType.codeGeneration:
        return {
          'language': 'dart',
          'style': 'clean',
          'includeComments': true,
        };
    }
  }
}

/// مقدمو الخدمة المدعومون لكل ميزة
class SupportedProviders {
  static List<String> getProvidersForFeature(FeatureType type) {
    switch (type) {
      case FeatureType.textSummarization:
        return ['openai', 'anthropic', 'google', 'openrouter'];
      case FeatureType.imageGeneration:
        return ['openai', 'stability', 'midjourney', 'replicate'];
      case FeatureType.dataAnalysis:
        return ['openai', 'anthropic', 'google'];
      case FeatureType.planCreation:
        return ['openai', 'anthropic', 'google', 'openrouter'];
      case FeatureType.writingAssistance:
        return ['openai', 'anthropic', 'google', 'openrouter'];
      case FeatureType.smartTranslation:
        return ['openai', 'google', 'deepl'];
      case FeatureType.imageAnalysis:
        return ['openai', 'google', 'anthropic'];
      case FeatureType.voiceChat:
        return ['openai', 'elevenlabs', 'azure'];
      case FeatureType.smartBrowsing:
        return ['openai', 'anthropic', 'google'];
      case FeatureType.codeGeneration:
        return ['openai', 'anthropic', 'github', 'openrouter'];
    }
  }

  static List<String> getModelsForProvider(String provider, FeatureType type) {
    switch (provider) {
      case 'openai':
        switch (type) {
          case FeatureType.imageGeneration:
            return ['dall-e-3', 'dall-e-2'];
          case FeatureType.voiceChat:
            return ['tts-1', 'tts-1-hd'];
          default:
            return ['gpt-4', 'gpt-4-turbo', 'gpt-3.5-turbo'];
        }
      case 'anthropic':
        return ['claude-3-opus', 'claude-3-sonnet', 'claude-3-haiku'];
      case 'google':
        return ['gemini-pro', 'gemini-pro-vision'];
      case 'stability':
        return ['stable-diffusion-xl', 'stable-diffusion-v1-6'];
      default:
        return [];
    }
  }
}
