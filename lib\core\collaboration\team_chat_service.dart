import 'package:flutter/foundation.dart';
import '../services/storage_service.dart';
import 'user_management_service.dart';

/// خدمة الدردشة الجماعية
class TeamChatService {
  static const String _channelsKey = 'team_chat_channels';
  static const String _messagesKey = 'team_chat_messages';
  static const String _threadsKey = 'team_chat_threads';

  static bool _isInitialized = false;
  static Map<String, ChatChannel> _channels = {};
  static Map<String, List<ChatMessage>> _channelMessages = {};
  static Map<String, List<ChatMessage>> _threadMessages = {};

  /// تهيئة خدمة الدردشة الجماعية
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadChannels();
      await _loadMessages();
      await _loadThreads();

      _isInitialized = true;
      debugPrint('💬 تم تهيئة خدمة الدردشة الجماعية');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة الدردشة الجماعية: $e');
    }
  }

  /// إنشاء قناة دردشة جديدة
  static Future<ChatChannel> createChannel({
    required String name,
    required String description,
    required String workspaceId,
    ChannelType type = ChannelType.public,
    List<String>? memberIds,
  }) async {
    final channel = ChatChannel(
      id: 'ch_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      description: description,
      workspaceId: workspaceId,
      type: type,
      createdBy: UserManagementService.currentUser?.id ?? '',
      memberIds: memberIds ?? [],
      isArchived: false,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // إضافة المنشئ كعضو
    if (UserManagementService.currentUser != null &&
        !channel.memberIds.contains(UserManagementService.currentUser!.id)) {
      channel.memberIds.add(UserManagementService.currentUser!.id);
    }

    _channels[channel.id] = channel;
    _channelMessages[channel.id] = [];
    await _saveChannels();

    debugPrint('✅ تم إنشاء قناة دردشة جديدة: $name');
    return channel;
  }

  /// إرسال رسالة في القناة
  static Future<ChatMessage> sendMessage({
    required String channelId,
    required String content,
    MessageType type = MessageType.text,
    String? replyToId,
    List<MessageAttachment>? attachments,
  }) async {
    final message = ChatMessage(
      id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
      channelId: channelId,
      senderId: UserManagementService.currentUser?.id ?? '',
      content: content,
      type: type,
      replyToId: replyToId,
      attachments: attachments ?? [],
      reactions: {},
      isEdited: false,
      isDeleted: false,
      sentAt: DateTime.now(),
    );

    // إضافة الرسالة للقناة
    if (_channelMessages[channelId] == null) {
      _channelMessages[channelId] = [];
    }
    _channelMessages[channelId]!.add(message);

    // تحديث آخر نشاط في القناة
    await _updateChannelLastActivity(channelId);

    await _saveMessages();

    debugPrint('📤 تم إرسال رسالة في القناة: $channelId');
    return message;
  }

  /// إرسال رسالة في خيط محادثة
  static Future<ChatMessage> sendThreadMessage({
    required String parentMessageId,
    required String content,
    MessageType type = MessageType.text,
    List<MessageAttachment>? attachments,
  }) async {
    final parentMessage = await getMessage(parentMessageId);
    if (parentMessage == null) {
      throw Exception('الرسالة الأصلية غير موجودة');
    }

    final message = ChatMessage(
      id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
      channelId: parentMessage.channelId,
      senderId: UserManagementService.currentUser?.id ?? '',
      content: content,
      type: type,
      parentMessageId: parentMessageId,
      attachments: attachments ?? [],
      reactions: {},
      isEdited: false,
      isDeleted: false,
      sentAt: DateTime.now(),
    );

    // إضافة الرسالة للخيط
    if (_threadMessages[parentMessageId] == null) {
      _threadMessages[parentMessageId] = [];
    }
    _threadMessages[parentMessageId]!.add(message);

    await _saveThreads();

    debugPrint('🧵 تم إرسال رسالة في الخيط: $parentMessageId');
    return message;
  }

  /// تحديث رسالة
  static Future<bool> editMessage(String messageId, String newContent) async {
    try {
      // البحث في رسائل القنوات
      for (final channelId in _channelMessages.keys) {
        final messages = _channelMessages[channelId]!;
        final messageIndex = messages.indexWhere((msg) => msg.id == messageId);

        if (messageIndex != -1) {
          final message = messages[messageIndex];

          // التحقق من الصلاحية
          if (message.senderId != UserManagementService.currentUser?.id) {
            return false;
          }

          final updatedMessage = ChatMessage(
            id: message.id,
            channelId: message.channelId,
            senderId: message.senderId,
            content: newContent,
            type: message.type,
            replyToId: message.replyToId,
            parentMessageId: message.parentMessageId,
            attachments: message.attachments,
            reactions: message.reactions,
            isEdited: true,
            isDeleted: message.isDeleted,
            sentAt: message.sentAt,
            editedAt: DateTime.now(),
          );

          messages[messageIndex] = updatedMessage;
          await _saveMessages();

          debugPrint('✏️ تم تحديث الرسالة: $messageId');
          return true;
        }
      }

      // البحث في رسائل الخيوط
      for (final threadId in _threadMessages.keys) {
        final messages = _threadMessages[threadId]!;
        final messageIndex = messages.indexWhere((msg) => msg.id == messageId);

        if (messageIndex != -1) {
          final message = messages[messageIndex];

          if (message.senderId != UserManagementService.currentUser?.id) {
            return false;
          }

          final updatedMessage = ChatMessage(
            id: message.id,
            channelId: message.channelId,
            senderId: message.senderId,
            content: newContent,
            type: message.type,
            replyToId: message.replyToId,
            parentMessageId: message.parentMessageId,
            attachments: message.attachments,
            reactions: message.reactions,
            isEdited: true,
            isDeleted: message.isDeleted,
            sentAt: message.sentAt,
            editedAt: DateTime.now(),
          );

          messages[messageIndex] = updatedMessage;
          await _saveThreads();

          debugPrint('✏️ تم تحديث رسالة الخيط: $messageId');
          return true;
        }
      }

      return false;
    } catch (e) {
      debugPrint('❌ فشل في تحديث الرسالة: $e');
      return false;
    }
  }

  /// حذف رسالة
  static Future<bool> deleteMessage(String messageId) async {
    try {
      // البحث في رسائل القنوات
      for (final channelId in _channelMessages.keys) {
        final messages = _channelMessages[channelId]!;
        final messageIndex = messages.indexWhere((msg) => msg.id == messageId);

        if (messageIndex != -1) {
          final message = messages[messageIndex];

          // التحقق من الصلاحية
          if (message.senderId != UserManagementService.currentUser?.id) {
            return false;
          }

          final updatedMessage = ChatMessage(
            id: message.id,
            channelId: message.channelId,
            senderId: message.senderId,
            content: '[تم حذف هذه الرسالة]',
            type: message.type,
            replyToId: message.replyToId,
            parentMessageId: message.parentMessageId,
            attachments: [],
            reactions: message.reactions,
            isEdited: message.isEdited,
            isDeleted: true,
            sentAt: message.sentAt,
            editedAt: message.editedAt,
            deletedAt: DateTime.now(),
          );

          messages[messageIndex] = updatedMessage;
          await _saveMessages();

          debugPrint('🗑️ تم حذف الرسالة: $messageId');
          return true;
        }
      }

      // البحث في رسائل الخيوط
      for (final threadId in _threadMessages.keys) {
        final messages = _threadMessages[threadId]!;
        final messageIndex = messages.indexWhere((msg) => msg.id == messageId);

        if (messageIndex != -1) {
          final message = messages[messageIndex];

          if (message.senderId != UserManagementService.currentUser?.id) {
            return false;
          }

          final updatedMessage = ChatMessage(
            id: message.id,
            channelId: message.channelId,
            senderId: message.senderId,
            content: '[تم حذف هذه الرسالة]',
            type: message.type,
            replyToId: message.replyToId,
            parentMessageId: message.parentMessageId,
            attachments: [],
            reactions: message.reactions,
            isEdited: message.isEdited,
            isDeleted: true,
            sentAt: message.sentAt,
            editedAt: message.editedAt,
            deletedAt: DateTime.now(),
          );

          messages[messageIndex] = updatedMessage;
          await _saveThreads();

          debugPrint('🗑️ تم حذف رسالة الخيط: $messageId');
          return true;
        }
      }

      return false;
    } catch (e) {
      debugPrint('❌ فشل في حذف الرسالة: $e');
      return false;
    }
  }

  /// إضافة تفاعل على رسالة
  static Future<bool> addReaction(String messageId, String emoji) async {
    try {
      final message = await getMessage(messageId);
      if (message == null) return false;

      final userId = UserManagementService.currentUser?.id ?? '';

      // إضافة أو إزالة التفاعل
      if (message.reactions[emoji] == null) {
        message.reactions[emoji] = [];
      }

      if (message.reactions[emoji]!.contains(userId)) {
        message.reactions[emoji]!.remove(userId);
        if (message.reactions[emoji]!.isEmpty) {
          message.reactions.remove(emoji);
        }
      } else {
        message.reactions[emoji]!.add(userId);
      }

      await _saveMessages();
      await _saveThreads();

      debugPrint('😊 تم تحديث التفاعل على الرسالة: $messageId');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في إضافة التفاعل: $e');
      return false;
    }
  }

  /// إضافة عضو للقناة
  static Future<bool> addChannelMember(String channelId, String userId) async {
    try {
      final channel = _channels[channelId];
      if (channel == null) return false;

      if (!channel.memberIds.contains(userId)) {
        channel.memberIds.add(userId);

        final updatedChannel = ChatChannel(
          id: channel.id,
          name: channel.name,
          description: channel.description,
          workspaceId: channel.workspaceId,
          type: channel.type,
          createdBy: channel.createdBy,
          memberIds: channel.memberIds,
          isArchived: channel.isArchived,
          createdAt: channel.createdAt,
          updatedAt: DateTime.now(),
        );

        _channels[channelId] = updatedChannel;
        await _saveChannels();

        debugPrint('✅ تم إضافة عضو للقناة: $userId');
        return true;
      }
      return true;
    } catch (e) {
      debugPrint('❌ فشل في إضافة عضو للقناة: $e');
      return false;
    }
  }

  /// البحث في الرسائل
  static List<ChatMessage> searchMessages(String query, {String? channelId}) {
    final results = <ChatMessage>[];

    // البحث في رسائل القنوات
    for (final cId in _channelMessages.keys) {
      if (channelId != null && cId != channelId) continue;

      final messages = _channelMessages[cId]!;
      for (final message in messages) {
        if (message.content.toLowerCase().contains(query.toLowerCase()) &&
            !message.isDeleted) {
          results.add(message);
        }
      }
    }

    // ترتيب النتائج حسب التاريخ
    results.sort((a, b) => b.sentAt.compareTo(a.sentAt));

    return results;
  }

  /// الحصول على رسائل القناة
  static List<ChatMessage> getChannelMessages(String channelId, {int? limit}) {
    final messages = _channelMessages[channelId] ?? [];

    // ترتيب حسب التاريخ (الأحدث أولاً)
    messages.sort((a, b) => b.sentAt.compareTo(a.sentAt));

    if (limit != null && limit > 0) {
      return messages.take(limit).toList();
    }

    return messages;
  }

  /// الحصول على رسائل الخيط
  static List<ChatMessage> getThreadMessages(String parentMessageId) {
    final messages = _threadMessages[parentMessageId] ?? [];

    // ترتيب حسب التاريخ (الأقدم أولاً للخيوط)
    messages.sort((a, b) => a.sentAt.compareTo(b.sentAt));

    return messages;
  }

  /// الحصول على رسالة محددة
  static Future<ChatMessage?> getMessage(String messageId) async {
    // البحث في رسائل القنوات
    for (final messages in _channelMessages.values) {
      final message = messages.firstWhere(
        (msg) => msg.id == messageId,
        orElse: () => null as ChatMessage,
      );
      if (message != null) return message;
    }

    // البحث في رسائل الخيوط
    for (final messages in _threadMessages.values) {
      final message = messages.firstWhere(
        (msg) => msg.id == messageId,
        orElse: () => null as ChatMessage,
      );
      if (message != null) return message;
    }

    return null;
  }

  /// تحديث آخر نشاط في القناة
  static Future<void> _updateChannelLastActivity(String channelId) async {
    final channel = _channels[channelId];
    if (channel == null) return;

    final updatedChannel = ChatChannel(
      id: channel.id,
      name: channel.name,
      description: channel.description,
      workspaceId: channel.workspaceId,
      type: channel.type,
      createdBy: channel.createdBy,
      memberIds: channel.memberIds,
      isArchived: channel.isArchived,
      createdAt: channel.createdAt,
      updatedAt: DateTime.now(),
    );

    _channels[channelId] = updatedChannel;
    await _saveChannels();
  }

  // Private methods for data persistence

  static Future<void> _saveChannels() async {
    final data = _channels.map((key, value) => MapEntry(key, value.toMap()));
    await StorageService.saveData(_channelsKey, data);
  }

  static Future<void> _loadChannels() async {
    try {
      final data = StorageService.getData(_channelsKey);
      if (data != null && data is Map) {
        _channels = data.map((key, value) => MapEntry(key, ChatChannel.fromMap(value)));
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل القنوات: $e');
    }
  }

  static Future<void> _saveMessages() async {
    final data = <String, dynamic>{};
    for (final entry in _channelMessages.entries) {
      data[entry.key] = entry.value.map((msg) => msg.toMap()).toList();
    }
    await StorageService.saveData(_messagesKey, data);
  }

  static Future<void> _loadMessages() async {
    try {
      final data = StorageService.getData(_messagesKey);
      if (data != null && data is Map) {
        _channelMessages.clear();
        for (final entry in data.entries) {
          if (entry.value is List) {
            _channelMessages[entry.key] = (entry.value as List)
                .map((item) => ChatMessage.fromMap(item))
                .toList();
          }
        }
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل الرسائل: $e');
    }
  }

  static Future<void> _saveThreads() async {
    final data = <String, dynamic>{};
    for (final entry in _threadMessages.entries) {
      data[entry.key] = entry.value.map((msg) => msg.toMap()).toList();
    }
    await StorageService.saveData(_threadsKey, data);
  }

  static Future<void> _loadThreads() async {
    try {
      final data = StorageService.getData(_threadsKey);
      if (data != null && data is Map) {
        _threadMessages.clear();
        for (final entry in data.entries) {
          if (entry.value is List) {
            _threadMessages[entry.key] = (entry.value as List)
                .map((item) => ChatMessage.fromMap(item))
                .toList();
          }
        }
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل الخيوط: $e');
    }
  }

  // Getters
  static bool get isInitialized => _isInitialized;
  static List<ChatChannel> get allChannels => _channels.values.toList();
  static int get channelsCount => _channels.length;
  static int get totalMessagesCount => _channelMessages.values
      .map((messages) => messages.length)
      .fold(0, (sum, count) => sum + count);
}

/// قناة الدردشة
class ChatChannel {
  final String id;
  final String name;
  final String description;
  final String workspaceId;
  final ChannelType type;
  final String createdBy;
  final List<String> memberIds;
  final bool isArchived;
  final DateTime createdAt;
  final DateTime updatedAt;

  ChatChannel({
    required this.id,
    required this.name,
    required this.description,
    required this.workspaceId,
    required this.type,
    required this.createdBy,
    required this.memberIds,
    required this.isArchived,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'workspaceId': workspaceId,
      'type': type.toString(),
      'createdBy': createdBy,
      'memberIds': memberIds,
      'isArchived': isArchived,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory ChatChannel.fromMap(Map<String, dynamic> map) {
    return ChatChannel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      workspaceId: map['workspaceId'] ?? '',
      type: ChannelType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => ChannelType.public,
      ),
      createdBy: map['createdBy'] ?? '',
      memberIds: List<String>.from(map['memberIds'] ?? []),
      isArchived: map['isArchived'] ?? false,
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }
}

/// رسالة الدردشة
class ChatMessage {
  final String id;
  final String channelId;
  final String senderId;
  final String content;
  final MessageType type;
  final String? replyToId;
  final String? parentMessageId;
  final List<MessageAttachment> attachments;
  final Map<String, List<String>> reactions;
  final bool isEdited;
  final bool isDeleted;
  final DateTime sentAt;
  final DateTime? editedAt;
  final DateTime? deletedAt;

  ChatMessage({
    required this.id,
    required this.channelId,
    required this.senderId,
    required this.content,
    required this.type,
    this.replyToId,
    this.parentMessageId,
    required this.attachments,
    required this.reactions,
    required this.isEdited,
    required this.isDeleted,
    required this.sentAt,
    this.editedAt,
    this.deletedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'channelId': channelId,
      'senderId': senderId,
      'content': content,
      'type': type.toString(),
      'replyToId': replyToId,
      'parentMessageId': parentMessageId,
      'attachments': attachments.map((att) => att.toMap()).toList(),
      'reactions': reactions,
      'isEdited': isEdited,
      'isDeleted': isDeleted,
      'sentAt': sentAt.toIso8601String(),
      'editedAt': editedAt?.toIso8601String(),
      'deletedAt': deletedAt?.toIso8601String(),
    };
  }

  factory ChatMessage.fromMap(Map<String, dynamic> map) {
    return ChatMessage(
      id: map['id'] ?? '',
      channelId: map['channelId'] ?? '',
      senderId: map['senderId'] ?? '',
      content: map['content'] ?? '',
      type: MessageType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => MessageType.text,
      ),
      replyToId: map['replyToId'],
      parentMessageId: map['parentMessageId'],
      attachments: (map['attachments'] as List? ?? [])
          .map((item) => MessageAttachment.fromMap(item))
          .toList(),
      reactions: Map<String, List<String>>.from(
        map['reactions']?.map((key, value) =>
            MapEntry(key, List<String>.from(value))) ?? {}),
      isEdited: map['isEdited'] ?? false,
      isDeleted: map['isDeleted'] ?? false,
      sentAt: DateTime.parse(map['sentAt']),
      editedAt: map['editedAt'] != null ? DateTime.parse(map['editedAt']) : null,
      deletedAt: map['deletedAt'] != null ? DateTime.parse(map['deletedAt']) : null,
    );
  }
}

/// مرفق الرسالة
class MessageAttachment {
  final String id;
  final String name;
  final String url;
  final AttachmentType type;
  final int size;

  MessageAttachment({
    required this.id,
    required this.name,
    required this.url,
    required this.type,
    required this.size,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'url': url,
      'type': type.toString(),
      'size': size,
    };
  }

  factory MessageAttachment.fromMap(Map<String, dynamic> map) {
    return MessageAttachment(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      url: map['url'] ?? '',
      type: AttachmentType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => AttachmentType.file,
      ),
      size: map['size'] ?? 0,
    );
  }
}

/// نوع القناة
enum ChannelType {
  public,
  private,
  direct,
}

/// نوع الرسالة
enum MessageType {
  text,
  image,
  file,
  voice,
  video,
  system,
}

/// نوع المرفق
enum AttachmentType {
  image,
  video,
  audio,
  document,
  file,
}
