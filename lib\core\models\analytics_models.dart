/// نماذج البيانات للتحليلات والذكاء الاصطناعي

import 'package:equatable/equatable.dart';

/// نموذج بيانات التحليلات
class AnalyticsData extends Equatable {
  final String id;
  final String category;
  final String name;
  final Map<String, dynamic> data;
  final List<AnalyticsEvent> events;
  final AnalyticsMetrics metrics;
  final DateTime createdAt;
  final DateTime updatedAt;

  const AnalyticsData({
    required this.id,
    required this.category,
    required this.name,
    required this.data,
    required this.events,
    required this.metrics,
    required this.createdAt,
    required this.updatedAt,
  });

  factory AnalyticsData.fromJson(Map<String, dynamic> json) {
    return AnalyticsData(
      id: json['id'] as String,
      category: json['category'] as String,
      name: json['name'] as String,
      data: Map<String, dynamic>.from(json['data'] as Map),
      events: (json['events'] as List? ?? [])
          .map((e) => AnalyticsEvent.fromJson(e as Map<String, dynamic>))
          .toList(),
      metrics: AnalyticsMetrics.fromJson(json['metrics'] as Map<String, dynamic>? ?? {}),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'category': category,
      'name': name,
      'data': data,
      'events': events.map((e) => e.toJson()).toList(),
      'metrics': metrics.toJson(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  Map<String, dynamic> toMap() => toJson();

  factory AnalyticsData.fromMap(Map<String, dynamic> map) => AnalyticsData.fromJson(map);

  AnalyticsData copyWith({
    String? id,
    String? category,
    String? name,
    Map<String, dynamic>? data,
    List<AnalyticsEvent>? events,
    AnalyticsMetrics? metrics,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return AnalyticsData(
      id: id ?? this.id,
      category: category ?? this.category,
      name: name ?? this.name,
      data: data ?? this.data,
      events: events ?? this.events,
      metrics: metrics ?? this.metrics,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [id, category, name, data, events, metrics, createdAt, updatedAt];
}

/// أنواع الرؤى
enum InsightType {
  trend,
  pattern,
  anomaly,
  prediction,
  recommendation,
  warning,
  opportunity,
  usage,
  performance,
  behavior,
}

/// تأثير الرؤية
enum InsightImpact {
  low,
  medium,
  high,
  critical,
}

/// نموذج الرؤى الذكية
class AIInsight extends Equatable {
  final String id;
  final String title;
  final String description;
  final InsightType type;
  final InsightImpact impact;
  final double confidence;
  final Map<String, dynamic> metadata;
  final DateTime createdAt;

  const AIInsight({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.impact,
    required this.confidence,
    required this.metadata,
    required this.createdAt,
  });

  factory AIInsight.fromJson(Map<String, dynamic> json) {
    return AIInsight(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: InsightType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => InsightType.trend,
      ),
      impact: InsightImpact.values.firstWhere(
        (e) => e.name == json['impact'],
        orElse: () => InsightImpact.medium,
      ),
      confidence: (json['confidence'] as num).toDouble(),
      metadata: Map<String, dynamic>.from(json['metadata'] as Map),
      createdAt: DateTime.parse(json['created_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'impact': impact.name,
      'confidence': confidence,
      'metadata': metadata,
      'created_at': createdAt.toIso8601String(),
    };
  }

  AIInsight copyWith({
    String? id,
    String? title,
    String? description,
    InsightType? type,
    InsightImpact? impact,
    double? confidence,
    Map<String, dynamic>? metadata,
    DateTime? createdAt,
  }) {
    return AIInsight(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      impact: impact ?? this.impact,
      confidence: confidence ?? this.confidence,
      metadata: metadata ?? this.metadata,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  Map<String, dynamic> toMap() => toJson();

  factory AIInsight.fromMap(Map<String, dynamic> map) => AIInsight.fromJson(map);

  @override
  List<Object?> get props => [id, title, description, type, impact, confidence, metadata, createdAt];
}

/// أنواع التنبؤات
enum PredictionType {
  usage,
  performance,
  trend,
  behavior,
  demand,
  risk,
}

/// إطار زمني للتنبؤ
enum PredictionTimeframe {
  shortTerm, // أسبوع
  mediumTerm, // شهر
  longTerm, // 3 أشهر
  week,
  month,
}

/// نموذج التنبؤات الذكية
class AIPrediction extends Equatable {
  final String id;
  final String title;
  final String description;
  final PredictionType type;
  final PredictionTimeframe timeframe;
  final double confidence;
  final Map<String, dynamic> data;
  final DateTime createdAt;
  final DateTime validUntil;

  const AIPrediction({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.timeframe,
    required this.confidence,
    required this.data,
    required this.createdAt,
    required this.validUntil,
  });

  factory AIPrediction.fromJson(Map<String, dynamic> json) {
    return AIPrediction(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: PredictionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => PredictionType.usage,
      ),
      timeframe: PredictionTimeframe.values.firstWhere(
        (e) => e.name == json['timeframe'],
        orElse: () => PredictionTimeframe.mediumTerm,
      ),
      confidence: (json['confidence'] as num).toDouble(),
      data: Map<String, dynamic>.from(json['data'] as Map),
      createdAt: DateTime.parse(json['created_at'] as String),
      validUntil: DateTime.parse(json['valid_until'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'timeframe': timeframe.name,
      'confidence': confidence,
      'data': data,
      'created_at': createdAt.toIso8601String(),
      'valid_until': validUntil.toIso8601String(),
    };
  }

  AIPrediction copyWith({
    String? id,
    String? title,
    String? description,
    PredictionType? type,
    PredictionTimeframe? timeframe,
    double? confidence,
    Map<String, dynamic>? data,
    DateTime? createdAt,
    DateTime? validUntil,
  }) {
    return AIPrediction(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      timeframe: timeframe ?? this.timeframe,
      confidence: confidence ?? this.confidence,
      data: data ?? this.data,
      createdAt: createdAt ?? this.createdAt,
      validUntil: validUntil ?? this.validUntil,
    );
  }

  Map<String, dynamic> toMap() => toJson();

  factory AIPrediction.fromMap(Map<String, dynamic> map) => AIPrediction.fromJson(map);

  @override
  List<Object?> get props => [id, title, description, type, timeframe, confidence, data, createdAt, validUntil];
}

/// نموذج ملخص التحليلات
class AnalyticsSummary extends Equatable {
  final int totalEvents;
  final Map<String, int> eventsByCategory;
  final DateTime periodStart;
  final DateTime periodEnd;
  final Map<String, dynamic> metrics;
  final int totalInsights;
  final int totalPredictions;

  const AnalyticsSummary({
    required this.totalEvents,
    required this.eventsByCategory,
    required this.periodStart,
    required this.periodEnd,
    required this.metrics,
    this.totalInsights = 0,
    this.totalPredictions = 0,
  });

  factory AnalyticsSummary.fromJson(Map<String, dynamic> json) {
    return AnalyticsSummary(
      totalEvents: json['total_events'] as int,
      eventsByCategory: Map<String, int>.from(json['events_by_category'] as Map),
      periodStart: DateTime.parse(json['period_start'] as String),
      periodEnd: DateTime.parse(json['period_end'] as String),
      metrics: Map<String, dynamic>.from(json['metrics'] as Map),
      totalInsights: json['total_insights'] as int? ?? 0,
      totalPredictions: json['total_predictions'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'total_events': totalEvents,
      'events_by_category': eventsByCategory,
      'period_start': periodStart.toIso8601String(),
      'period_end': periodEnd.toIso8601String(),
      'metrics': metrics,
      'total_insights': totalInsights,
      'total_predictions': totalPredictions,
    };
  }

  AnalyticsSummary copyWith({
    int? totalEvents,
    Map<String, int>? eventsByCategory,
    DateTime? periodStart,
    DateTime? periodEnd,
    Map<String, dynamic>? metrics,
    int? totalInsights,
    int? totalPredictions,
  }) {
    return AnalyticsSummary(
      totalEvents: totalEvents ?? this.totalEvents,
      eventsByCategory: eventsByCategory ?? this.eventsByCategory,
      periodStart: periodStart ?? this.periodStart,
      periodEnd: periodEnd ?? this.periodEnd,
      metrics: metrics ?? this.metrics,
      totalInsights: totalInsights ?? this.totalInsights,
      totalPredictions: totalPredictions ?? this.totalPredictions,
    );
  }

  @override
  List<Object?> get props => [totalEvents, eventsByCategory, periodStart, periodEnd, metrics, totalInsights, totalPredictions];
}

/// نموذج حدث التحليلات
class AnalyticsEvent extends Equatable {
  final String id;
  final String name;
  final String category;
  final Map<String, dynamic> properties;
  final String? userId;
  final DateTime timestamp;

  const AnalyticsEvent({
    required this.id,
    required this.name,
    required this.category,
    required this.properties,
    this.userId,
    required this.timestamp,
  });

  factory AnalyticsEvent.fromJson(Map<String, dynamic> json) {
    return AnalyticsEvent(
      id: json['id'] as String,
      name: json['name'] as String,
      category: json['category'] as String,
      properties: Map<String, dynamic>.from(json['properties'] as Map),
      userId: json['user_id'] as String?,
      timestamp: DateTime.parse(json['timestamp'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'category': category,
      'properties': properties,
      'user_id': userId,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [id, name, category, properties, userId, timestamp];
}

/// نموذج مقاييس التحليلات
class AnalyticsMetrics extends Equatable {
  final Map<String, num> values;
  final DateTime calculatedAt;

  const AnalyticsMetrics({
    required this.values,
    required this.calculatedAt,
  });

  factory AnalyticsMetrics.fromJson(Map<String, dynamic> json) {
    if (json.isEmpty) {
      return AnalyticsMetrics(
        values: {},
        calculatedAt: DateTime.now(),
      );
    }
    return AnalyticsMetrics(
      values: Map<String, num>.from(json['values'] as Map? ?? {}),
      calculatedAt: json['calculated_at'] != null
          ? DateTime.parse(json['calculated_at'] as String)
          : DateTime.now(),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'values': values,
      'calculated_at': calculatedAt.toIso8601String(),
    };
  }

  @override
  List<Object?> get props => [values, calculatedAt];
}
