import 'package:flutter/material.dart';
import 'dart:math' as math;
import '../utils/app_colors.dart';

/// خلفية متحركة مع تأثيرات زجاجية وجسيمات متحركة
class AnimatedBackground extends StatefulWidget {
  final Widget child;
  final bool enableParticles;
  final bool enableGradientAnimation;
  final int particleCount;

  const AnimatedBackground({
    Key? key,
    required this.child,
    this.enableParticles = true,
    this.enableGradientAnimation = true,
    this.particleCount = 30,
  }) : super(key: key);

  @override
  State<AnimatedBackground> createState() => _AnimatedBackgroundState();
}

class _AnimatedBackgroundState extends State<AnimatedBackground>
    with TickerProviderStateMixin {
  late AnimationController _gradientController;
  late AnimationController _particleController;
  late List<Particle> _particles;

  @override
  void initState() {
    super.initState();

    // تحكم في تحريك التدرج
    _gradientController = AnimationController(
      duration: const Duration(seconds: 8),
      vsync: this,
    )..repeat();

    // تحكم في تحريك الجسيمات
    _particleController = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();

    // إنشاء الجسيمات
    _particles = List.generate(
      widget.particleCount,
      (index) => Particle.random(),
    );
  }

  @override
  void dispose() {
    _gradientController.dispose();
    _particleController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // الخلفية المتدرجة المتحركة
        if (widget.enableGradientAnimation)
          AnimatedBuilder(
            animation: _gradientController,
            builder: (context, child) {
              return Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.background,
                      AppColors.darkGrey,
                      AppColors.darkPurple.withValues(alpha: 0.3),
                      AppColors.background,
                    ],
                    stops: [
                      0.0,
                      0.3 + (_gradientController.value * 0.2),
                      0.7 + (_gradientController.value * 0.2),
                      1.0,
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    transform: GradientRotation(
                      _gradientController.value * 2 * math.pi,
                    ),
                  ),
                ),
              );
            },
          )
        else
          Container(
            decoration: const BoxDecoration(
              gradient: AppColors.backgroundGradient,
            ),
          ),

        // الجسيمات المتحركة (معطلة مؤقتاً)
        // if (widget.enableParticles)
        //   AnimatedBuilder(
        //     animation: _particleController,
        //     builder: (context, child) {
        //       return CustomPaint(
        //         painter: ParticlePainter(
        //           particles: _particles,
        //           animationValue: _particleController.value,
        //         ),
        //         size: Size.infinite,
        //       );
        //     },
        //   ),

        // المحتوى الرئيسي
        widget.child,
      ],
    );
  }
}

/// فئة الجسيم المتحرك
class Particle {
  final double x;
  final double y;
  final double size;
  final Color color;
  final double speed;
  final double direction;
  final double opacity;

  Particle({
    required this.x,
    required this.y,
    required this.size,
    required this.color,
    required this.speed,
    required this.direction,
    required this.opacity,
  });

  factory Particle.random() {
    final random = math.Random();
    return Particle(
      x: random.nextDouble(),
      y: random.nextDouble(),
      size: 1 + random.nextDouble() * 3,
      color:
          [
            AppColors.primaryPurple,
            AppColors.accentBlue,
            AppColors.accentPink,
            AppColors.accentTeal,
          ][random.nextInt(4)],
      speed: 0.1 + random.nextDouble() * 0.3,
      direction: random.nextDouble() * 2 * math.pi,
      opacity: 0.1 + random.nextDouble() * 0.4,
    );
  }

  Particle move(double animationValue) {
    final newX = (x + math.cos(direction) * speed * animationValue) % 1.0;
    final newY = (y + math.sin(direction) * speed * animationValue) % 1.0;

    return Particle(
      x: newX,
      y: newY,
      size: size,
      color: color,
      speed: speed,
      direction: direction,
      opacity: opacity,
    );
  }
}

/// رسام الجسيمات
class ParticlePainter extends CustomPainter {
  final List<Particle> particles;
  final double animationValue;

  ParticlePainter({required this.particles, required this.animationValue});

  @override
  void paint(Canvas canvas, Size size) {
    for (final particle in particles) {
      final movedParticle = particle.move(animationValue);

      final paint =
          Paint()
            ..color = particle.color.withValues(alpha: particle.opacity)
            ..style = PaintingStyle.fill;

      final center = Offset(
        movedParticle.x * size.width,
        movedParticle.y * size.height,
      );

      // رسم الجسيم كدائرة مع تأثير توهج
      canvas.drawCircle(center, particle.size, paint);

      // تأثير التوهج
      final glowPaint =
          Paint()
            ..color = particle.color.withValues(alpha: particle.opacity * 0.3)
            ..style = PaintingStyle.fill
            ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);

      canvas.drawCircle(center, particle.size * 2, glowPaint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// تأثير الموجات المتحركة
class WaveBackground extends StatefulWidget {
  final Widget child;
  final List<Color> waveColors;
  final int waveCount;

  const WaveBackground({
    Key? key,
    required this.child,
    this.waveColors = const [
      AppColors.primaryPurple,
      AppColors.accentBlue,
      AppColors.accentPink,
    ],
    this.waveCount = 3,
  }) : super(key: key);

  @override
  State<WaveBackground> createState() => _WaveBackgroundState();
}

class _WaveBackgroundState extends State<WaveBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 6),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // الخلفية الأساسية
        Container(
          decoration: const BoxDecoration(
            gradient: AppColors.backgroundGradient,
          ),
        ),

        // الموجات المتحركة
        AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return CustomPaint(
              painter: WavePainter(
                animationValue: _controller.value,
                colors: widget.waveColors,
                waveCount: widget.waveCount,
              ),
              size: Size.infinite,
            );
          },
        ),

        // المحتوى
        widget.child,
      ],
    );
  }
}

/// رسام الموجات
class WavePainter extends CustomPainter {
  final double animationValue;
  final List<Color> colors;
  final int waveCount;

  WavePainter({
    required this.animationValue,
    required this.colors,
    required this.waveCount,
  });

  @override
  void paint(Canvas canvas, Size size) {
    for (int i = 0; i < waveCount; i++) {
      final paint =
          Paint()
            ..color = colors[i % colors.length].withValues(alpha: 0.1)
            ..style = PaintingStyle.fill;

      final path = Path();
      final waveHeight = size.height * 0.1;
      final waveLength = size.width / 2;
      final phase = animationValue * 2 * math.pi + (i * math.pi / 2);

      path.moveTo(0, size.height);

      for (double x = 0; x <= size.width; x += 5) {
        final y =
            size.height -
            waveHeight * math.sin((x / waveLength) * 2 * math.pi + phase);
        path.lineTo(x, y);
      }

      path.lineTo(size.width, size.height);
      path.close();

      canvas.drawPath(path, paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

/// تأثير الضوء المتحرك
class LightEffect extends StatefulWidget {
  final Widget child;
  final Color lightColor;
  final double intensity;

  const LightEffect({
    Key? key,
    required this.child,
    this.lightColor = AppColors.primaryPurple,
    this.intensity = 0.3,
  }) : super(key: key);

  @override
  State<LightEffect> createState() => _LightEffectState();
}

class _LightEffectState extends State<LightEffect>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 4),
      vsync: this,
    )..repeat(reverse: true);
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: RadialGradient(
              center: Alignment.center,
              radius: 1.0 + (_controller.value * 0.5),
              colors: [
                widget.lightColor.withValues(
                  alpha: widget.intensity * _controller.value,
                ),
                Colors.transparent,
              ],
            ),
          ),
          child: widget.child,
        );
      },
    );
  }
}
