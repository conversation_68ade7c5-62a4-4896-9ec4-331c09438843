import 'package:flutter/foundation.dart';
import '../storage/storage_service.dart';
import 'platform_models.dart';

/// خدمة التطبيق المصاحب
class CompanionAppService {
  static const String _companionDataKey = 'companion_app_data';
  static const String _pairedDevicesKey = 'paired_devices';
  static const String _messagesKey = 'companion_messages';
  
  static bool _isInitialized = false;
  static Map<String, CompanionApp> _companionApps = {};
  static Map<String, PairedDevice> _pairedDevices = {};
  static List<CompanionMessage> _messages = [];

  /// تهيئة خدمة التطبيق المصاحب
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadCompanionApps();
      await _loadPairedDevices();
      await _loadMessages();
      
      _isInitialized = true;
      debugPrint('📱 تم تهيئة خدمة التطبيق المصاحب');
      
      // بدء الاستماع للرسائل
      _startMessageListener();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة التطبيق المصاحب: $e');
    }
  }

  /// تسجيل تطبيق مصاحب جديد
  static Future<CompanionApp> registerCompanionApp({
    required String name,
    required CompanionAppType type,
    required String version,
    Map<String, dynamic>? capabilities,
  }) async {
    final app = CompanionApp(
      id: 'comp_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      type: type,
      version: version,
      capabilities: capabilities ?? {},
      isActive: true,
      registeredAt: DateTime.now(),
      lastActiveAt: DateTime.now(),
    );

    _companionApps[app.id] = app;
    await _saveCompanionApps();
    
    debugPrint('✅ تم تسجيل التطبيق المصاحب: $name');
    return app;
  }

  /// إقران جهاز جديد
  static Future<PairingResult> pairDevice({
    required String deviceName,
    required String deviceId,
    required PlatformType platformType,
    String? pairingCode,
  }) async {
    try {
      // التحقق من رمز الإقران
      if (pairingCode != null && !_validatePairingCode(pairingCode)) {
        return PairingResult(
          success: false,
          error: 'رمز الإقران غير صحيح',
        );
      }

      final device = PairedDevice(
        id: deviceId,
        name: deviceName,
        platformType: platformType,
        pairingCode: pairingCode,
        isPaired: true,
        pairedAt: DateTime.now(),
        lastSeen: DateTime.now(),
      );

      _pairedDevices[deviceId] = device;
      await _savePairedDevices();
      
      // إرسال رسالة ترحيب
      await sendMessage(
        targetDeviceId: deviceId,
        messageType: 'welcome',
        content: 'مرحباً! تم إقران الجهاز بنجاح',
      );
      
      debugPrint('🔗 تم إقران الجهاز: $deviceName');
      
      return PairingResult(
        success: true,
        device: device,
      );
      
    } catch (e) {
      debugPrint('❌ فشل في إقران الجهاز: $e');
      return PairingResult(
        success: false,
        error: 'فشل في إقران الجهاز: $e',
      );
    }
  }

  /// إلغاء إقران جهاز
  static Future<bool> unpairDevice(String deviceId) async {
    try {
      final device = _pairedDevices[deviceId];
      if (device == null) return false;

      // إرسال رسالة وداع
      await sendMessage(
        targetDeviceId: deviceId,
        messageType: 'goodbye',
        content: 'تم إلغاء الإقران',
      );

      _pairedDevices.remove(deviceId);
      await _savePairedDevices();
      
      debugPrint('🔓 تم إلغاء إقران الجهاز: ${device.name}');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في إلغاء الإقران: $e');
      return false;
    }
  }

  /// إرسال رسالة لجهاز مقترن
  static Future<bool> sendMessage({
    required String targetDeviceId,
    required String messageType,
    required String content,
    Map<String, dynamic>? data,
  }) async {
    try {
      final device = _pairedDevices[targetDeviceId];
      if (device == null || !device.isPaired) {
        debugPrint('❌ الجهاز غير مقترن: $targetDeviceId');
        return false;
      }

      final message = CompanionMessage(
        id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
        fromDeviceId: 'main_app',
        toDeviceId: targetDeviceId,
        messageType: messageType,
        content: content,
        data: data ?? {},
        sentAt: DateTime.now(),
        status: MessageStatus.sent,
      );

      _messages.add(message);
      await _saveMessages();
      
      // محاكاة إرسال الرسالة
      await Future.delayed(const Duration(milliseconds: 200));
      
      // تحديث حالة الرسالة
      message.status = MessageStatus.delivered;
      await _saveMessages();
      
      debugPrint('📤 تم إرسال رسالة إلى: ${device.name}');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في إرسال الرسالة: $e');
      return false;
    }
  }

  /// استقبال رسالة من جهاز مقترن
  static Future<void> receiveMessage(CompanionMessage message) async {
    try {
      _messages.add(message);
      await _saveMessages();
      
      // معالجة الرسالة حسب النوع
      await _processIncomingMessage(message);
      
      debugPrint('📥 تم استقبال رسالة من: ${message.fromDeviceId}');
    } catch (e) {
      debugPrint('❌ فشل في استقبال الرسالة: $e');
    }
  }

  /// إرسال إشعار لجميع الأجهزة المقترنة
  static Future<void> broadcastNotification({
    required String title,
    required String content,
    Map<String, dynamic>? data,
  }) async {
    for (final device in _pairedDevices.values) {
      if (device.isPaired) {
        await sendMessage(
          targetDeviceId: device.id,
          messageType: 'notification',
          content: content,
          data: {
            'title': title,
            ...?data,
          },
        );
      }
    }
    
    debugPrint('📢 تم إرسال إشعار لجميع الأجهزة المقترنة');
  }

  /// مزامنة البيانات مع جهاز محدد
  static Future<bool> syncWithDevice({
    required String deviceId,
    required String dataType,
    required Map<String, dynamic> data,
  }) async {
    try {
      return await sendMessage(
        targetDeviceId: deviceId,
        messageType: 'sync',
        content: 'مزامنة البيانات',
        data: {
          'dataType': dataType,
          'syncData': data,
          'timestamp': DateTime.now().toIso8601String(),
        },
      );
    } catch (e) {
      debugPrint('❌ فشل في مزامنة البيانات: $e');
      return false;
    }
  }

  /// طلب بيانات من جهاز محدد
  static Future<Map<String, dynamic>?> requestDataFromDevice({
    required String deviceId,
    required String dataType,
  }) async {
    try {
      final success = await sendMessage(
        targetDeviceId: deviceId,
        messageType: 'data_request',
        content: 'طلب بيانات',
        data: {
          'requestedDataType': dataType,
          'requestId': 'req_${DateTime.now().millisecondsSinceEpoch}',
        },
      );

      if (success) {
        // انتظار الاستجابة (محاكاة)
        await Future.delayed(const Duration(seconds: 2));
        
        // إرجاع بيانات تجريبية
        return {
          'dataType': dataType,
          'data': 'بيانات تجريبية من الجهاز',
          'timestamp': DateTime.now().toIso8601String(),
        };
      }
      
      return null;
    } catch (e) {
      debugPrint('❌ فشل في طلب البيانات: $e');
      return null;
    }
  }

  /// الحصول على حالة الأجهزة المقترنة
  static CompanionStatus getCompanionStatus() {
    final pairedDevices = _pairedDevices.values.where((d) => d.isPaired).toList();
    final activeApps = _companionApps.values.where((a) => a.isActive).toList();
    final recentMessages = _messages
        .where((m) => DateTime.now().difference(m.sentAt).inHours < 24)
        .toList();

    return CompanionStatus(
      pairedDevicesCount: pairedDevices.length,
      activeAppsCount: activeApps.length,
      recentMessagesCount: recentMessages.length,
      lastActivity: _messages.isNotEmpty ? _messages.last.sentAt : null,
    );
  }

  /// الحصول على رسائل جهاز محدد
  static List<CompanionMessage> getDeviceMessages(String deviceId) {
    return _messages
        .where((m) => m.fromDeviceId == deviceId || m.toDeviceId == deviceId)
        .toList()
      ..sort((a, b) => b.sentAt.compareTo(a.sentAt));
  }

  /// توليد رمز إقران جديد
  static String generatePairingCode() {
    final random = DateTime.now().millisecondsSinceEpoch;
    return (random % 999999).toString().padLeft(6, '0');
  }

  /// التحقق من صحة رمز الإقران
  static bool _validatePairingCode(String code) {
    // تحقق بسيط من طول الرمز
    return code.length == 6 && int.tryParse(code) != null;
  }

  /// معالجة الرسائل الواردة
  static Future<void> _processIncomingMessage(CompanionMessage message) async {
    switch (message.messageType) {
      case 'sync':
        await _handleSyncMessage(message);
        break;
      case 'data_request':
        await _handleDataRequest(message);
        break;
      case 'notification':
        await _handleNotification(message);
        break;
      case 'heartbeat':
        await _handleHeartbeat(message);
        break;
      default:
        debugPrint('📨 رسالة غير معروفة: ${message.messageType}');
    }
  }

  /// معالجة رسالة المزامنة
  static Future<void> _handleSyncMessage(CompanionMessage message) async {
    final syncData = message.data['syncData'];
    final dataType = message.data['dataType'];
    
    debugPrint('🔄 معالجة مزامنة البيانات: $dataType');
    
    // معالجة البيانات المزامنة هنا
    // ...
  }

  /// معالجة طلب البيانات
  static Future<void> _handleDataRequest(CompanionMessage message) async {
    final requestedDataType = message.data['requestedDataType'];
    final requestId = message.data['requestId'];
    
    debugPrint('📋 معالجة طلب البيانات: $requestedDataType');
    
    // إرسال البيانات المطلوبة
    await sendMessage(
      targetDeviceId: message.fromDeviceId,
      messageType: 'data_response',
      content: 'استجابة البيانات',
      data: {
        'requestId': requestId,
        'dataType': requestedDataType,
        'responseData': 'بيانات الاستجابة',
      },
    );
  }

  /// معالجة الإشعار
  static Future<void> _handleNotification(CompanionMessage message) async {
    final title = message.data['title'];
    debugPrint('🔔 تم استقبال إشعار: $title');
    
    // عرض الإشعار للمستخدم
    // ...
  }

  /// معالجة نبضة الحياة
  static Future<void> _handleHeartbeat(CompanionMessage message) async {
    final deviceId = message.fromDeviceId;
    final device = _pairedDevices[deviceId];
    
    if (device != null) {
      device.lastSeen = DateTime.now();
      await _savePairedDevices();
    }
  }

  /// بدء الاستماع للرسائل
  static void _startMessageListener() {
    // محاكاة استقبال رسائل دورية
    Stream.periodic(const Duration(minutes: 5)).listen((_) async {
      // محاكاة رسالة نبضة حياة من الأجهزة المقترنة
      for (final device in _pairedDevices.values) {
        if (device.isPaired) {
          final heartbeat = CompanionMessage(
            id: 'hb_${DateTime.now().millisecondsSinceEpoch}',
            fromDeviceId: device.id,
            toDeviceId: 'main_app',
            messageType: 'heartbeat',
            content: 'نبضة حياة',
            data: {'timestamp': DateTime.now().toIso8601String()},
            sentAt: DateTime.now(),
            status: MessageStatus.delivered,
          );
          
          await receiveMessage(heartbeat);
        }
      }
    });
  }

  // Data persistence methods
  static Future<void> _saveCompanionApps() async {
    final data = _companionApps.map((key, value) => MapEntry(key, value.toMap()));
    await StorageService.saveData(_companionDataKey, data);
  }

  static Future<void> _loadCompanionApps() async {
    try {
      final data = await StorageService.getData(_companionDataKey);
      if (data != null && data is Map) {
        _companionApps = data.map((key, value) => 
            MapEntry(key, CompanionApp.fromMap(value)));
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل التطبيقات المصاحبة: $e');
    }
  }

  static Future<void> _savePairedDevices() async {
    final data = _pairedDevices.map((key, value) => MapEntry(key, value.toMap()));
    await StorageService.saveData(_pairedDevicesKey, data);
  }

  static Future<void> _loadPairedDevices() async {
    try {
      final data = await StorageService.getData(_pairedDevicesKey);
      if (data != null && data is Map) {
        _pairedDevices = data.map((key, value) => 
            MapEntry(key, PairedDevice.fromMap(value)));
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل الأجهزة المقترنة: $e');
    }
  }

  static Future<void> _saveMessages() async {
    final data = _messages.map((message) => message.toMap()).toList();
    await StorageService.saveData(_messagesKey, data);
  }

  static Future<void> _loadMessages() async {
    try {
      final data = await StorageService.getData(_messagesKey);
      if (data != null && data is List) {
        _messages = data.map((item) => CompanionMessage.fromMap(item)).toList();
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل الرسائل: $e');
    }
  }

  // Getters
  static bool get isInitialized => _isInitialized;
  static List<CompanionApp> get companionApps => _companionApps.values.toList();
  static List<PairedDevice> get pairedDevices => _pairedDevices.values.toList();
  static List<CompanionMessage> get messages => List.unmodifiable(_messages);
  static int get pairedDevicesCount => _pairedDevices.values
      .where((device) => device.isPaired)
      .length;
}

/// تطبيق مصاحب
class CompanionApp {
  final String id;
  final String name;
  final CompanionAppType type;
  final String version;
  final Map<String, dynamic> capabilities;
  final bool isActive;
  final DateTime registeredAt;
  DateTime lastActiveAt;

  CompanionApp({
    required this.id,
    required this.name,
    required this.type,
    required this.version,
    required this.capabilities,
    required this.isActive,
    required this.registeredAt,
    required this.lastActiveAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'type': type.toString(),
      'version': version,
      'capabilities': capabilities,
      'isActive': isActive,
      'registeredAt': registeredAt.toIso8601String(),
      'lastActiveAt': lastActiveAt.toIso8601String(),
    };
  }

  factory CompanionApp.fromMap(Map<String, dynamic> map) {
    return CompanionApp(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      type: CompanionAppType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => CompanionAppType.mobile,
      ),
      version: map['version'] ?? '',
      capabilities: Map<String, dynamic>.from(map['capabilities'] ?? {}),
      isActive: map['isActive'] ?? false,
      registeredAt: DateTime.parse(map['registeredAt']),
      lastActiveAt: DateTime.parse(map['lastActiveAt']),
    );
  }
}

/// جهاز مقترن
class PairedDevice {
  final String id;
  final String name;
  final PlatformType platformType;
  final String? pairingCode;
  final bool isPaired;
  final DateTime pairedAt;
  DateTime lastSeen;

  PairedDevice({
    required this.id,
    required this.name,
    required this.platformType,
    this.pairingCode,
    required this.isPaired,
    required this.pairedAt,
    required this.lastSeen,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'platformType': platformType.toString(),
      'pairingCode': pairingCode,
      'isPaired': isPaired,
      'pairedAt': pairedAt.toIso8601String(),
      'lastSeen': lastSeen.toIso8601String(),
    };
  }

  factory PairedDevice.fromMap(Map<String, dynamic> map) {
    return PairedDevice(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      platformType: PlatformType.values.firstWhere(
        (e) => e.toString() == map['platformType'],
        orElse: () => PlatformType.mobile,
      ),
      pairingCode: map['pairingCode'],
      isPaired: map['isPaired'] ?? false,
      pairedAt: DateTime.parse(map['pairedAt']),
      lastSeen: DateTime.parse(map['lastSeen']),
    );
  }
}

/// رسالة التطبيق المصاحب
class CompanionMessage {
  final String id;
  final String fromDeviceId;
  final String toDeviceId;
  final String messageType;
  final String content;
  final Map<String, dynamic> data;
  final DateTime sentAt;
  MessageStatus status;

  CompanionMessage({
    required this.id,
    required this.fromDeviceId,
    required this.toDeviceId,
    required this.messageType,
    required this.content,
    required this.data,
    required this.sentAt,
    required this.status,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'fromDeviceId': fromDeviceId,
      'toDeviceId': toDeviceId,
      'messageType': messageType,
      'content': content,
      'data': data,
      'sentAt': sentAt.toIso8601String(),
      'status': status.toString(),
    };
  }

  factory CompanionMessage.fromMap(Map<String, dynamic> map) {
    return CompanionMessage(
      id: map['id'] ?? '',
      fromDeviceId: map['fromDeviceId'] ?? '',
      toDeviceId: map['toDeviceId'] ?? '',
      messageType: map['messageType'] ?? '',
      content: map['content'] ?? '',
      data: Map<String, dynamic>.from(map['data'] ?? {}),
      sentAt: DateTime.parse(map['sentAt']),
      status: MessageStatus.values.firstWhere(
        (e) => e.toString() == map['status'],
        orElse: () => MessageStatus.sent,
      ),
    );
  }
}

/// نتيجة الإقران
class PairingResult {
  final bool success;
  final PairedDevice? device;
  final String? error;

  PairingResult({
    required this.success,
    this.device,
    this.error,
  });
}

/// حالة التطبيق المصاحب
class CompanionStatus {
  final int pairedDevicesCount;
  final int activeAppsCount;
  final int recentMessagesCount;
  final DateTime? lastActivity;

  CompanionStatus({
    required this.pairedDevicesCount,
    required this.activeAppsCount,
    required this.recentMessagesCount,
    this.lastActivity,
  });
}

/// أنواع التطبيقات المصاحبة
enum CompanionAppType {
  mobile,
  desktop,
  web,
  wearable,
  tv,
}

/// حالة الرسالة
enum MessageStatus {
  sent,
  delivered,
  read,
  failed,
}
