@echo off
chcp 65001 >nul
title Flutter Emulator Setup

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                   🛠️ إعداد Flutter للمحاكي                  ║
echo ║                    Flutter Emulator Setup                    ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 فحص بيئة Flutter...
flutter doctor

echo.
echo 📱 عرض المحاكيات المتاحة...
flutter emulators

echo.
echo 🚀 هل تريد إنشاء محاكي جديد؟ (y/n)
set /p create_emulator=

if /i "%create_emulator%"=="y" (
    echo.
    echo 📱 إنشاء محاكي جديد...
    flutter emulators --create --name flutter_dev
    echo ✅ تم إنشاء المحاكي بنجاح!
)

echo.
echo 🔧 تحديث Flutter...
flutter upgrade

echo.
echo 📦 تنظيف cache...
flutter pub cache clean

echo.
echo ✅ تم الإعداد بنجاح!
echo.
echo 📋 للتشغيل السريع، استخدم:
echo    flutter run
echo.
echo 🎯 أو استخدم السكريبت: run_flutter_emulator.bat
echo.
pause
