# 🔧 دليل استكشاف أخطاء Flutter مع المحاكي

## 🚨 المشاكل الشائعة والحلول

### 1. المحاكي لا يظهر في flutter devices

**المشكلة:**
```
No devices found
```

**الحل:**
```bash
# تشغيل المحاكي يدوياً
emulator -avd <emulator_name>

# أو
flutter emulators --launch <emulator_name>

# التحقق من ADB
adb devices
adb kill-server
adb start-server
```

### 2. خطأ Android NDK

**المشكلة:**
```
Your project is configured with Android NDK 26.x.x, but plugin requires 27.x.x
```

**الحل:**
```kotlin
// في android/app/build.gradle.kts
android {
    ndkVersion = "27.0.12077973"
}
```

### 3. خطأ Core Library Desugaring

**المشكلة:**
```
Dependency requires core library desugaring to be enabled
```

**الحل:**
```kotlin
android {
    compileOptions {
        isCoreLibraryDesugaringEnabled = true
    }
}

dependencies {
    coreLibraryDesugaring("com.android.tools:desugar_jdk_libs:2.0.4")
}
```

### 4. خطأ Gradle Memory

**المشكلة:**
```
OutOfMemoryError: Java heap space
```

**الحل:**
```properties
# في android/gradle.properties
org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=1024m
```

### 5. خطأ مكتبة flutter_local_notifications

**المشكلة:**
```
error: reference to bigLargeIcon is ambiguous
```

**الحل:**
```yaml
# في pubspec.yaml - استخدم إصدار أحدث
flutter_local_notifications: ^17.2.3  # بدلاً من ^16.x.x
```

### 6. خطأ file_picker

**المشكلة:**
```
error: cannot find symbol class Registrar
```

**الحل:**
```yaml
# في pubspec.yaml - استخدم إصدار أحدث
file_picker: ^8.1.2  # بدلاً من ^6.x.x
```

### 7. المحاكي بطيء جداً

**الحل:**
- زيادة RAM المخصص للمحاكي (4GB+)
- تفعيل Hardware Graphics
- استخدام x86_64 بدلاً من ARM
- إغلاق التطبيقات الأخرى

### 8. خطأ في البناء (Build Failed)

**الحل السريع:**
```bash
flutter clean
flutter pub get
flutter pub deps
flutter run
```

### 9. خطأ Hot Reload لا يعمل

**الحل:**
```bash
# إعادة تشغيل كاملة
flutter run
# ثم اضغط R في Terminal
```

### 10. خطأ في الأذونات (Permissions)

**الحل:**
```bash
# قبول جميع تراخيص Android
flutter doctor --android-licenses
```

## 🛠️ أوامر التشخيص المفيدة

### فحص شامل للبيئة
```bash
flutter doctor -v
flutter config
flutter devices
adb devices
```

### تنظيف شامل
```bash
flutter clean
flutter pub cache clean
flutter pub get
```

### إعادة تعيين ADB
```bash
adb kill-server
adb start-server
adb devices
```

### فحص المحاكيات
```bash
flutter emulators
emulator -list-avds
```

## 📋 قائمة فحص سريعة

عند مواجهة أي مشكلة، جرب هذه الخطوات بالترتيب:

1. ✅ `flutter doctor` - تأكد من عدم وجود مشاكل
2. ✅ `flutter devices` - تأكد من ظهور المحاكي
3. ✅ `flutter clean` - تنظيف المشروع
4. ✅ `flutter pub get` - تحديث الاعتماديات
5. ✅ فحص `android/app/build.gradle.kts` - تأكد من الإعدادات الصحيحة
6. ✅ فحص `android/gradle.properties` - تأكد من إعدادات الذاكرة
7. ✅ `flutter run` - تشغيل التطبيق

## 🆘 إذا لم تنجح الحلول السابقة

### إعادة تعيين كاملة
```bash
# حذف build folder
rmdir /s build
rmdir /s .dart_tool

# إعادة تثبيت الاعتماديات
flutter pub cache clean
flutter clean
flutter pub get

# إعادة تشغيل المحاكي
adb kill-server
adb start-server
flutter emulators --launch <emulator_name>
flutter run
```

### إنشاء مشروع جديد للاختبار
```bash
flutter create test_app
cd test_app
flutter run
```

إذا عمل المشروع الجديد، فالمشكلة في إعدادات مشروعك الحالي.

## 📞 طلب المساعدة

عند طلب المساعدة، قدم هذه المعلومات:

```bash
flutter doctor -v
flutter --version
```

ونسخ كامل من رسالة الخطأ.

---

**نصيحة**: احفظ هذا الدليل واستخدمه كمرجع سريع عند مواجهة أي مشاكل! 🚀
