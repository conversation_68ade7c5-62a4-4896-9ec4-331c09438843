import 'analytics_models.dart';

/// لوحة معلومات تحليلية
class AnalyticsDashboard {
  final String id;
  final String name;
  final String description;
  final DashboardType type;
  final List<String> widgetIds;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  AnalyticsDashboard({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.widgetIds,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'type': type.toString(),
      'widgetIds': widgetIds,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory AnalyticsDashboard.fromMap(Map<String, dynamic> map) {
    return AnalyticsDashboard(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      type: DashboardType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => DashboardType.custom,
      ),
      widgetIds: List<String>.from(map['widgetIds'] ?? []),
      isActive: map['isActive'] ?? true,
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }
}

/// ويدجت لوحة المعلومات
class DashboardWidget {
  final String id;
  final String title;
  final WidgetType type;
  final WidgetSize size;
  final Map<String, dynamic> config;
  final Map<String, dynamic> data;
  final bool isVisible;
  final DateTime createdAt;
  final DateTime updatedAt;

  DashboardWidget({
    required this.id,
    required this.title,
    required this.type,
    required this.size,
    required this.config,
    required this.data,
    required this.isVisible,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'type': type.toString(),
      'size': size.toString(),
      'config': config,
      'data': data,
      'isVisible': isVisible,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory DashboardWidget.fromMap(Map<String, dynamic> map) {
    return DashboardWidget(
      id: map['id'] ?? '',
      title: map['title'] ?? '',
      type: WidgetType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => WidgetType.metric,
      ),
      size: WidgetSize.values.firstWhere(
        (e) => e.toString() == map['size'],
        orElse: () => WidgetSize.medium,
      ),
      config: Map<String, dynamic>.from(map['config'] ?? {}),
      data: Map<String, dynamic>.from(map['data'] ?? {}),
      isVisible: map['isVisible'] ?? true,
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }
}

/// تخطيط لوحة المعلومات
class DashboardLayout {
  final String id;
  final String dashboardId;
  final List<WidgetPosition> positions;
  final int columns;
  final double spacing;
  final DateTime createdAt;
  final DateTime updatedAt;

  DashboardLayout({
    required this.id,
    required this.dashboardId,
    required this.positions,
    required this.columns,
    required this.spacing,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'dashboardId': dashboardId,
      'positions': positions.map((pos) => pos.toMap()).toList(),
      'columns': columns,
      'spacing': spacing,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory DashboardLayout.fromMap(Map<String, dynamic> map) {
    return DashboardLayout(
      id: map['id'] ?? '',
      dashboardId: map['dashboardId'] ?? '',
      positions: (map['positions'] as List? ?? [])
          .map((item) => WidgetPosition.fromMap(item))
          .toList(),
      columns: map['columns'] ?? 2,
      spacing: map['spacing']?.toDouble() ?? 16.0,
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }
}

/// موضع الويدجت
class WidgetPosition {
  final String widgetId;
  final int row;
  final int column;
  final int rowSpan;
  final int columnSpan;

  WidgetPosition({
    required this.widgetId,
    required this.row,
    required this.column,
    required this.rowSpan,
    required this.columnSpan,
  });

  Map<String, dynamic> toMap() {
    return {
      'widgetId': widgetId,
      'row': row,
      'column': column,
      'rowSpan': rowSpan,
      'columnSpan': columnSpan,
    };
  }

  factory WidgetPosition.fromMap(Map<String, dynamic> map) {
    return WidgetPosition(
      widgetId: map['widgetId'] ?? '',
      row: map['row'] ?? 0,
      column: map['column'] ?? 0,
      rowSpan: map['rowSpan'] ?? 1,
      columnSpan: map['columnSpan'] ?? 1,
    );
  }
}

/// تقرير لوحة المعلومات
class DashboardReport {
  final String id;
  final String dashboardId;
  final String dashboardName;
  final int widgetCount;
  final AnalyticsSummary summary;
  final List<AIInsight> insights;
  final List<AIPrediction> predictions;
  final DateTime generatedAt;

  DashboardReport({
    required this.id,
    required this.dashboardId,
    required this.dashboardName,
    required this.widgetCount,
    required this.summary,
    required this.insights,
    required this.predictions,
    required this.generatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'dashboardId': dashboardId,
      'dashboardName': dashboardName,
      'widgetCount': widgetCount,
      'summary': {
        'totalEvents': summary.totalEvents,
        'totalCategories': summary.totalCategories,
        'totalInsights': summary.totalInsights,
        'totalPredictions': summary.totalPredictions,
      },
      'insights': insights.map((insight) => insight.toMap()).toList(),
      'predictions': predictions.map((prediction) => prediction.toMap()).toList(),
      'generatedAt': generatedAt.toIso8601String(),
    };
  }
}

/// أنواع لوحات المعلومات
enum DashboardType {
  overview,
  performance,
  usage,
  behavior,
  revenue,
  custom,
}

/// أنواع الويدجتات
enum WidgetType {
  chart,
  metric,
  table,
  insight,
  prediction,
  heatmap,
  gauge,
  progress,
}

/// أحجام الويدجتات
enum WidgetSize {
  small,   // 1x1
  medium,  // 2x1
  large,   // 2x2
  wide,    // 3x1
  tall,    // 1x2
  extra,   // 3x2
}
