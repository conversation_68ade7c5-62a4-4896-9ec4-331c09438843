import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:flutter/foundation.dart';
// import 'package:crypto/crypto.dart';
// import 'dart:convert';

/// مدير مفاتيح API الآمن
class ApiKeyManager {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(encryptedSharedPreferences: true),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // مفاتيح التخزين
  static const String _openaiKeyStorage = 'openai_api_key';
  static const String _geminiKeyStorage = 'gemini_api_key';
  static const String _deepseekKeyStorage = 'deepseek_api_key';
  static const String _openrouterKeyStorage = 'openrouter_api_key';

  /// تشفير مفتاح API (للاستخدام المستقبلي)
  // static String _encryptKey(String key) {
  //   final bytes = utf8.encode(key);
  //   final digest = sha256.convert(bytes);
  //   return digest.toString();
  // }

  /// حفظ مفتاح OpenAI
  static Future<void> setOpenAIKey(String key) async {
    await _storage.write(key: _openaiKeyStorage, value: key);
  }

  /// حفظ مفتاح Gemini
  static Future<void> setGeminiKey(String key) async {
    await _storage.write(key: _geminiKeyStorage, value: key);
  }

  /// حفظ مفتاح DeepSeek
  static Future<void> setDeepSeekKey(String key) async {
    await _storage.write(key: _deepseekKeyStorage, value: key);
  }

  /// حفظ مفتاح OpenRouter
  static Future<void> setOpenRouterKey(String key) async {
    await _storage.write(key: _openrouterKeyStorage, value: key);
  }

  /// الحصول على مفتاح OpenAI
  static Future<String?> getOpenAIKey() async {
    return await _storage.read(key: _openaiKeyStorage);
  }

  /// الحصول على مفتاح Gemini
  static Future<String?> getGeminiKey() async {
    return await _storage.read(key: _geminiKeyStorage);
  }

  /// الحصول على مفتاح DeepSeek
  static Future<String?> getDeepSeekKey() async {
    return await _storage.read(key: _deepseekKeyStorage);
  }

  /// الحصول على مفتاح OpenRouter
  static Future<String?> getOpenRouterKey() async {
    return await _storage.read(key: _openrouterKeyStorage);
  }

  /// حفظ مفتاح Hugging Face
  static Future<void> setHuggingFaceKey(String key) async {
    await _storage.write(key: 'huggingface_api_key', value: key);
  }

  /// الحصول على مفتاح Hugging Face
  static Future<String?> getHuggingFaceKey() async {
    return await _storage.read(key: 'huggingface_api_key');
  }

  /// التحقق من صحة مفتاح API
  static bool isValidApiKey(String key) {
    if (key.isEmpty) return false;

    // التحقق من مفاتيح OpenAI و OpenRouter
    if (key.startsWith('sk-')) {
      return key.length >= 40;
    }

    // التحقق من مفاتيح Gemini
    if (key.startsWith('AIza')) {
      return key.length >= 35;
    }

    // التحقق من مفاتيح DeepSeek
    if (key.contains('deepseek') || key.length >= 30) {
      return true;
    }

    return false;
  }

  /// الحصول على نوع المفتاح
  static String getKeyType(String key) {
    if (key.startsWith('sk-')) return 'OpenAI';
    if (key.startsWith('AIza')) return 'Gemini';
    return 'DeepSeek';
  }

  /// حذف جميع المفاتيح
  static Future<void> clearAllKeys() async {
    await _storage.deleteAll();
  }

  /// التحقق من وجود أي مفتاح
  static Future<bool> hasAnyKey() async {
    final openai = await getOpenAIKey();
    final gemini = await getGeminiKey();
    final deepseek = await getDeepSeekKey();

    return (openai != null && openai.isNotEmpty) ||
        (gemini != null && gemini.isNotEmpty) ||
        (deepseek != null && deepseek.isNotEmpty);
  }

  /// إعداد المفاتيح الافتراضية (للتطوير فقط)
  static Future<void> setupDefaultKeys() async {
    // مفتاح Hugging Face مجاني 100% (للصور والنصوص)
    const huggingFaceKey = '*************************************';
    await setHuggingFaceKey(huggingFaceKey);

    // مفتاح OpenRouter للدردشة والنصوص (مجاني محدود)
    const openRouterKey =
        '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';
    await setOpenRouterKey(openRouterKey);

    // مفتاح OpenAI لإنشاء الصور (معطل مؤقتاً - نفد الرصيد)
    // const openAIKey = 'sk-proj-...';
    // await setOpenAIKey(openAIKey);

    debugPrint('✅ تم حفظ مفاتيح API بنجاح');
    debugPrint('✅ OpenRouter للدردشة - OpenAI لإنشاء الصور');
  }

  /// الحصول على المفتاح المناسب للنموذج
  static Future<String?> getKeyForModel(String model) async {
    if (model.toLowerCase().contains('gpt') ||
        model.toLowerCase().contains('openai')) {
      return await getOpenAIKey();
    } else if (model.toLowerCase().contains('gemini')) {
      return await getGeminiKey();
    } else if (model.toLowerCase().contains('deepseek')) {
      return await getDeepSeekKey();
    }

    // افتراضي: OpenAI
    return await getOpenAIKey();
  }
}
