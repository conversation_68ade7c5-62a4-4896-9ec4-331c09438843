import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/app_colors.dart';
import '../widgets/modern_ui_components.dart';
import '../core/testing/test_suite_manager.dart';
import '../core/documentation/documentation_generator.dart';

/// شاشة الاختبارات والتوثيق
class TestingDocumentationScreen extends ConsumerStatefulWidget {
  const TestingDocumentationScreen({super.key});

  @override
  ConsumerState<TestingDocumentationScreen> createState() => _TestingDocumentationScreenState();
}

class _TestingDocumentationScreenState extends ConsumerState<TestingDocumentationScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  QualityReport? _qualityReport;
  DocumentationReport? _documentationReport;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeServices();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    setState(() => _isLoading = true);
    try {
      await TestSuiteManager.initialize();
      await DocumentationGenerator.initialize();
    } catch (e) {
      debugPrint('خطأ في تهيئة الاختبارات والتوثيق: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.darkGrey,
        title: Text(
          'الاختبارات والتوثيق',
          style: TextStyle(color: AppColors.white),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh, color: AppColors.electricBlue),
            onPressed: _refreshAll,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primaryPurple,
          unselectedLabelColor: AppColors.lightGrey,
          indicatorColor: AppColors.primaryPurple,
          tabs: const [
            Tab(text: 'الاختبارات', icon: Icon(Icons.bug_report)),
            Tab(text: 'التوثيق', icon: Icon(Icons.description)),
            Tab(text: 'تقرير الجودة', icon: Icon(Icons.assessment)),
          ],
        ),
      ),
      body: _isLoading
          ? Center(child: ModernUIComponents.modernLoadingIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildTestingTab(),
                _buildDocumentationTab(),
                _buildQualityReportTab(),
              ],
            ),
    );
  }

  Widget _buildTestingTab() {
    final testSuites = TestSuiteManager.testSuites;
    final testResults = TestSuiteManager.testResults.take(5).toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTestActionsCard(),
          const SizedBox(height: 24),
          _buildTestSuitesCard(testSuites),
          const SizedBox(height: 24),
          _buildRecentTestsCard(testResults),
        ],
      ),
    );
  }

  Widget _buildTestActionsCard() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إجراءات الاختبار',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ModernUIComponents.modernButton(
                  text: 'تشغيل جميع الاختبارات',
                  onPressed: _runAllTests,
                  icon: Icons.play_arrow,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ModernUIComponents.modernButton(
                  text: 'إنشاء تقرير جودة',
                  onPressed: _generateQualityReport,
                  icon: Icons.assessment,
                  gradient: LinearGradient(
                    colors: [AppColors.electricBlue, AppColors.lightPurple],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTestSuitesCard(Map<String, TestSuite> testSuites) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مجموعات الاختبار',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...testSuites.values.map((suite) => _buildTestSuiteItem(suite)),
        ],
      ),
    );
  }

  Widget _buildTestSuiteItem(TestSuite suite) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getTestSuiteColor(suite.id).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              _getTestSuiteIcon(suite.id),
              color: _getTestSuiteColor(suite.id),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  suite.name,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${suite.tests.length} اختبار',
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          ModernUIComponents.modernButton(
            text: 'تشغيل',
            onPressed: () => _runTestSuite(suite.id),
            icon: Icons.play_arrow,
            width: 80,
            height: 32,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentTestsCard(List<TestResult> results) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الاختبارات الأخيرة',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          if (results.isEmpty)
            Text(
              'لم يتم تشغيل أي اختبارات بعد',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14,
              ),
            )
          else
            ...results.map((result) => _buildTestResultItem(result)),
        ],
      ),
    );
  }

  Widget _buildTestResultItem(TestResult result) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            result.success ? Icons.check_circle : Icons.error,
            color: result.success ? AppColors.success : AppColors.error,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  result.testName,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${result.duration.inMilliseconds}ms',
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentationTab() {
    final sections = DocumentationGenerator.sections;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDocumentationActionsCard(),
          const SizedBox(height: 24),
          _buildDocumentationSectionsCard(sections),
          const SizedBox(height: 24),
          _buildDocumentationStatsCard(),
        ],
      ),
    );
  }

  Widget _buildDocumentationActionsCard() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إجراءات التوثيق',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ModernUIComponents.modernButton(
                  text: 'إنشاء التوثيق الكامل',
                  onPressed: _generateFullDocumentation,
                  icon: Icons.description,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ModernUIComponents.modernButton(
                  text: 'تصدير التوثيق',
                  onPressed: _exportDocumentation,
                  icon: Icons.download,
                  gradient: LinearGradient(
                    colors: [AppColors.glowPink, AppColors.primaryPurple],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentationSectionsCard(Map<String, DocumentationSection> sections) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'أقسام التوثيق',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...sections.values.map((section) => _buildDocumentationSectionItem(section)),
        ],
      ),
    );
  }

  Widget _buildDocumentationSectionItem(DocumentationSection section) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(
            _getDocumentationIcon(section.id),
            color: AppColors.primaryPurple,
            size: 20,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  section.title,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'آخر تحديث: ${_formatDate(section.lastUpdated)}',
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Icon(
            Icons.arrow_forward_ios,
            color: AppColors.textSecondary,
            size: 16,
          ),
        ],
      ),
    );
  }

  Widget _buildDocumentationStatsCard() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات التوثيق',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatItem(
                  'الأقسام',
                  '${DocumentationGenerator.sections.length}',
                  Icons.folder,
                  AppColors.electricBlue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatItem(
                  'الاكتمال',
                  '100%',
                  Icons.check_circle,
                  AppColors.success,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: AppColors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQualityReportTab() {
    if (_qualityReport == null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.assessment,
              size: 64,
              color: AppColors.textSecondary,
            ),
            const SizedBox(height: 16),
            Text(
              'لا يوجد تقرير جودة',
              style: TextStyle(
                color: AppColors.white,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'قم بإنشاء تقرير جودة لعرض النتائج',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 24),
            ModernUIComponents.modernButton(
              text: 'إنشاء تقرير الجودة',
              onPressed: _generateQualityReport,
              icon: Icons.assessment,
            ),
          ],
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildQualityScoreCard(_qualityReport!),
          const SizedBox(height: 24),
          _buildQualityMetricsCard(_qualityReport!),
          const SizedBox(height: 24),
          _buildRecommendationsCard(_qualityReport!),
        ],
      ),
    );
  }

  Widget _buildQualityScoreCard(QualityReport report) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: _getQualityGradient(report.overallScore),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    report.grade,
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    '${report.overallScore.toInt()}%',
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'تقييم الجودة الإجمالي',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'تم إنشاؤه: ${_formatDate(report.generatedAt)}',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQualityMetricsCard(QualityReport report) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'مقاييس الجودة',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildQualityMetricItem('جودة الكود', report.codeQuality, AppColors.primaryPurple),
          const SizedBox(height: 12),
          _buildQualityMetricItem('الأداء', report.performanceScore, AppColors.electricBlue),
          const SizedBox(height: 12),
          _buildQualityMetricItem('الأمان', report.securityScore, AppColors.success),
          const SizedBox(height: 12),
          _buildQualityMetricItem('سهولة الاستخدام', report.usabilityScore, AppColors.glowPink),
        ],
      ),
    );
  }

  Widget _buildQualityMetricItem(String title, double score, Color color) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: TextStyle(
                color: AppColors.white,
                fontSize: 14,
              ),
            ),
            Text(
              '${score.toInt()}%',
              style: TextStyle(
                color: color,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 4),
        LinearProgressIndicator(
          value: score / 100,
          backgroundColor: AppColors.darkGrey,
          valueColor: AlwaysStoppedAnimation<Color>(color),
        ),
      ],
    );
  }

  Widget _buildRecommendationsCard(QualityReport report) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'التوصيات',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...report.recommendations.map((recommendation) => Container(
            margin: const EdgeInsets.only(bottom: 8),
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.lightbulb,
                  color: AppColors.warning,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    recommendation,
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  // Helper methods
  Color _getTestSuiteColor(String suiteId) {
    switch (suiteId) {
      case 'unit':
        return AppColors.primaryPurple;
      case 'integration':
        return AppColors.electricBlue;
      case 'ui':
        return AppColors.glowPink;
      case 'performance':
        return AppColors.warning;
      case 'security':
        return AppColors.success;
      default:
        return AppColors.lightGrey;
    }
  }

  IconData _getTestSuiteIcon(String suiteId) {
    switch (suiteId) {
      case 'unit':
        return Icons.code;
      case 'integration':
        return Icons.link;
      case 'ui':
        return Icons.design_services;
      case 'performance':
        return Icons.speed;
      case 'security':
        return Icons.security;
      default:
        return Icons.bug_report;
    }
  }

  IconData _getDocumentationIcon(String sectionId) {
    switch (sectionId) {
      case 'project':
        return Icons.info;
      case 'features':
        return Icons.star;
      case 'architecture':
        return Icons.architecture;
      case 'user_guide':
        return Icons.person;
      case 'developer_guide':
        return Icons.code;
      default:
        return Icons.description;
    }
  }

  Gradient _getQualityGradient(double score) {
    if (score >= 90) {
      return LinearGradient(colors: [AppColors.success, AppColors.electricBlue]);
    } else if (score >= 70) {
      return LinearGradient(colors: [AppColors.warning, AppColors.primaryPurple]);
    } else {
      return LinearGradient(colors: [AppColors.error, AppColors.glowPink]);
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  // Action methods
  Future<void> _refreshAll() async {
    setState(() => _isLoading = true);
    try {
      await _initializeServices();
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _runAllTests() async {
    setState(() => _isLoading = true);
    try {
      await TestSuiteManager.runAllTests();
      setState(() {});
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تشغيل جميع الاختبارات بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _runTestSuite(String suiteId) async {
    try {
      await TestSuiteManager.runTestSuite(suiteId);
      setState(() {});
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تشغيل مجموعة الاختبار بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل في تشغيل الاختبار'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _generateQualityReport() async {
    setState(() => _isLoading = true);
    try {
      _qualityReport = await TestSuiteManager.generateQualityReport();
      setState(() {});
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم إنشاء تقرير الجودة بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _generateFullDocumentation() async {
    setState(() => _isLoading = true);
    try {
      _documentationReport = await DocumentationGenerator.generateFullDocumentation();
      setState(() {});
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم إنشاء التوثيق الكامل بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _exportDocumentation() async {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('ميزة التصدير ستكون متاحة قريباً'),
        backgroundColor: AppColors.info,
      ),
    );
  }
}
