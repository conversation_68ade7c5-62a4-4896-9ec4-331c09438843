import 'package:flutter/foundation.dart';
import '../storage/storage_service.dart';

/// خدمة المصادقة والمستخدمين
class AuthService {
  static const String _userKey = 'current_user';
  static const String _sessionKey = 'user_session';
  
  static UserModel? _currentUser;
  static String? _sessionToken;

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    await _loadUserSession();
  }

  /// تحميل جلسة المستخدم المحفوظة
  static Future<void> _loadUserSession() async {
    try {
      final userData = await StorageService.getData(_userKey);
      final sessionToken = await StorageService.getData(_sessionKey);
      
      if (userData != null && sessionToken != null) {
        _currentUser = UserModel.fromMap(userData);
        _sessionToken = sessionToken;
        debugPrint('✅ تم تحميل جلسة المستخدم: ${_currentUser?.name}');
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل جلسة المستخدم: $e');
    }
  }

  /// تسجيل دخول كضيف
  static Future<UserModel> loginAsGuest() async {
    final guestUser = UserModel(
      id: 'guest_${DateTime.now().millisecondsSinceEpoch}',
      name: 'مستخدم ضيف',
      email: '<EMAIL>',
      type: UserType.guest,
      createdAt: DateTime.now(),
      preferences: UserPreferences.defaultPreferences(),
    );

    await _saveUserSession(guestUser);
    return guestUser;
  }

  /// تسجيل دخول بالبريد الإلكتروني (محاكاة)
  static Future<UserModel> loginWithEmail(String email, String password) async {
    // محاكاة تسجيل الدخول
    await Future.delayed(const Duration(seconds: 2));
    
    if (email.isEmpty || password.isEmpty) {
      throw AuthException('يرجى إدخال البريد الإلكتروني وكلمة المرور');
    }

    final user = UserModel(
      id: 'user_${DateTime.now().millisecondsSinceEpoch}',
      name: _extractNameFromEmail(email),
      email: email,
      type: UserType.registered,
      createdAt: DateTime.now(),
      preferences: UserPreferences.defaultPreferences(),
    );

    await _saveUserSession(user);
    return user;
  }

  /// إنشاء حساب جديد (محاكاة)
  static Future<UserModel> registerWithEmail(
    String name,
    String email,
    String password,
  ) async {
    // محاكاة إنشاء الحساب
    await Future.delayed(const Duration(seconds: 2));
    
    if (name.isEmpty || email.isEmpty || password.isEmpty) {
      throw AuthException('يرجى ملء جميع الحقول المطلوبة');
    }

    if (!_isValidEmail(email)) {
      throw AuthException('البريد الإلكتروني غير صحيح');
    }

    if (password.length < 6) {
      throw AuthException('كلمة المرور يجب أن تكون 6 أحرف على الأقل');
    }

    final user = UserModel(
      id: 'user_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      email: email,
      type: UserType.registered,
      createdAt: DateTime.now(),
      preferences: UserPreferences.defaultPreferences(),
    );

    await _saveUserSession(user);
    return user;
  }

  /// تسجيل الخروج
  static Future<void> logout() async {
    _currentUser = null;
    _sessionToken = null;
    
    await StorageService.removeData(_userKey);
    await StorageService.removeData(_sessionKey);
    
    debugPrint('✅ تم تسجيل الخروج بنجاح');
  }

  /// حفظ جلسة المستخدم
  static Future<void> _saveUserSession(UserModel user) async {
    _currentUser = user;
    _sessionToken = 'session_${DateTime.now().millisecondsSinceEpoch}';
    
    await StorageService.saveData(_userKey, user.toMap());
    await StorageService.saveData(_sessionKey, _sessionToken);
    
    debugPrint('✅ تم حفظ جلسة المستخدم: ${user.name}');
  }

  /// الحصول على المستخدم الحالي
  static UserModel? get currentUser => _currentUser;

  /// التحقق من تسجيل الدخول
  static bool get isLoggedIn => _currentUser != null;

  /// التحقق من نوع المستخدم
  static bool get isGuest => _currentUser?.type == UserType.guest;

  /// تحديث تفضيلات المستخدم
  static Future<void> updateUserPreferences(UserPreferences preferences) async {
    if (_currentUser == null) return;

    _currentUser = _currentUser!.copyWith(preferences: preferences);
    await StorageService.saveData(_userKey, _currentUser!.toMap());
    
    debugPrint('✅ تم تحديث تفضيلات المستخدم');
  }

  /// تحديث ملف المستخدم
  static Future<void> updateUserProfile({
    String? name,
    String? email,
  }) async {
    if (_currentUser == null) return;

    _currentUser = _currentUser!.copyWith(
      name: name ?? _currentUser!.name,
      email: email ?? _currentUser!.email,
    );
    
    await StorageService.saveData(_userKey, _currentUser!.toMap());
    debugPrint('✅ تم تحديث ملف المستخدم');
  }

  /// استخراج الاسم من البريد الإلكتروني
  static String _extractNameFromEmail(String email) {
    return email.split('@').first.replaceAll('.', ' ').replaceAll('_', ' ');
  }

  /// التحقق من صحة البريد الإلكتروني
  static bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
}

/// نموذج المستخدم
class UserModel {
  final String id;
  final String name;
  final String email;
  final UserType type;
  final DateTime createdAt;
  final UserPreferences preferences;

  UserModel({
    required this.id,
    required this.name,
    required this.email,
    required this.type,
    required this.createdAt,
    required this.preferences,
  });

  UserModel copyWith({
    String? name,
    String? email,
    UserType? type,
    UserPreferences? preferences,
  }) {
    return UserModel(
      id: id,
      name: name ?? this.name,
      email: email ?? this.email,
      type: type ?? this.type,
      createdAt: createdAt,
      preferences: preferences ?? this.preferences,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'type': type.toString(),
      'createdAt': createdAt.toIso8601String(),
      'preferences': preferences.toMap(),
    };
  }

  factory UserModel.fromMap(Map<String, dynamic> map) {
    return UserModel(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      email: map['email'] ?? '',
      type: UserType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => UserType.guest,
      ),
      createdAt: DateTime.parse(map['createdAt'] ?? DateTime.now().toIso8601String()),
      preferences: UserPreferences.fromMap(map['preferences'] ?? {}),
    );
  }
}

/// أنواع المستخدمين
enum UserType {
  guest,
  registered,
  premium,
}

/// تفضيلات المستخدم
class UserPreferences {
  final String language;
  final String theme;
  final bool notifications;
  final String defaultAIProvider;
  final Map<String, dynamic> customSettings;

  UserPreferences({
    required this.language,
    required this.theme,
    required this.notifications,
    required this.defaultAIProvider,
    required this.customSettings,
  });

  static UserPreferences defaultPreferences() {
    return UserPreferences(
      language: 'ar',
      theme: 'dark',
      notifications: true,
      defaultAIProvider: 'openai',
      customSettings: {},
    );
  }

  UserPreferences copyWith({
    String? language,
    String? theme,
    bool? notifications,
    String? defaultAIProvider,
    Map<String, dynamic>? customSettings,
  }) {
    return UserPreferences(
      language: language ?? this.language,
      theme: theme ?? this.theme,
      notifications: notifications ?? this.notifications,
      defaultAIProvider: defaultAIProvider ?? this.defaultAIProvider,
      customSettings: customSettings ?? this.customSettings,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'language': language,
      'theme': theme,
      'notifications': notifications,
      'defaultAIProvider': defaultAIProvider,
      'customSettings': customSettings,
    };
  }

  factory UserPreferences.fromMap(Map<String, dynamic> map) {
    return UserPreferences(
      language: map['language'] ?? 'ar',
      theme: map['theme'] ?? 'dark',
      notifications: map['notifications'] ?? true,
      defaultAIProvider: map['defaultAIProvider'] ?? 'openai',
      customSettings: Map<String, dynamic>.from(map['customSettings'] ?? {}),
    );
  }
}

/// استثناء المصادقة
class AuthException implements Exception {
  final String message;
  AuthException(this.message);

  @override
  String toString() => 'AuthException: $message';
}
