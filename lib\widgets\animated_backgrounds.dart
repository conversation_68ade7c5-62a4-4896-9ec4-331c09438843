import 'package:flutter/material.dart';
import 'dart:math' as math;
import 'dart:ui' as ui;
import '../utils/app_colors.dart';

// خلفية الجسيمات المتحركة
class ParticleBackground extends StatefulWidget {
  final Color particleColor;
  final int particleCount;
  final double maxRadius;
  final Widget child;

  const ParticleBackground({
    Key? key,
    this.particleColor = AppColors.primaryPurple,
    this.particleCount = 50,
    this.maxRadius = 3,
    required this.child,
  }) : super(key: key);

  @override
  State<ParticleBackground> createState() => _ParticleBackgroundState();
}

class _ParticleBackgroundState extends State<ParticleBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late List<Particle> _particles;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 60),
      vsync: this,
    )..repeat();

    _particles = List.generate(widget.particleCount, (index) {
      return Particle(
        position: Offset(
          math.Random().nextDouble(),
          math.Random().nextDouble(),
        ),
        velocity: Offset(
          (math.Random().nextDouble() - 0.5) * 0.002,
          (math.Random().nextDouble() - 0.5) * 0.002,
        ),
        radius: math.Random().nextDouble() * widget.maxRadius + 1,
        opacity: math.Random().nextDouble() * 0.5 + 0.5,
      );
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return CustomPaint(
              painter: ParticlePainter(
                particles: _particles,
                color: widget.particleColor,
                animationValue: _controller.value,
              ),
              size: Size.infinite,
            );
          },
        ),
        widget.child,
      ],
    );
  }
}

class Particle {
  Offset position;
  final Offset velocity;
  final double radius;
  final double opacity;

  Particle({
    required this.position,
    required this.velocity,
    required this.radius,
    required this.opacity,
  });

  void update() {
    position += velocity;

    // التفاف الجسيمات عند الحواف
    if (position.dx < 0) position = Offset(1, position.dy);
    if (position.dx > 1) position = Offset(0, position.dy);
    if (position.dy < 0) position = Offset(position.dx, 1);
    if (position.dy > 1) position = Offset(position.dx, 0);
  }
}

class ParticlePainter extends CustomPainter {
  final List<Particle> particles;
  final Color color;
  final double animationValue;

  ParticlePainter({
    required this.particles,
    required this.color,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()..style = PaintingStyle.fill;

    for (final particle in particles) {
      particle.update();

      paint.color = color.withOpacity(particle.opacity * 0.6);

      final position = Offset(
        particle.position.dx * size.width,
        particle.position.dy * size.height,
      );

      canvas.drawCircle(position, particle.radius, paint);
    }

    // رسم خطوط الاتصال بين الجسيمات القريبة
    final linePaint =
        Paint()
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5;

    for (int i = 0; i < particles.length; i++) {
      for (int j = i + 1; j < particles.length; j++) {
        final p1 = particles[i];
        final p2 = particles[j];

        final distance = (p1.position - p2.position).distance;

        if (distance < 0.15) {
          final opacity = (1 - distance / 0.15) * 0.3;
          linePaint.color = color.withOpacity(opacity);

          canvas.drawLine(
            Offset(p1.position.dx * size.width, p1.position.dy * size.height),
            Offset(p2.position.dx * size.width, p2.position.dy * size.height),
            linePaint,
          );
        }
      }
    }
  }

  @override
  bool shouldRepaint(ParticlePainter oldDelegate) {
    return true;
  }
}

// خلفية الموجات السائلة
class LiquidBackground extends StatefulWidget {
  final List<Color> colors;
  final Widget child;

  const LiquidBackground({
    Key? key,
    this.colors = const [AppColors.primaryPurple, AppColors.lightPurple],
    required this.child,
  }) : super(key: key);

  @override
  State<LiquidBackground> createState() => _LiquidBackgroundState();
}

class _LiquidBackgroundState extends State<LiquidBackground>
    with TickerProviderStateMixin {
  late AnimationController _controller1;
  late AnimationController _controller2;

  @override
  void initState() {
    super.initState();
    _controller1 = AnimationController(
      duration: const Duration(seconds: 10),
      vsync: this,
    )..repeat();

    _controller2 = AnimationController(
      duration: const Duration(seconds: 15),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller1.dispose();
    _controller2.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        AnimatedBuilder(
          animation: Listenable.merge([_controller1, _controller2]),
          builder: (context, child) {
            return CustomPaint(
              painter: LiquidPainter(
                colors: widget.colors,
                animation1: _controller1.value,
                animation2: _controller2.value,
              ),
              size: Size.infinite,
            );
          },
        ),
        widget.child,
      ],
    );
  }
}

class LiquidPainter extends CustomPainter {
  final List<Color> colors;
  final double animation1;
  final double animation2;

  LiquidPainter({
    required this.colors,
    required this.animation1,
    required this.animation2,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..style = PaintingStyle.fill
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 30);

    // موجة 1
    final path1 = Path();
    path1.moveTo(0, size.height);

    for (double x = 0; x <= size.width; x++) {
      final y =
          size.height * 0.6 +
          math.sin(
                (x / size.width * 2 * math.pi) + (animation1 * 2 * math.pi),
              ) *
              50 +
          math.sin(
                (x / size.width * 4 * math.pi) + (animation2 * 2 * math.pi),
              ) *
              20;
      path1.lineTo(x, y);
    }

    path1.lineTo(size.width, size.height);
    path1.close();

    paint.shader = LinearGradient(
      colors: [colors[0].withOpacity(0.6), colors[1].withOpacity(0.3)],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    canvas.drawPath(path1, paint);

    // موجة 2
    final path2 = Path();
    path2.moveTo(0, size.height);

    for (double x = 0; x <= size.width; x++) {
      final y =
          size.height * 0.7 +
          math.sin(
                (x / size.width * 3 * math.pi) + (animation2 * 2 * math.pi),
              ) *
              40 +
          math.sin(
                (x / size.width * 6 * math.pi) + (animation1 * 2 * math.pi),
              ) *
              15;
      path2.lineTo(x, y);
    }

    path2.lineTo(size.width, size.height);
    path2.close();

    paint.shader = LinearGradient(
      colors: [colors[1].withOpacity(0.5), colors[0].withOpacity(0.2)],
      begin: Alignment.topCenter,
      end: Alignment.bottomCenter,
    ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    canvas.drawPath(path2, paint);
  }

  @override
  bool shouldRepaint(LiquidPainter oldDelegate) {
    return oldDelegate.animation1 != animation1 ||
        oldDelegate.animation2 != animation2;
  }
}

// خلفية الشبكة المتحركة
class GridBackground extends StatefulWidget {
  final Color gridColor;
  final double gridSize;
  final Widget child;

  const GridBackground({
    Key? key,
    this.gridColor = AppColors.primaryPurple,
    this.gridSize = 50,
    required this.child,
  }) : super(key: key);

  @override
  State<GridBackground> createState() => _GridBackgroundState();
}

class _GridBackgroundState extends State<GridBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 20),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return CustomPaint(
              painter: GridPainter(
                gridColor: widget.gridColor,
                gridSize: widget.gridSize,
                animationValue: _controller.value,
              ),
              size: Size.infinite,
            );
          },
        ),
        widget.child,
      ],
    );
  }
}

class GridPainter extends CustomPainter {
  final Color gridColor;
  final double gridSize;
  final double animationValue;

  GridPainter({
    required this.gridColor,
    required this.gridSize,
    required this.animationValue,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = gridColor.withOpacity(0.1)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1;

    // رسم الخطوط الأفقية
    for (double y = 0; y <= size.height; y += gridSize) {
      final path = Path();
      path.moveTo(0, y);

      for (double x = 0; x <= size.width; x += 10) {
        final offset =
            math.sin((x / size.width + animationValue) * 2 * math.pi) * 5;
        path.lineTo(x, y + offset);
      }

      canvas.drawPath(path, paint);
    }

    // رسم الخطوط العمودية
    for (double x = 0; x <= size.width; x += gridSize) {
      final path = Path();
      path.moveTo(x, 0);

      for (double y = 0; y <= size.height; y += 10) {
        final offset =
            math.cos((y / size.height + animationValue) * 2 * math.pi) * 5;
        path.lineTo(x + offset, y);
      }

      canvas.drawPath(path, paint);
    }

    // رسم نقاط التقاطع المتوهجة
    final glowPaint = Paint()..style = PaintingStyle.fill;

    for (double x = 0; x <= size.width; x += gridSize) {
      for (double y = 0; y <= size.height; y += gridSize) {
        final intensity =
            (math.sin((x + y) / 100 + animationValue * 2 * math.pi) + 1) / 2;

        glowPaint.shader = RadialGradient(
          colors: [
            gridColor.withOpacity(intensity * 0.5),
            gridColor.withOpacity(0),
          ],
        ).createShader(Rect.fromCircle(center: Offset(x, y), radius: 20));

        canvas.drawCircle(Offset(x, y), 20, glowPaint);
      }
    }
  }

  @override
  bool shouldRepaint(GridPainter oldDelegate) {
    return oldDelegate.animationValue != animationValue;
  }
}

// خلفية التدرج المتحرك
class AnimatedGradientBackground extends StatefulWidget {
  final List<Color> colors;
  final Widget child;

  const AnimatedGradientBackground({
    Key? key,
    required this.colors,
    required this.child,
  }) : super(key: key);

  @override
  State<AnimatedGradientBackground> createState() =>
      _AnimatedGradientBackgroundState();
}

class _AnimatedGradientBackgroundState extends State<AnimatedGradientBackground>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        AnimatedBuilder(
          animation: _controller,
          builder: (context, child) {
            return Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: widget.colors,
                  begin: Alignment(
                    math.sin(_controller.value * 2 * math.pi),
                    math.cos(_controller.value * 2 * math.pi),
                  ),
                  end: Alignment(
                    -math.sin(_controller.value * 2 * math.pi),
                    -math.cos(_controller.value * 2 * math.pi),
                  ),
                ),
              ),
            );
          },
        ),
        widget.child,
      ],
    );
  }
}
