/// نماذج البيانات للوحات المعلومات

import 'package:equatable/equatable.dart';

/// أنواع لوحات المعلومات
enum DashboardType {
  overview,
  analytics,
  performance,
  usage,
  insights,
  predictions,
  custom,
}

/// أنواع الويدجت
enum WidgetType {
  chart,
  metric,
  list,
  progress,
  gauge,
  heatmap,
  timeline,
  table,
  insight,
  prediction,
}

/// أحجام الويدجت
enum WidgetSize {
  small,
  medium,
  large,
  extraLarge,
}

/// نموذج لوحة المعلومات
class AnalyticsDashboard extends Equatable {
  final String id;
  final String title;
  final String description;
  final DashboardType type;
  final List<DashboardWidget> widgets;
  final Map<String, dynamic> settings;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String name;
  final List<String> widgetIds;
  final bool isActive;

  const AnalyticsDashboard({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.widgets,
    required this.settings,
    required this.createdAt,
    required this.updatedAt,
    required this.name,
    required this.widgetIds,
    this.isActive = true,
  });

  factory AnalyticsDashboard.fromJson(Map<String, dynamic> json) {
    return AnalyticsDashboard(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: DashboardType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => DashboardType.overview,
      ),
      widgets: (json['widgets'] as List)
          .map((w) => DashboardWidget.fromJson(w as Map<String, dynamic>))
          .toList(),
      settings: Map<String, dynamic>.from(json['settings'] as Map),
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      name: json['name'] as String? ?? json['title'] as String,
      widgetIds: List<String>.from(json['widget_ids'] as List? ?? []),
      isActive: json['is_active'] as bool? ?? true,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'widgets': widgets.map((w) => w.toJson()).toList(),
      'settings': settings,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  AnalyticsDashboard copyWith({
    String? id,
    String? title,
    String? description,
    DashboardType? type,
    List<DashboardWidget>? widgets,
    Map<String, dynamic>? settings,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? name,
    List<String>? widgetIds,
    bool? isActive,
  }) {
    return AnalyticsDashboard(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      widgets: widgets ?? this.widgets,
      settings: settings ?? this.settings,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      name: name ?? this.name,
      widgetIds: widgetIds ?? this.widgetIds,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  List<Object?> get props => [id, title, description, type, widgets, settings, createdAt, updatedAt];
}

/// نموذج ويدجت لوحة المعلومات
class DashboardWidget extends Equatable {
  final String id;
  final String title;
  final String description;
  final WidgetType type;
  final WidgetSize size;
  final Map<String, dynamic> data;
  final Map<String, dynamic> config;
  final int position;
  final bool isVisible;
  final DateTime createdAt;
  final DateTime updatedAt;

  const DashboardWidget({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.size,
    required this.data,
    required this.config,
    required this.position,
    this.isVisible = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory DashboardWidget.fromJson(Map<String, dynamic> json) {
    return DashboardWidget(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      type: WidgetType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => WidgetType.metric,
      ),
      size: WidgetSize.values.firstWhere(
        (e) => e.name == json['size'],
        orElse: () => WidgetSize.medium,
      ),
      data: Map<String, dynamic>.from(json['data'] as Map),
      config: Map<String, dynamic>.from(json['config'] as Map),
      position: json['position'] as int,
      isVisible: json['is_visible'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'type': type.name,
      'size': size.name,
      'data': data,
      'config': config,
      'position': position,
    };
  }

  DashboardWidget copyWith({
    String? id,
    String? title,
    String? description,
    WidgetType? type,
    WidgetSize? size,
    Map<String, dynamic>? data,
    Map<String, dynamic>? config,
    int? position,
    bool? isVisible,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return DashboardWidget(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      size: size ?? this.size,
      data: data ?? this.data,
      config: config ?? this.config,
      position: position ?? this.position,
      isVisible: isVisible ?? this.isVisible,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  @override
  List<Object?> get props => [id, title, description, type, size, data, config, position];
}

/// نموذج تخطيط لوحة المعلومات
class DashboardLayout extends Equatable {
  final String id;
  final String dashboardId;
  final List<WidgetPosition> positions;
  final Map<String, dynamic> settings;

  const DashboardLayout({
    required this.id,
    required this.dashboardId,
    required this.positions,
    required this.settings,
  });

  factory DashboardLayout.fromJson(Map<String, dynamic> json) {
    return DashboardLayout(
      id: json['id'] as String,
      dashboardId: json['dashboard_id'] as String,
      positions: (json['positions'] as List)
          .map((p) => WidgetPosition.fromJson(p as Map<String, dynamic>))
          .toList(),
      settings: Map<String, dynamic>.from(json['settings'] as Map),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'dashboard_id': dashboardId,
      'positions': positions.map((p) => p.toJson()).toList(),
      'settings': settings,
    };
  }

  @override
  List<Object?> get props => [id, dashboardId, positions, settings];
}

/// نموذج موضع الويدجت
class WidgetPosition extends Equatable {
  final String widgetId;
  final int x;
  final int y;
  final int width;
  final int height;

  const WidgetPosition({
    required this.widgetId,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  factory WidgetPosition.fromJson(Map<String, dynamic> json) {
    return WidgetPosition(
      widgetId: json['widget_id'] as String,
      x: json['x'] as int,
      y: json['y'] as int,
      width: json['width'] as int,
      height: json['height'] as int,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'widget_id': widgetId,
      'x': x,
      'y': y,
      'width': width,
      'height': height,
    };
  }

  @override
  List<Object?> get props => [widgetId, x, y, width, height];
}

/// نموذج تقرير لوحة المعلومات
class DashboardReport extends Equatable {
  final String id;
  final String dashboardId;
  final String title;
  final Map<String, dynamic> data;
  final DateTime generatedAt;
  final String format;
  final String dashboardName;
  final int widgetCount;
  final Map<String, dynamic> summary;
  final List<dynamic> insights;
  final List<dynamic> predictions;

  const DashboardReport({
    required this.id,
    required this.dashboardId,
    required this.title,
    required this.data,
    required this.generatedAt,
    required this.format,
    required this.dashboardName,
    required this.widgetCount,
    required this.summary,
    required this.insights,
    required this.predictions,
  });

  factory DashboardReport.fromJson(Map<String, dynamic> json) {
    return DashboardReport(
      id: json['id'] as String,
      dashboardId: json['dashboard_id'] as String,
      title: json['title'] as String,
      data: Map<String, dynamic>.from(json['data'] as Map),
      generatedAt: DateTime.parse(json['generated_at'] as String),
      format: json['format'] as String,
      dashboardName: json['dashboard_name'] as String? ?? '',
      widgetCount: json['widget_count'] as int? ?? 0,
      summary: Map<String, dynamic>.from(json['summary'] as Map? ?? {}),
      insights: List<dynamic>.from(json['insights'] as List? ?? []),
      predictions: List<dynamic>.from(json['predictions'] as List? ?? []),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'dashboard_id': dashboardId,
      'title': title,
      'data': data,
      'generated_at': generatedAt.toIso8601String(),
      'format': format,
    };
  }

  @override
  List<Object?> get props => [id, dashboardId, title, data, generatedAt, format];
}
