import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
// import 'package:file_picker/file_picker.dart';  // مؤقتاً معطل
import 'dart:io';
import 'dart:convert';
import '../core/services/real_tools_service.dart';
import '../widgets/enhanced_widgets.dart';
// import '../widgets/glassmorphism_widgets.dart';  // غير مستخدم
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';

/// شاشة تحليل الصور بالذكاء الاصطناعي
class ImageAnalysisScreen extends StatefulWidget {
  const ImageAnalysisScreen({super.key});

  @override
  State<ImageAnalysisScreen> createState() => _ImageAnalysisScreenState();
}

class _ImageAnalysisScreenState extends State<ImageAnalysisScreen>
    with TickerProviderStateMixin {
  File? _selectedImage;
  String _analysisResult = '';
  bool _isAnalyzing = false;
  String _selectedAnalysisType = 'general';
  final List<String> _specificQuestions = [];
  final TextEditingController _questionController = TextEditingController();

  late AnimationController _fadeController;
  late AnimationController _slideController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  final Map<String, String> _analysisTypes = {
    'general': 'تحليل عام',
    'detailed': 'تحليل مفصل',
    'technical': 'تحليل تقني',
    'artistic': 'تحليل فني',
    'medical': 'تحليل طبي',
    'educational': 'تحليل تعليمي',
  };

  @override
  void initState() {
    super.initState();
    _initAnimations();
  }

  void _initAnimations() {
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(parent: _slideController, curve: Curves.easeOut));

    _fadeController.forward();
    _slideController.forward();
  }

  @override
  void dispose() {
    _fadeController.dispose();
    _slideController.dispose();
    _questionController.dispose();
    super.dispose();
  }

  Future<void> _pickImageFromCamera() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.camera,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _analysisResult = '';
        });
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في التقاط الصورة: $e');
    }
  }

  Future<void> _pickImageFromGallery() async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _analysisResult = '';
        });
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في اختيار الصورة: $e');
    }
  }

  Future<void> _pickImageFromFiles() async {
    try {
      // مؤقتاً معطل - يمكن استخدام image_picker بدلاً من file_picker
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: ImageSource.gallery,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        setState(() {
          _selectedImage = File(image.path);
          _analysisResult = '';
        });
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في اختيار الملف: $e');
    }
  }

  Future<void> _analyzeImage() async {
    if (_selectedImage == null) {
      _showErrorSnackBar('يرجى اختيار صورة أولاً');
      return;
    }

    setState(() {
      _isAnalyzing = true;
      _analysisResult = '';
    });

    try {
      // تحويل الصورة إلى base64 أو رفعها إلى خدمة
      final bytes = await _selectedImage!.readAsBytes();
      final base64Image = base64Encode(bytes);

      final result = await RealToolsService.analyzeImage(
        imageUrl: 'data:image/jpeg;base64,$base64Image',
        analysisType: _selectedAnalysisType,
        specificQuestions: _specificQuestions.isNotEmpty ? _specificQuestions : [],
      );

      setState(() {
        _analysisResult = result['analysis'] ?? 'لم يتم الحصول على نتيجة';
      });

      _showSuccessSnackBar('تم تحليل الصورة بنجاح!');
    } catch (e) {
      _showErrorSnackBar('خطأ في تحليل الصورة: $e');
    } finally {
      setState(() {
        _isAnalyzing = false;
      });
    }
  }

  void _addSpecificQuestion() {
    if (_questionController.text.trim().isNotEmpty) {
      setState(() {
        _specificQuestions.add(_questionController.text.trim());
        _questionController.clear();
      });
    }
  }

  void _removeQuestion(int index) {
    setState(() {
      _specificQuestions.removeAt(index);
    });
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('🔍 تحليل الصور'),
        backgroundColor: AppColors.darkPurple,
        elevation: 0,
      ),
      body: FadeTransition(
        opacity: _fadeAnimation,
        child: SlideTransition(
          position: _slideAnimation,
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                _buildImageSelectionSection(),
                const SizedBox(height: 20),
                _buildAnalysisOptionsSection(),
                const SizedBox(height: 20),
                _buildSpecificQuestionsSection(),
                const SizedBox(height: 20),
                _buildAnalyzeButton(),
                const SizedBox(height: 20),
                _buildResultsSection(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildImageSelectionSection() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '📷 اختيار الصورة',
            style: AppTextStyles.heading.copyWith(
              color: AppColors.primaryPurple,
            ),
          ),
          const SizedBox(height: 15),
          if (_selectedImage != null) ...[
            Container(
              height: 200,
              width: double.infinity,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(12),
                image: DecorationImage(
                  image: FileImage(_selectedImage!),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(height: 15),
          ],
          Row(
            children: [
              Expanded(
                child: EnhancedButton(
                  text: '📸 كاميرا',
                  onPressed: _pickImageFromCamera,
                  size: ButtonSize.small,
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: EnhancedButton(
                  text: '🖼️ معرض',
                  onPressed: _pickImageFromGallery,
                  size: ButtonSize.small,
                ),
              ),
              const SizedBox(width: 10),
              Expanded(
                child: EnhancedButton(
                  text: '📁 ملفات',
                  onPressed: _pickImageFromFiles,
                  size: ButtonSize.small,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildAnalysisOptionsSection() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '⚙️ نوع التحليل',
            style: AppTextStyles.heading.copyWith(
              color: AppColors.primaryPurple,
            ),
          ),
          const SizedBox(height: 15),
          DropdownButtonFormField<String>(
            value: _selectedAnalysisType,
            decoration: InputDecoration(
              filled: true,
              fillColor: AppColors.darkGrey,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
            ),
            dropdownColor: AppColors.darkGrey,
            style: AppTextStyles.body.copyWith(color: AppColors.white),
            items: _analysisTypes.entries.map((entry) {
              return DropdownMenuItem<String>(
                value: entry.key,
                child: Text(entry.value),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedAnalysisType = value;
                });
              }
            },
          ),
        ],
      ),
    );
  }

  Widget _buildSpecificQuestionsSection() {
    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '❓ أسئلة محددة (اختياري)',
            style: AppTextStyles.heading.copyWith(
              color: AppColors.primaryPurple,
            ),
          ),
          const SizedBox(height: 15),
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _questionController,
                  decoration: InputDecoration(
                    hintText: 'أدخل سؤالاً محدداً حول الصورة...',
                    filled: true,
                    fillColor: AppColors.darkGrey,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    hintStyle: AppTextStyles.body.copyWith(
                      color: AppColors.white.withValues(alpha: 0.5),
                    ),
                  ),
                  style: AppTextStyles.body.copyWith(color: AppColors.white),
                  maxLines: 2,
                ),
              ),
              const SizedBox(width: 10),
              IconButton(
                onPressed: _addSpecificQuestion,
                icon: const Icon(Icons.add_circle, color: AppColors.primaryPurple),
                iconSize: 32,
              ),
            ],
          ),
          if (_specificQuestions.isNotEmpty) ...[
            const SizedBox(height: 15),
            ...List.generate(_specificQuestions.length, (index) {
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: AppColors.darkGrey.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        _specificQuestions[index],
                        style: AppTextStyles.body,
                      ),
                    ),
                    IconButton(
                      onPressed: () => _removeQuestion(index),
                      icon: const Icon(Icons.remove_circle, color: AppColors.error),
                      iconSize: 20,
                    ),
                  ],
                ),
              );
            }),
          ],
        ],
      ),
    );
  }

  Widget _buildAnalyzeButton() {
    return EnhancedButton(
      text: _isAnalyzing ? 'جاري التحليل...' : '🔍 تحليل الصورة',
      onPressed: _isAnalyzing ? null : _analyzeImage,
      isLoading: _isAnalyzing,
      size: ButtonSize.large,
    );
  }

  Widget _buildResultsSection() {
    if (_analysisResult.isEmpty && !_isAnalyzing) {
      return const SizedBox.shrink();
    }

    return EnhancedCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            '📊 نتائج التحليل',
            style: AppTextStyles.heading.copyWith(
              color: AppColors.primaryPurple,
            ),
          ),
          const SizedBox(height: 15),
          if (_isAnalyzing)
            const Center(
              child: EnhancedLoadingIndicator(
                message: 'جاري تحليل الصورة...',
              ),
            )
          else
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: AppColors.darkGrey.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: AppColors.primaryPurple.withValues(alpha: 0.3),
                ),
              ),
              child: Text(
                _analysisResult,
                style: AppTextStyles.body.copyWith(
                  height: 1.6,
                  fontSize: 16,
                ),
              ),
            ),
        ],
      ),
    );
  }
}
