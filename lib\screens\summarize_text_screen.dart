import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../core/services/enhanced_ai_service.dart';
import '../widgets/modern_ui_components.dart';

class SummarizeTextScreen extends StatefulWidget {
  const SummarizeTextScreen({super.key});

  @override
  State<SummarizeTextScreen> createState() => _SummarizeTextScreenState();
}

class _SummarizeTextScreenState extends State<SummarizeTextScreen>
    with TickerProviderStateMixin {
  final TextEditingController _textController = TextEditingController();
  String? _summary;
  bool _loading = false;
  String _selectedSummaryType = 'شامل';
  int _maxLength = 300;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<String> _summaryTypes = [
    'شامل',
    'نقاط رئيسية',
    'ملخص سريع',
    'تحليلي',
    'إبداعي',
  ];

  final List<Map<String, dynamic>> _quickExamples = [
    {
      'icon': Icons.article_outlined,
      'title': 'مقال علمي طويل',
      'text': 'الذكاء الاصطناعي هو مجال واسع في علوم الحاسوب يهدف إلى إنشاء أنظمة قادرة على أداء مهام تتطلب عادة ذكاءً بشرياً. يشمل هذا المجال تقنيات مثل التعلم الآلي، ومعالجة اللغات الطبيعية، والرؤية الحاسوبية، والروبوتات. في السنوات الأخيرة، شهد الذكاء الاصطناعي تطوراً هائلاً بفضل التقدم في قوة الحوسبة وتوفر البيانات الضخمة.',
      'color': Colors.blue,
    },
    {
      'icon': Icons.business_outlined,
      'title': 'تقرير أعمال',
      'text': 'أظهرت نتائج الربع الثالث من العام الحالي نمواً قوياً في جميع قطاعات الشركة. حققت الشركة إيرادات بلغت 2.5 مليار دولار، بزيادة قدرها 15% مقارنة بالفترة نفسها من العام الماضي. كما ارتفعت الأرباح الصافية إلى 450 مليون دولار. ويُعزى هذا النمو إلى استراتيجية التوسع الجديدة والاستثمار في التكنولوجيا.',
      'color': Colors.green,
    },
    {
      'icon': Icons.school_outlined,
      'title': 'محتوى تعليمي',
      'text': 'التعلم الإلكتروني أصبح جزءاً أساسياً من النظام التعليمي الحديث. يوفر هذا النوع من التعلم مرونة في الوقت والمكان، ويسمح للطلاب بالتعلم وفقاً لسرعتهم الخاصة. كما يتيح الوصول إلى موارد تعليمية متنوعة ومحدثة. ومع ذلك، يواجه التعلم الإلكتروني تحديات مثل الحاجة إلى الانضباط الذاتي والتفاعل المحدود مع المعلمين والزملاء.',
      'color': Colors.orange,
    },
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _textController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _summarize() async {
    if (_textController.text.trim().isEmpty) {
      _showErrorMessage('يرجى إدخال النص المراد تلخيصه');
      return;
    }

    setState(() {
      _loading = true;
      _summary = null;
    });

    try {
      final result = await EnhancedAIService.advancedSummarize(
        text: _textController.text,
        style: _selectedSummaryType.toLowerCase(),
        maxLength: _maxLength,
      );

      setState(() {
        _summary = result;
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _loading = false;
      });
      _showErrorMessage('حدث خطأ أثناء التلخيص: ${e.toString()}');
    }
  }

  void _showErrorMessage(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: AppColors.error,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.darkPurple.withValues(alpha: 0.9),
              AppColors.background,
              AppColors.primaryPurple.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                _buildHeader(),
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildTextInputSection(),
                        const SizedBox(height: 24),
                        _buildSummaryTypeSelection(),
                        const SizedBox(height: 24),
                        _buildLengthSlider(),
                        const SizedBox(height: 24),
                        _buildQuickExamples(),
                        const SizedBox(height: 32),
                        _buildSummarizeButton(),
                        if (_summary != null) ...[
                          const SizedBox(height: 24),
                          _buildResult(),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.electricBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => Navigator.pop(context),
                borderRadius: BorderRadius.circular(12),
                child: const Padding(
                  padding: EdgeInsets.all(12),
                  child: Icon(Icons.arrow_back, color: Colors.white, size: 20),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.summarize_outlined,
                      color: AppColors.electricBlue,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'تلخيص النصوص الذكي',
                      style: AppTextStyles.heading2.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Text(
                  'لخص النصوص الطويلة في نقاط مفيدة',
                  style: AppTextStyles.body.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextInputSection() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.electricBlue.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.text_fields_outlined,
                  color: AppColors.electricBlue,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'النص المراد تلخيصه',
                style: AppTextStyles.heading3.copyWith(color: Colors.white),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _textController,
            maxLines: 8,
            style: AppTextStyles.body.copyWith(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'الصق النص الذي تريد تلخيصه هنا...',
              hintStyle: AppTextStyles.body.copyWith(
                color: AppColors.textSecondary,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: AppColors.electricBlue.withValues(alpha: 0.3),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: AppColors.electricBlue.withValues(alpha: 0.3),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(color: AppColors.electricBlue, width: 2),
              ),
              filled: true,
              fillColor: AppColors.darkGrey.withValues(alpha: 0.3),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryTypeSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع التلخيص',
          style: AppTextStyles.heading3.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 50,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _summaryTypes.length,
            itemBuilder: (context, index) {
              final type = _summaryTypes[index];
              final isSelected = type == _selectedSummaryType;

              return Container(
                margin: const EdgeInsets.only(right: 12),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        _selectedSummaryType = type;
                      });
                    },
                    borderRadius: BorderRadius.circular(25),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        gradient: isSelected
                            ? LinearGradient(
                                colors: [
                                  AppColors.primaryPurple,
                                  AppColors.electricBlue,
                                ],
                              )
                            : LinearGradient(
                                colors: [
                                  AppColors.darkGrey.withValues(alpha: 0.5),
                                  AppColors.darkGrey.withValues(alpha: 0.3),
                                ],
                              ),
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                          color: isSelected
                              ? AppColors.electricBlue
                              : AppColors.electricBlue.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        type,
                        style: AppTextStyles.body.copyWith(
                          color: Colors.white,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildLengthSlider() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'طول الملخص',
                style: AppTextStyles.heading3.copyWith(color: Colors.white),
              ),
              Text(
                '$_maxLength كلمة',
                style: AppTextStyles.body.copyWith(
                  color: AppColors.electricBlue,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: AppColors.electricBlue,
              inactiveTrackColor: AppColors.darkGrey,
              thumbColor: AppColors.electricBlue,
              overlayColor: AppColors.electricBlue.withValues(alpha: 0.2),
            ),
            child: Slider(
              value: _maxLength.toDouble(),
              min: 100,
              max: 1000,
              divisions: 18,
              onChanged: (value) {
                setState(() {
                  _maxLength = value.round();
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickExamples() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أمثلة سريعة',
          style: AppTextStyles.heading3.copyWith(color: Colors.white),
        ),
        const SizedBox(height: 16),
        ...List.generate(_quickExamples.length, (index) {
          final example = _quickExamples[index];
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  _textController.text = example['text'];
                },
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.darkGrey.withValues(alpha: 0.6),
                        AppColors.darkGrey.withValues(alpha: 0.4),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: (example['color'] as Color).withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: (example['color'] as Color).withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          example['icon'],
                          color: example['color'],
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          example['title'],
                          style: AppTextStyles.body.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildSummarizeButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.primaryPurple, AppColors.electricBlue],
        ),
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryPurple.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _loading ? null : _summarize,
          borderRadius: BorderRadius.circular(28),
          child: Center(
            child: _loading
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.auto_awesome, color: Colors.white, size: 24),
                      const SizedBox(width: 8),
                      Text(
                        'تلخيص النص',
                        style: AppTextStyles.button.copyWith(
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildResult() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.glowPink.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.auto_awesome,
                  color: AppColors.glowPink,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'الملخص',
                style: AppTextStyles.heading3.copyWith(color: Colors.white),
              ),
              const Spacer(),
              IconButton(
                onPressed: () {
                  Clipboard.setData(ClipboardData(text: _summary!));
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: const Text('تم نسخ الملخص'),
                      backgroundColor: AppColors.primaryPurple,
                      behavior: SnackBarBehavior.floating,
                    ),
                  );
                },
                icon: const Icon(Icons.copy, color: Colors.white),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.glowPink.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Text(
              _summary!,
              style: AppTextStyles.body.copyWith(
                color: Colors.white,
                height: 1.6,
              ),
            ),
          ),
        ],
      ),
    );
  }
}