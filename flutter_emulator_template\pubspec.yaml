name: flutter_emulator_app
description: "قالب Flutter محسن للمحاكي - بدون مشاكل"

publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: ^3.7.2

dependencies:
  flutter:
    sdk: flutter
  
  # Core UI
  cupertino_icons: ^1.0.8
  
  # State Management (اختر واحد)
  provider: ^6.1.2
  # riverpod: ^2.5.1
  # flutter_riverpod: ^2.5.1
  
  # HTTP & API
  http: ^1.2.1
  dio: ^5.4.3
  
  # Local Storage
  shared_preferences: ^2.2.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Security
  flutter_secure_storage: ^9.2.2
  crypto: ^3.0.3
  
  # UI & Animations
  animations: ^2.0.11
  
  # Utils
  intl: ^0.19.0
  uuid: ^4.4.0
  
  # Media (محسن للمحاكي)
  image_picker: ^1.1.2
  cached_network_image: ^3.3.1
  
  # Permissions
  permission_handler: ^11.3.1
  
  # Navigation
  go_router: ^14.2.7

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^4.0.0
  build_runner: ^2.4.12

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
  
  # fonts:
  #   - family: Cairo
  #     fonts:
  #       - asset: assets/fonts/Cairo-Regular.ttf
  #       - asset: assets/fonts/Cairo-Bold.ttf
  #         weight: 700
