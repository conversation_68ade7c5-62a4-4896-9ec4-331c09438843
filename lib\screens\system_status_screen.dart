import 'package:flutter/material.dart';
import '../core/services/real_api_validator.dart';
import '../tools/real_ai_tools_hub.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../widgets/animated_button.dart';

/// شاشة حالة النظام - عرض حالة APIs والأدوات
class SystemStatusScreen extends StatefulWidget {
  const SystemStatusScreen({super.key});

  @override
  State<SystemStatusScreen> createState() => _SystemStatusScreenState();
}

class _SystemStatusScreenState extends State<SystemStatusScreen> {
  bool _isLoading = false;
  Map<String, dynamic>? _healthReport;
  Map<String, dynamic>? _setupStatus;

  @override
  void initState() {
    super.initState();
    _loadSystemStatus();
  }

  Future<void> _loadSystemStatus() async {
    setState(() => _isLoading = true);
    
    try {
      final healthReport = await RealApiValidator.getHealthReport();
      final setupStatus = await RealAIToolsHub.checkSetupCompleteness();
      
      setState(() {
        _healthReport = healthReport.toJson();
        _setupStatus = setupStatus;
      });
    } catch (e) {
      _showError('خطأ في تحميل حالة النظام: $e');
    }
    
    setState(() => _isLoading = false);
  }

  Future<void> _refreshStatus() async {
    await _loadSystemStatus();
    _showSuccess('تم تحديث حالة النظام');
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.darkPurple.withValues(alpha: 0.2),
              AppColors.background,
              AppColors.primaryPurple.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: Column(
          children: [
            _buildHeader(),
            Expanded(
              child: _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : _buildContent(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.fromLTRB(24, 60, 24, 24),
      child: Row(
        children: [
          AnimatedButton(
            onTap: () => Navigator.pop(context),
            gradient: LinearGradient(
              colors: [
                AppColors.darkGrey.withValues(alpha: 0.5),
                AppColors.midGrey.withValues(alpha: 0.5),
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            padding: const EdgeInsets.all(12),
            child: const Icon(
              Icons.arrow_back_ios_new,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'حالة النظام',
                  style: AppTextStyles.heading.copyWith(
                    color: Colors.white,
                    fontSize: 24,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  'مراقبة APIs والأدوات',
                  style: AppTextStyles.body.copyWith(
                    color: Colors.white70,
                  ),
                ),
              ],
            ),
          ),
          AnimatedButton(
            onTap: _isLoading ? null : _refreshStatus,
            gradient: LinearGradient(
              colors: [
                AppColors.primaryPurple,
                AppColors.lightPurple,
              ],
            ),
            borderRadius: BorderRadius.circular(12),
            padding: const EdgeInsets.all(12),
            child: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  )
                : const Icon(
                    Icons.refresh,
                    color: Colors.white,
                    size: 20,
                  ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    if (_healthReport == null || _setupStatus == null) {
      return const Center(
        child: Text(
          'لا توجد بيانات متاحة',
          style: TextStyle(color: Colors.white60),
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildOverallStatus(),
          const SizedBox(height: 24),
          _buildApiStatus(),
          const SizedBox(height: 24),
          _buildToolsStatus(),
          const SizedBox(height: 24),
          _buildRecommendations(),
        ],
      ),
    );
  }

  Widget _buildOverallStatus() {
    final isHealthy = _healthReport!['isHealthy'] as bool;
    final healthScore = _healthReport!['healthScore'] as double;
    final completeness = _setupStatus!['completeness_percentage'] as int;
    final isReady = _setupStatus!['is_ready_for_production'] as bool;

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isReady
              ? [Colors.green.withValues(alpha: 0.3), Colors.green.withValues(alpha: 0.1)]
              : [Colors.orange.withValues(alpha: 0.3), Colors.orange.withValues(alpha: 0.1)],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isReady ? Colors.green : Colors.orange,
          width: 2,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: (isReady ? Colors.green : Colors.orange).withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  isReady ? Icons.check_circle : Icons.warning,
                  color: isReady ? Colors.green : Colors.orange,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      isReady ? 'النظام جاهز للإنتاج' : 'النظام يحتاج إعداد',
                      style: AppTextStyles.heading.copyWith(
                        color: Colors.white,
                        fontSize: 18,
                      ),
                    ),
                    Text(
                      'اكتمال الإعداد: $completeness%',
                      style: AppTextStyles.body.copyWith(
                        color: Colors.white70,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatusMetric(
                  'صحة APIs',
                  '${(healthScore * 100).round()}%',
                  isHealthy ? Colors.green : Colors.red,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatusMetric(
                  'الأدوات المتاحة',
                  '${_setupStatus!['available_tools']}/${_setupStatus!['total_tools']}',
                  completeness >= 70 ? Colors.green : Colors.orange,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusMetric(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Text(
            value,
            style: AppTextStyles.heading.copyWith(
              color: color,
              fontSize: 20,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: AppTextStyles.caption.copyWith(
              color: Colors.white70,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildApiStatus() {
    final validationResults = _healthReport!['validationResults'] as Map<String, dynamic>;
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.darkGrey, AppColors.midGrey],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.lightGrey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.api,
                color: AppColors.primaryPurple,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'حالة APIs',
                style: AppTextStyles.heading.copyWith(
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...validationResults.entries.map((entry) {
            final providerId = entry.key;
            final result = entry.value as Map<String, dynamic>;
            final isValid = result['isValid'] as bool;
            final capabilities = List<String>.from(result['capabilities'] ?? []);
            final error = result['error'] as String?;
            
            return _buildApiCard(providerId, isValid, capabilities, error);
          }),
        ],
      ),
    );
  }

  Widget _buildApiCard(String providerId, bool isValid, List<String> capabilities, String? error) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: isValid
              ? [Colors.green.withValues(alpha: 0.2), Colors.green.withValues(alpha: 0.1)]
              : [Colors.red.withValues(alpha: 0.2), Colors.red.withValues(alpha: 0.1)],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isValid ? Colors.green : Colors.red,
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isValid ? Icons.check_circle : Icons.error,
                color: isValid ? Colors.green : Colors.red,
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  _getProviderDisplayName(providerId),
                  style: AppTextStyles.body.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              Text(
                isValid ? 'متصل' : 'غير متصل',
                style: AppTextStyles.caption.copyWith(
                  color: isValid ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
          if (isValid && capabilities.isNotEmpty) ...[
            const SizedBox(height: 8),
            Wrap(
              spacing: 4,
              runSpacing: 4,
              children: capabilities.map((capability) => Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _getCapabilityLabel(capability),
                  style: AppTextStyles.caption.copyWith(
                    color: Colors.blue,
                    fontSize: 10,
                  ),
                ),
              )).toList(),
            ),
          ],
          if (!isValid && error != null) ...[
            const SizedBox(height: 8),
            Text(
              'خطأ: $error',
              style: AppTextStyles.caption.copyWith(
                color: Colors.red,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildToolsStatus() {
    final tools = RealAIToolsHub.getAvailableTools();
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [AppColors.darkGrey, AppColors.midGrey],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.lightGrey),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.build,
                color: AppColors.primaryPurple,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'حالة الأدوات',
                style: AppTextStyles.heading.copyWith(
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 1.2,
            ),
            itemCount: tools.length,
            itemBuilder: (context, index) {
              final tool = tools[index];
              final isAvailable = tool['available'] as bool;
              
              return Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: isAvailable
                        ? [Colors.green.withValues(alpha: 0.2), Colors.green.withValues(alpha: 0.1)]
                        : [Colors.grey.withValues(alpha: 0.2), Colors.grey.withValues(alpha: 0.1)],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: isAvailable ? Colors.green : Colors.grey,
                    width: 1,
                  ),
                ),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      tool['icon'] as IconData,
                      color: isAvailable ? Colors.green : Colors.grey,
                      size: 24,
                    ),
                    const SizedBox(height: 8),
                    Text(
                      tool['name'] as String,
                      style: AppTextStyles.caption.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    const SizedBox(height: 4),
                    Text(
                      isAvailable ? 'متاح' : 'غير متاح',
                      style: AppTextStyles.caption.copyWith(
                        color: isAvailable ? Colors.green : Colors.grey,
                        fontSize: 10,
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildRecommendations() {
    final recommendations = List<String>.from(_setupStatus!['recommendations'] ?? []);
    
    if (recommendations.isEmpty) return const SizedBox.shrink();
    
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.orange.withValues(alpha: 0.2), Colors.orange.withValues(alpha: 0.1)],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.orange),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.lightbulb,
                color: Colors.orange,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'توصيات التحسين',
                style: AppTextStyles.heading.copyWith(
                  color: Colors.white,
                  fontSize: 18,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          ...recommendations.map((recommendation) => Padding(
            padding: const EdgeInsets.only(bottom: 8),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Icon(
                  Icons.arrow_right,
                  color: Colors.orange,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    recommendation,
                    style: AppTextStyles.body.copyWith(
                      color: Colors.white,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  String _getProviderDisplayName(String providerId) {
    switch (providerId) {
      case 'openai':
        return 'OpenAI';
      case 'openrouter':
        return 'OpenRouter';
      case 'gemini':
        return 'Google Gemini';
      case 'anthropic':
        return 'Anthropic Claude';
      default:
        return providerId;
    }
  }

  String _getCapabilityLabel(String capability) {
    switch (capability.toLowerCase()) {
      case 'chat':
      case 'text':
        return 'نص';
      case 'image_generation':
        return 'إنشاء صور';
      case 'vision':
        return 'رؤية';
      case 'multiple_models':
        return 'نماذج متعددة';
      default:
        return capability;
    }
  }
}
