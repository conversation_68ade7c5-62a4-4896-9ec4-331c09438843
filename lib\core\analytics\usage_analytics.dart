import 'package:flutter/foundation.dart';
import '../services/storage_service.dart';

/// خدمة تحليلات الاستخدام
class UsageAnalytics {
  static const String _analyticsKey = 'usage_analytics';
  static const String _sessionsKey = 'user_sessions';

  static AnalyticsData? _currentData;
  static String? _currentSessionId;

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    await _loadAnalyticsData();
    await _startNewSession();
  }

  /// تحميل بيانات التحليلات
  static Future<void> _loadAnalyticsData() async {
    try {
      final data = StorageService.getData(_analyticsKey);
      if (data != null) {
        _currentData = AnalyticsData.fromMap(data);
      } else {
        _currentData = AnalyticsData.empty();
      }
      debugPrint('✅ تم تحميل بيانات التحليلات');
    } catch (e) {
      debugPrint('❌ خطأ في تحميل بيانات التحليلات: $e');
      _currentData = AnalyticsData.empty();
    }
  }

  /// بدء جلسة جديدة
  static Future<void> _startNewSession() async {
    _currentSessionId = 'session_${DateTime.now().millisecondsSinceEpoch}';

    final session = UserSession(
      id: _currentSessionId!,
      startTime: DateTime.now(),
      events: [],
    );

    await _saveSession(session);
    await _incrementSessionCount();

    debugPrint('✅ تم بدء جلسة جديدة: $_currentSessionId');
  }

  /// حفظ الجلسة
  static Future<void> _saveSession(UserSession session) async {
    final sessions = await _loadSessions();
    sessions.add(session);

    // الاحتفاظ بآخر 100 جلسة فقط
    if (sessions.length > 100) {
      sessions.removeRange(0, sessions.length - 100);
    }

    await StorageService.saveData(
      _sessionsKey,
      sessions.map((s) => s.toMap()).toList(),
    );
  }

  /// تحميل الجلسات
  static Future<List<UserSession>> _loadSessions() async {
    try {
      final data = StorageService.getData(_sessionsKey);
      if (data != null && data is List) {
        return data.map((s) => UserSession.fromMap(s)).toList();
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الجلسات: $e');
    }
    return [];
  }

  /// زيادة عدد الجلسات
  static Future<void> _incrementSessionCount() async {
    if (_currentData == null) return;

    _currentData = _currentData!.copyWith(
      totalSessions: _currentData!.totalSessions + 1,
      lastSessionDate: DateTime.now(),
    );

    await _saveAnalyticsData();
  }

  /// حفظ بيانات التحليلات
  static Future<void> _saveAnalyticsData() async {
    if (_currentData == null) return;

    await StorageService.saveData(_analyticsKey, _currentData!.toMap());
  }

  /// تسجيل استخدام أداة
  static Future<void> trackToolUsage(String toolName) async {
    if (_currentData == null) return;

    final updatedTools = Map<String, int>.from(_currentData!.toolsUsage);
    updatedTools[toolName] = (updatedTools[toolName] ?? 0) + 1;

    _currentData = _currentData!.copyWith(
      toolsUsage: updatedTools,
      totalToolUsage: _currentData!.totalToolUsage + 1,
    );

    await _saveAnalyticsData();
    await _trackEvent('tool_used', {'tool_name': toolName});

    debugPrint('📊 تم تسجيل استخدام الأداة: $toolName');
  }

  /// تسجيل استخدام مقدم خدمة AI
  static Future<void> trackProviderUsage(String provider) async {
    if (_currentData == null) return;

    final updatedProviders = Map<String, int>.from(_currentData!.providersUsage);
    updatedProviders[provider] = (updatedProviders[provider] ?? 0) + 1;

    _currentData = _currentData!.copyWith(
      providersUsage: updatedProviders,
    );

    await _saveAnalyticsData();
    await _trackEvent('provider_used', {'provider': provider});

    debugPrint('📊 تم تسجيل استخدام المقدم: $provider');
  }

  /// تسجيل عرض شاشة
  static Future<void> trackScreenView(String screenName) async {
    await _trackEvent('screen_view', {'screen_name': screenName});
    debugPrint('📊 تم تسجيل عرض الشاشة: $screenName');
  }

  /// تسجيل استخدام الرموز
  static Future<void> trackTokenUsage(int tokens, String provider) async {
    if (_currentData == null) return;

    final updatedTokens = Map<String, int>.from(_currentData!.tokensUsed);
    updatedTokens[provider] = (updatedTokens[provider] ?? 0) + tokens;

    _currentData = _currentData!.copyWith(
      tokensUsed: updatedTokens,
      totalTokensUsed: _currentData!.totalTokensUsed + tokens,
    );

    await _saveAnalyticsData();
    await _trackEvent('tokens_used', {
      'tokens': tokens,
      'provider': provider,
    });

    debugPrint('📊 تم تسجيل استخدام الرموز: $tokens ($provider)');
  }

  /// تسجيل حدث
  static Future<void> _trackEvent(String eventName, Map<String, dynamic> data) async {
    if (_currentSessionId == null) return;

    final event = AnalyticsEvent(
      name: eventName,
      timestamp: DateTime.now(),
      data: data,
    );

    // إضافة الحدث للجلسة الحالية
    final sessions = await _loadSessions();
    final currentSessionIndex = sessions.indexWhere((s) => s.id == _currentSessionId);

    if (currentSessionIndex != -1) {
      sessions[currentSessionIndex].events.add(event);
      await StorageService.saveData(
        _sessionsKey,
        sessions.map((s) => s.toMap()).toList(),
      );
    }
  }

  /// إنهاء الجلسة الحالية
  static Future<void> endCurrentSession() async {
    if (_currentSessionId == null) return;

    final sessions = await _loadSessions();
    final currentSessionIndex = sessions.indexWhere((s) => s.id == _currentSessionId);

    if (currentSessionIndex != -1) {
      sessions[currentSessionIndex] = sessions[currentSessionIndex].copyWith(
        endTime: DateTime.now(),
      );

      await StorageService.saveData(
        _sessionsKey,
        sessions.map((s) => s.toMap()).toList(),
      );
    }

    await _trackEvent('session_ended', {});
    debugPrint('✅ تم إنهاء الجلسة: $_currentSessionId');
  }

  /// الحصول على بيانات التحليلات
  static AnalyticsData? get analyticsData => _currentData;

  /// الحصول على إحصائيات الاستخدام
  static Future<UsageStats> getUsageStats() async {
    if (_currentData == null) {
      return UsageStats.empty();
    }

    final sessions = await _loadSessions();
    final totalSessionTime = _calculateTotalSessionTime(sessions);
    final averageSessionTime = sessions.isNotEmpty
        ? totalSessionTime ~/ sessions.length
        : 0;

    return UsageStats(
      totalSessions: _currentData!.totalSessions,
      totalToolUsage: _currentData!.totalToolUsage,
      totalTokensUsed: _currentData!.totalTokensUsed,
      totalSessionTimeMinutes: totalSessionTime,
      averageSessionTimeMinutes: averageSessionTime,
      mostUsedTool: _getMostUsedTool(),
      mostUsedProvider: _getMostUsedProvider(),
      toolsUsage: Map.from(_currentData!.toolsUsage),
      providersUsage: Map.from(_currentData!.providersUsage),
      tokensUsed: Map.from(_currentData!.tokensUsed),
      lastSessionDate: _currentData!.lastSessionDate,
    );
  }

  /// حساب إجمالي وقت الجلسات
  static int _calculateTotalSessionTime(List<UserSession> sessions) {
    int totalMinutes = 0;

    for (final session in sessions) {
      if (session.endTime != null) {
        final duration = session.endTime!.difference(session.startTime);
        totalMinutes += duration.inMinutes;
      }
    }

    return totalMinutes;
  }

  /// الحصول على الأداة الأكثر استخداماً
  static String _getMostUsedTool() {
    if (_currentData?.toolsUsage.isEmpty ?? true) return 'لا يوجد';

    return _currentData!.toolsUsage.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  /// الحصول على المقدم الأكثر استخداماً
  static String _getMostUsedProvider() {
    if (_currentData?.providersUsage.isEmpty ?? true) return 'لا يوجد';

    return _currentData!.providersUsage.entries
        .reduce((a, b) => a.value > b.value ? a : b)
        .key;
  }

  /// مسح بيانات التحليلات
  static Future<void> clearAnalytics() async {
    _currentData = AnalyticsData.empty();
    await StorageService.deleteData(_analyticsKey);
    await StorageService.deleteData(_sessionsKey);
    debugPrint('✅ تم مسح بيانات التحليلات');
  }
}

/// نموذج بيانات التحليلات
class AnalyticsData {
  final int totalSessions;
  final int totalToolUsage;
  final int totalTokensUsed;
  final Map<String, int> toolsUsage;
  final Map<String, int> providersUsage;
  final Map<String, int> tokensUsed;
  final DateTime? lastSessionDate;

  AnalyticsData({
    required this.totalSessions,
    required this.totalToolUsage,
    required this.totalTokensUsed,
    required this.toolsUsage,
    required this.providersUsage,
    required this.tokensUsed,
    this.lastSessionDate,
  });

  factory AnalyticsData.empty() {
    return AnalyticsData(
      totalSessions: 0,
      totalToolUsage: 0,
      totalTokensUsed: 0,
      toolsUsage: {},
      providersUsage: {},
      tokensUsed: {},
    );
  }

  AnalyticsData copyWith({
    int? totalSessions,
    int? totalToolUsage,
    int? totalTokensUsed,
    Map<String, int>? toolsUsage,
    Map<String, int>? providersUsage,
    Map<String, int>? tokensUsed,
    DateTime? lastSessionDate,
  }) {
    return AnalyticsData(
      totalSessions: totalSessions ?? this.totalSessions,
      totalToolUsage: totalToolUsage ?? this.totalToolUsage,
      totalTokensUsed: totalTokensUsed ?? this.totalTokensUsed,
      toolsUsage: toolsUsage ?? this.toolsUsage,
      providersUsage: providersUsage ?? this.providersUsage,
      tokensUsed: tokensUsed ?? this.tokensUsed,
      lastSessionDate: lastSessionDate ?? this.lastSessionDate,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'totalSessions': totalSessions,
      'totalToolUsage': totalToolUsage,
      'totalTokensUsed': totalTokensUsed,
      'toolsUsage': toolsUsage,
      'providersUsage': providersUsage,
      'tokensUsed': tokensUsed,
      'lastSessionDate': lastSessionDate?.toIso8601String(),
    };
  }

  factory AnalyticsData.fromMap(Map<String, dynamic> map) {
    return AnalyticsData(
      totalSessions: map['totalSessions'] ?? 0,
      totalToolUsage: map['totalToolUsage'] ?? 0,
      totalTokensUsed: map['totalTokensUsed'] ?? 0,
      toolsUsage: Map<String, int>.from(map['toolsUsage'] ?? {}),
      providersUsage: Map<String, int>.from(map['providersUsage'] ?? {}),
      tokensUsed: Map<String, int>.from(map['tokensUsed'] ?? {}),
      lastSessionDate: map['lastSessionDate'] != null
          ? DateTime.parse(map['lastSessionDate'])
          : null,
    );
  }
}

/// نموذج جلسة المستخدم
class UserSession {
  final String id;
  final DateTime startTime;
  final DateTime? endTime;
  final List<AnalyticsEvent> events;

  UserSession({
    required this.id,
    required this.startTime,
    this.endTime,
    required this.events,
  });

  UserSession copyWith({
    DateTime? endTime,
    List<AnalyticsEvent>? events,
  }) {
    return UserSession(
      id: id,
      startTime: startTime,
      endTime: endTime ?? this.endTime,
      events: events ?? this.events,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'startTime': startTime.toIso8601String(),
      'endTime': endTime?.toIso8601String(),
      'events': events.map((e) => e.toMap()).toList(),
    };
  }

  factory UserSession.fromMap(Map<String, dynamic> map) {
    return UserSession(
      id: map['id'] ?? '',
      startTime: DateTime.parse(map['startTime']),
      endTime: map['endTime'] != null ? DateTime.parse(map['endTime']) : null,
      events: (map['events'] as List? ?? [])
          .map((e) => AnalyticsEvent.fromMap(e))
          .toList(),
    );
  }
}

/// نموذج حدث التحليلات
class AnalyticsEvent {
  final String name;
  final DateTime timestamp;
  final Map<String, dynamic> data;

  AnalyticsEvent({
    required this.name,
    required this.timestamp,
    required this.data,
  });

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'timestamp': timestamp.toIso8601String(),
      'data': data,
    };
  }

  factory AnalyticsEvent.fromMap(Map<String, dynamic> map) {
    return AnalyticsEvent(
      name: map['name'] ?? '',
      timestamp: DateTime.parse(map['timestamp']),
      data: Map<String, dynamic>.from(map['data'] ?? {}),
    );
  }
}

/// إحصائيات الاستخدام
class UsageStats {
  final int totalSessions;
  final int totalToolUsage;
  final int totalTokensUsed;
  final int totalSessionTimeMinutes;
  final int averageSessionTimeMinutes;
  final String mostUsedTool;
  final String mostUsedProvider;
  final Map<String, int> toolsUsage;
  final Map<String, int> providersUsage;
  final Map<String, int> tokensUsed;
  final DateTime? lastSessionDate;

  UsageStats({
    required this.totalSessions,
    required this.totalToolUsage,
    required this.totalTokensUsed,
    required this.totalSessionTimeMinutes,
    required this.averageSessionTimeMinutes,
    required this.mostUsedTool,
    required this.mostUsedProvider,
    required this.toolsUsage,
    required this.providersUsage,
    required this.tokensUsed,
    this.lastSessionDate,
  });

  factory UsageStats.empty() {
    return UsageStats(
      totalSessions: 0,
      totalToolUsage: 0,
      totalTokensUsed: 0,
      totalSessionTimeMinutes: 0,
      averageSessionTimeMinutes: 0,
      mostUsedTool: 'لا يوجد',
      mostUsedProvider: 'لا يوجد',
      toolsUsage: {},
      providersUsage: {},
      tokensUsed: {},
    );
  }
}
