import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_staggered_animations/flutter_staggered_animations.dart';
import '../core/providers/app_state_provider.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../widgets/enhanced_widgets.dart';
import '../widgets/animated_backgrounds.dart';
import 'enhanced_chat_screen.dart';
import 'enhanced_settings_screen.dart';

class EnhancedHomeScreen extends ConsumerStatefulWidget {
  const EnhancedHomeScreen({super.key});

  @override
  ConsumerState<EnhancedHomeScreen> createState() => _EnhancedHomeScreenState();
}

class _EnhancedHomeScreenState extends ConsumerState<EnhancedHomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _headerController;
  late Animation<double> _headerAnimation;
  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;
  int _selectedIndex = 0;

  final List<AIFunction> _aiFunctions = [
    AIFunction(
      id: 'chat',
      title: 'محادثة ذكية',
      subtitle: 'تحدث مع الذكاء الاصطناعي',
      icon: Icons.chat_bubble_outline,
      gradient: [AppColors.primaryPurple, AppColors.lightPurple],
      route: '/enhanced_chat',
    ),
    AIFunction(
      id: 'summarize',
      title: 'تلخيص النص',
      subtitle: 'لخص النصوص الطويلة بذكاء',
      icon: Icons.summarize,
      gradient: [AppColors.accentBlue, AppColors.accentIndigo],
      route: '/summarize_text',
    ),
    AIFunction(
      id: 'analyze',
      title: 'تحليل البيانات',
      subtitle: 'حلل البيانات واستخرج الأنماط',
      icon: Icons.analytics,
      gradient: [AppColors.accentGreen, Color(0xFF27AE60)],
      route: '/analyze_data',
    ),
    AIFunction(
      id: 'plan',
      title: 'إنشاء خطة',
      subtitle: 'خطط لأهدافك بذكاء',
      icon: Icons.calendar_today,
      gradient: [AppColors.accentOrange, Color(0xFFE67E22)],
      route: '/plan',
    ),
    AIFunction(
      id: 'write',
      title: 'مساعدة الكتابة',
      subtitle: 'اكتب محتوى إبداعي ومتميز',
      icon: Icons.edit,
      gradient: [AppColors.accentTeal, Color(0xFF17A2B8)],
      route: '/write_assist',
    ),
    AIFunction(
      id: 'image',
      title: 'إنشاء صورة',
      subtitle: 'أنشئ صور فنية بالذكاء الاصطناعي',
      icon: Icons.image,
      gradient: [AppColors.accentPink, Color(0xFFC44569)],
      route: '/create_image',
    ),
  ];

  @override
  void initState() {
    super.initState();
    _headerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _headerAnimation = CurvedAnimation(
      parent: _headerController,
      curve: Curves.easeOutCubic, // تغيير من easeOutBack لتجنب قيم أكبر من 1.0
    );

    // تهيئة النبضة المتكررة لزر المحادثة
    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 2000),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.05).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _headerController.forward();
    _pulseController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _headerController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final appState = ref.watch(appStateProvider);
    final settings = ref.watch(settingsProvider);

    return Scaffold(
      backgroundColor: AppColors.background,
      body: ParticleBackground(
        particleCount: 30,
        child: SafeArea(
          child: Column(
            children: [_buildHeader(), Expanded(child: _buildContent())],
          ),
        ),
      ),
      bottomNavigationBar: _buildBottomNavigation(),
      floatingActionButton: _buildFloatingActionButton(),
      floatingActionButtonLocation: FloatingActionButtonLocation.centerFloat,
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _getGreeting(),
                    style: AppTextStyles.heading1.copyWith(
                      fontSize: 28,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'كيف يمكنني مساعدتك اليوم؟',
                    style: AppTextStyles.body.copyWith(
                      color: AppColors.white.withValues(alpha: 0.8),
                    ),
                  ),
                ],
              ),
              GestureDetector(
                onTap:
                    () => Navigator.push(
                      context,
                      MaterialPageRoute(
                        builder: (context) => const EnhancedSettingsScreen(),
                      ),
                    ),
                child: Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: const LinearGradient(
                      colors: [AppColors.primaryPurple, AppColors.lightPurple],
                    ),
                    borderRadius: BorderRadius.circular(25),
                    boxShadow: [
                      BoxShadow(
                        color: AppColors.primaryPurple.withValues(alpha: 0.3),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: const Icon(
                    Icons.settings,
                    color: AppColors.white,
                    size: 24,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildQuickStats(),
        ],
      ),
    );
  }

  Widget _buildQuickStats() {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            title: 'المحادثات',
            value: '12',
            icon: Icons.chat,
            color: AppColors.accentBlue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            title: 'النصوص',
            value: '8',
            icon: Icons.description,
            color: AppColors.accentGreen,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            title: 'الخطط',
            value: '5',
            icon: Icons.calendar_today,
            color: AppColors.accentOrange,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return EnhancedCard(
      backgroundColor: AppColors.darkGrey.withOpacity(0.5),
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: AppTextStyles.heading2.copyWith(fontSize: 20, color: color),
          ),
          Text(
            title,
            style: AppTextStyles.caption.copyWith(
              color: AppColors.white.withOpacity(0.7),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.symmetric(horizontal: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الميزات الرئيسية',
            style: AppTextStyles.heading2.copyWith(
              color: AppColors.primaryPurple,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildFunctionsGrid(),
          const SizedBox(height: 24),
          _buildRecentActivity(),
          const SizedBox(height: 120), // Space for FAB and bottom nav
        ],
      ),
    );
  }

  Widget _buildFunctionsGrid() {
    return AnimationLimiter(
      child: GridView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
        ),
        itemCount: _aiFunctions.length,
        itemBuilder: (context, index) {
          return AnimationConfiguration.staggeredGrid(
            position: index,
            duration: const Duration(milliseconds: 500),
            columnCount: 2,
            child: SlideAnimation(
              verticalOffset: 50.0,
              child: FadeInAnimation(
                child: _buildFunctionCard(_aiFunctions[index]),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildFunctionCard(AIFunction function) {
    return EnhancedCard(
      gradientColors: function.gradient,
      enableHover: true,
      enableGlow: true,
      onTap: () => _navigateToFunction(function),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: AppColors.white.withOpacity(0.2),
              borderRadius: BorderRadius.circular(25),
            ),
            child: Icon(function.icon, size: 24, color: AppColors.white),
          ),
          const SizedBox(height: 8),
          Text(
            function.title,
            style: AppTextStyles.body.copyWith(
              fontWeight: FontWeight.bold,
              color: AppColors.white,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 2),
          Text(
            function.subtitle,
            style: AppTextStyles.caption.copyWith(
              color: AppColors.white.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivity() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'النشاط الأخير',
              style: AppTextStyles.heading2.copyWith(
                color: AppColors.primaryPurple,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () {},
              child: Text(
                'عرض الكل',
                style: AppTextStyles.body.copyWith(
                  color: AppColors.primaryPurple,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        _buildActivityItem(
          title: 'محادثة حول الذكاء الاصطناعي',
          subtitle: 'منذ ساعتين',
          icon: Icons.chat,
          color: AppColors.accentBlue,
        ),
        _buildActivityItem(
          title: 'تلخيص مقال تقني',
          subtitle: 'منذ 4 ساعات',
          icon: Icons.summarize,
          color: AppColors.accentGreen,
        ),
        _buildActivityItem(
          title: 'خطة مشروع جديد',
          subtitle: 'أمس',
          icon: Icons.calendar_today,
          color: AppColors.accentOrange,
        ),
      ],
    );
  }

  Widget _buildActivityItem({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    return EnhancedCard(
      backgroundColor: AppColors.darkGrey.withOpacity(0.5),
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withOpacity(0.2),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(icon, color: color, size: 20),
        ),
        title: Text(
          title,
          style: AppTextStyles.body.copyWith(color: AppColors.white),
        ),
        subtitle: Text(
          subtitle,
          style: AppTextStyles.caption.copyWith(
            color: AppColors.white.withOpacity(0.7),
          ),
        ),
        trailing: const Icon(
          Icons.arrow_forward_ios,
          color: AppColors.white,
          size: 16,
        ),
      ),
    );
  }

  Widget _buildBottomNavigation() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.darkGrey,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.only(bottom: 8.0),
          child: BottomNavigationBar(
            backgroundColor: Colors.transparent,
            elevation: 0,
            type: BottomNavigationBarType.fixed,
            currentIndex: _selectedIndex,
            onTap: _onBottomNavTap,
            selectedItemColor: AppColors.primaryPurple,
            unselectedItemColor: AppColors.white.withOpacity(0.6),
            items: const [
              BottomNavigationBarItem(
                icon: Icon(Icons.home),
                label: 'الرئيسية',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.history),
                label: 'السجل',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.favorite),
                label: 'المفضلة',
              ),
              BottomNavigationBarItem(
                icon: Icon(Icons.settings),
                label: 'الإعدادات',
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFloatingActionButton() {
    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _pulseAnimation.value,
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(30),
              gradient: LinearGradient(
                colors: [
                  AppColors.primaryPurple,
                  AppColors.primaryPurple.withValues(alpha: 0.8),
                ],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryPurple.withValues(alpha: 0.4),
                  blurRadius: 15 * _pulseAnimation.value,
                  offset: const Offset(0, 8),
                  spreadRadius: 2 * _pulseAnimation.value,
                ),
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(30),
                onTap: () {
                  // تأثير اهتزاز خفيف
                  HapticFeedback.lightImpact();
                  Navigator.push(
                    context,
                    PageRouteBuilder(
                      pageBuilder:
                          (context, animation, secondaryAnimation) =>
                              const EnhancedChatScreen(),
                      transitionsBuilder: (
                        context,
                        animation,
                        secondaryAnimation,
                        child,
                      ) {
                        const begin = Offset(0.0, 1.0);
                        const end = Offset.zero;
                        const curve = Curves.easeInOutCubic;
                        var tween = Tween(
                          begin: begin,
                          end: end,
                        ).chain(CurveTween(curve: curve));
                        return SlideTransition(
                          position: animation.drive(tween),
                          child: child,
                        );
                      },
                      transitionDuration: const Duration(milliseconds: 400),
                    ),
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 16,
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: AppColors.white.withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.chat_bubble_outline,
                          color: AppColors.white,
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        'محادثة جديدة',
                        style: AppTextStyles.button.copyWith(
                          color: AppColors.white,
                          fontWeight: FontWeight.w600,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: AppColors.white.withValues(alpha: 0.3),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: const Icon(
                          Icons.arrow_forward_ios,
                          color: AppColors.white,
                          size: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  String _getGreeting() {
    final hour = DateTime.now().hour;
    if (hour < 12) {
      return 'صباح الخير! 🌅';
    } else if (hour < 17) {
      return 'مساء الخير! ☀️';
    } else {
      return 'مساء الخير! 🌙';
    }
  }

  void _navigateToFunction(AIFunction function) {
    if (function.id == 'chat') {
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const EnhancedChatScreen()),
      );
    } else if (function.route != null) {
      Navigator.pushNamed(context, function.route!);
    }
  }

  void _onBottomNavTap(int index) {
    setState(() {
      _selectedIndex = index;
    });

    switch (index) {
      case 0:
        // الرئيسية - نحن بالفعل في الصفحة الرئيسية
        break;
      case 1:
        // السجل - عرض سجل المحادثات
        _showHistoryDialog();
        break;
      case 2:
        // المفضلة - عرض المحادثات المفضلة
        _showFavoritesDialog();
        break;
      case 3:
        // الإعدادات - الانتقال إلى صفحة الإعدادات
        Navigator.push(
          context,
          MaterialPageRoute(
            builder: (context) => const EnhancedSettingsScreen(),
          ),
        );
        break;
    }
  }

  void _showHistoryDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppColors.darkGrey,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Row(
              children: [
                Icon(Icons.history, color: AppColors.primaryPurple, size: 24),
                const SizedBox(width: 8),
                Text(
                  'سجل المحادثات',
                  style: AppTextStyles.heading2.copyWith(
                    color: AppColors.white,
                  ),
                ),
              ],
            ),
            content: SizedBox(
              width: double.maxFinite,
              height: 300,
              child: Column(
                children: [
                  Expanded(
                    child: ListView.builder(
                      itemCount: 5, // عدد وهمي للمحادثات
                      itemBuilder: (context, index) {
                        return Card(
                          color: AppColors.background,
                          margin: const EdgeInsets.only(bottom: 8),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: AppColors.primaryPurple,
                              child: Text('${index + 1}'),
                            ),
                            title: Text(
                              'محادثة ${index + 1}',
                              style: AppTextStyles.body.copyWith(
                                color: AppColors.white,
                              ),
                            ),
                            subtitle: Text(
                              'منذ ${index + 1} ساعة',
                              style: AppTextStyles.caption.copyWith(
                                color: AppColors.mutedText,
                              ),
                            ),
                            trailing: Icon(
                              Icons.arrow_forward_ios,
                              color: AppColors.primaryPurple,
                              size: 16,
                            ),
                            onTap: () {
                              Navigator.pop(context);
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) => const EnhancedChatScreen(),
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'إغلاق',
                  style: TextStyle(color: AppColors.primaryPurple),
                ),
              ),
            ],
          ),
    );
  }

  void _showFavoritesDialog() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppColors.darkGrey,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Row(
              children: [
                Icon(Icons.favorite, color: Colors.red, size: 24),
                const SizedBox(width: 8),
                Text(
                  'المحادثات المفضلة',
                  style: AppTextStyles.heading2.copyWith(
                    color: AppColors.white,
                  ),
                ),
              ],
            ),
            content: SizedBox(
              width: double.maxFinite,
              height: 300,
              child: Column(
                children: [
                  Expanded(
                    child: ListView.builder(
                      itemCount: 3, // عدد وهمي للمفضلة
                      itemBuilder: (context, index) {
                        return Card(
                          color: AppColors.background,
                          margin: const EdgeInsets.only(bottom: 8),
                          child: ListTile(
                            leading: CircleAvatar(
                              backgroundColor: Colors.red,
                              child: Icon(
                                Icons.favorite,
                                color: AppColors.white,
                                size: 16,
                              ),
                            ),
                            title: Text(
                              'محادثة مفضلة ${index + 1}',
                              style: AppTextStyles.body.copyWith(
                                color: AppColors.white,
                              ),
                            ),
                            subtitle: Text(
                              'محفوظة في المفضلة',
                              style: AppTextStyles.caption.copyWith(
                                color: AppColors.mutedText,
                              ),
                            ),
                            trailing: Icon(
                              Icons.arrow_forward_ios,
                              color: Colors.red,
                              size: 16,
                            ),
                            onTap: () {
                              Navigator.pop(context);
                              Navigator.push(
                                context,
                                MaterialPageRoute(
                                  builder:
                                      (context) => const EnhancedChatScreen(),
                                ),
                              );
                            },
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text('إغلاق', style: TextStyle(color: Colors.red)),
              ),
            ],
          ),
    );
  }
}

class AIFunction {
  final String id;
  final String title;
  final String subtitle;
  final IconData icon;
  final List<Color> gradient;
  final String? route;

  AIFunction({
    required this.id,
    required this.title,
    required this.subtitle,
    required this.icon,
    required this.gradient,
    this.route,
  });
}
