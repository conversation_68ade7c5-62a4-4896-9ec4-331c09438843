import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/payment/payment_service.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../widgets/enhanced_widgets.dart';

/// شاشة الاشتراكات والدفع
class SubscriptionScreen extends ConsumerStatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  ConsumerState<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends ConsumerState<SubscriptionScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  String? _selectedPlanId;
  PaymentMethod _selectedPaymentMethod = PaymentMethod.card;
  final _promoCodeController = TextEditingController();
  bool _isProcessing = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
    _initializePaymentService();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _promoCodeController.dispose();
    super.dispose();
  }

  Future<void> _initializePaymentService() async {
    await PaymentService.initialize();
    setState(() {});
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }

  Future<void> _processPayment() async {
    if (_selectedPlanId == null) {
      _showErrorSnackBar('يرجى اختيار خطة اشتراك');
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final plan = PaymentService.getSubscriptionPlan(_selectedPlanId!);
      final promoCode = _promoCodeController.text.trim();

      final result = await PaymentService.processPayment(
        planId: _selectedPlanId!,
        method: _selectedPaymentMethod,
        amount: plan.price,
        promoCode: promoCode.isNotEmpty ? promoCode : null,
      );

      if (result.success) {
        _showSuccessSnackBar('تم الاشتراك بنجاح!');
        Navigator.of(context).pop();
      } else {
        _showErrorSnackBar(result.message);
      }
    } catch (e) {
      _showErrorSnackBar('حدث خطأ أثناء معالجة الدفع');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.darkGrey,
        title: Text(
          'الاشتراكات والدفع',
          style: AppTextStyles.heading.copyWith(color: AppColors.white),
        ),
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primaryPurple,
          unselectedLabelColor: AppColors.lightGrey,
          indicatorColor: AppColors.primaryPurple,
          tabs: const [
            Tab(text: 'خطط الاشتراك', icon: Icon(Icons.card_membership)),
            Tab(text: 'اشتراكاتي', icon: Icon(Icons.history)),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildSubscriptionPlansTab(),
          _buildMySubscriptionsTab(),
        ],
      ),
    );
  }

  Widget _buildSubscriptionPlansTab() {
    final plans = PaymentService.getSubscriptionPlans();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'اختر الخطة المناسبة لك',
            style: AppTextStyles.heading.copyWith(
              color: AppColors.primaryPurple,
              fontSize: 24,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'جميع الخطط تشمل ضمان استرداد المال خلال 30 يوم',
            style: AppTextStyles.body.copyWith(color: AppColors.lightGrey),
          ),
          const SizedBox(height: 24),

          // خطط الاشتراك
          ...plans.map((plan) => _buildPlanCard(plan)),

          const SizedBox(height: 24),

          // كود الخصم
          _buildPromoCodeSection(),

          const SizedBox(height: 24),

          // طرق الدفع
          _buildPaymentMethodSection(),

          const SizedBox(height: 32),

          // زر الدفع
          EnhancedButton(
            text: _isProcessing ? 'جاري المعالجة...' : 'اشترك الآن',
            onPressed: _isProcessing ? null : _processPayment,
            isLoading: _isProcessing,
            size: ButtonSize.large,
          ),
        ],
      ),
    );
  }

  Widget _buildPlanCard(SubscriptionPlan plan) {
    final isSelected = _selectedPlanId == plan.id;
    final isPopular = plan.priority == PlanPriority.high;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Stack(
        children: [
          GestureDetector(
            onTap: () {
              setState(() {
                _selectedPlanId = plan.id;
              });
            },
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                color: AppColors.darkGrey,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isSelected 
                      ? AppColors.primaryPurple 
                      : AppColors.lightGrey.withOpacity(0.3),
                  width: isSelected ? 2 : 1,
                ),
                boxShadow: isSelected ? [
                  BoxShadow(
                    color: AppColors.primaryPurple.withOpacity(0.3),
                    blurRadius: 12,
                    offset: const Offset(0, 4),
                  ),
                ] : null,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          plan.name,
                          style: AppTextStyles.heading.copyWith(
                            color: AppColors.white,
                            fontSize: 18,
                          ),
                        ),
                      ),
                      if (isSelected)
                        Icon(
                          Icons.check_circle,
                          color: AppColors.primaryPurple,
                          size: 24,
                        ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    plan.description,
                    style: AppTextStyles.body.copyWith(
                      color: AppColors.lightGrey,
                    ),
                  ),
                  const SizedBox(height: 16),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '\$${plan.price.toStringAsFixed(2)}',
                        style: AppTextStyles.heading.copyWith(
                          color: AppColors.primaryPurple,
                          fontSize: 32,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        '/شهر',
                        style: AppTextStyles.body.copyWith(
                          color: AppColors.lightGrey,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                  Text(
                    'الميزات المتضمنة:',
                    style: AppTextStyles.body.copyWith(
                      color: AppColors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  ...plan.features.map((feature) => Padding(
                    padding: const EdgeInsets.symmetric(vertical: 2),
                    child: Row(
                      children: [
                        Icon(
                          Icons.check,
                          color: AppColors.success,
                          size: 16,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            feature,
                            style: AppTextStyles.body.copyWith(
                              color: AppColors.lightGrey,
                            ),
                          ),
                        ),
                      ],
                    ),
                  )),
                  const SizedBox(height: 12),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: AppColors.primaryPurple.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      '${plan.tokensLimit.toString()} رمز شهرياً',
                      style: AppTextStyles.caption.copyWith(
                        color: AppColors.primaryPurple,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
          if (isPopular)
            Positioned(
              top: -8,
              right: 16,
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                decoration: BoxDecoration(
                  color: AppColors.warning,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  'الأكثر شعبية',
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildPromoCodeSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'كود الخصم',
            style: AppTextStyles.body.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          TextField(
            controller: _promoCodeController,
            style: AppTextStyles.body.copyWith(color: AppColors.white),
            decoration: InputDecoration(
              hintText: 'أدخل كود الخصم',
              hintStyle: AppTextStyles.body.copyWith(color: AppColors.lightGrey),
              filled: true,
              fillColor: AppColors.background,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide.none,
              ),
              suffixIcon: Icon(
                Icons.local_offer,
                color: AppColors.primaryPurple,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'أكواد متاحة: WELCOME20, STUDENT50, FIRST10',
            style: AppTextStyles.caption.copyWith(
              color: AppColors.lightGrey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodSection() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'طريقة الدفع',
            style: AppTextStyles.body.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ...PaymentMethod.values.map((method) => _buildPaymentMethodTile(method)),
        ],
      ),
    );
  }

  Widget _buildPaymentMethodTile(PaymentMethod method) {
    final isSelected = _selectedPaymentMethod == method;
    
    return GestureDetector(
      onTap: () {
        setState(() {
          _selectedPaymentMethod = method;
        });
      },
      child: Container(
        margin: const EdgeInsets.only(bottom: 8),
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: isSelected 
              ? AppColors.primaryPurple.withOpacity(0.2)
              : AppColors.background,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: isSelected 
                ? AppColors.primaryPurple 
                : AppColors.lightGrey.withOpacity(0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(
              _getPaymentMethodIcon(method),
              color: isSelected ? AppColors.primaryPurple : AppColors.lightGrey,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                _getPaymentMethodName(method),
                style: AppTextStyles.body.copyWith(
                  color: isSelected ? AppColors.primaryPurple : AppColors.white,
                ),
              ),
            ),
            if (isSelected)
              Icon(
                Icons.check_circle,
                color: AppColors.primaryPurple,
                size: 20,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildMySubscriptionsTab() {
    final subscriptions = PaymentService.subscriptions;
    final paymentHistory = PaymentService.paymentHistory;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // الاشتراك النشط
          _buildActiveSubscriptionSection(),
          
          const SizedBox(height: 24),
          
          // سجل الاشتراكات
          if (subscriptions.isNotEmpty) ...[
            Text(
              'سجل الاشتراكات',
              style: AppTextStyles.heading.copyWith(
                color: AppColors.primaryPurple,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 16),
            ...subscriptions.map((subscription) => _buildSubscriptionHistoryCard(subscription)),
          ],
          
          const SizedBox(height: 24),
          
          // سجل الدفعات
          if (paymentHistory.isNotEmpty) ...[
            Text(
              'سجل الدفعات',
              style: AppTextStyles.heading.copyWith(
                color: AppColors.primaryPurple,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 16),
            ...paymentHistory.map((payment) => _buildPaymentHistoryCard(payment)),
          ],
          
          if (subscriptions.isEmpty && paymentHistory.isEmpty)
            _buildEmptyState(),
        ],
      ),
    );
  }

  Widget _buildActiveSubscriptionSection() {
    final activeSubscription = PaymentService.getActiveSubscription();
    
    if (activeSubscription == null) {
      return Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: AppColors.darkGrey,
          borderRadius: BorderRadius.circular(12),
        ),
        child: Column(
          children: [
            Icon(
              Icons.info_outline,
              color: AppColors.lightGrey,
              size: 48,
            ),
            const SizedBox(height: 16),
            Text(
              'لا يوجد اشتراك نشط',
              style: AppTextStyles.heading.copyWith(
                color: AppColors.lightGrey,
                fontSize: 18,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'اشترك في إحدى خططنا للاستفادة من جميع الميزات',
              style: AppTextStyles.body.copyWith(
                color: AppColors.lightGrey,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      );
    }

    final plan = PaymentService.getSubscriptionPlan(activeSubscription.planId);
    final daysLeft = activeSubscription.endDate.difference(DateTime.now()).inDays;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.success.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.check_circle,
                color: AppColors.success,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'الاشتراك النشط',
                style: AppTextStyles.heading.copyWith(
                  color: AppColors.success,
                  fontSize: 18,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            plan.name,
            style: AppTextStyles.heading.copyWith(
              color: AppColors.white,
              fontSize: 20,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ينتهي في $daysLeft يوم',
            style: AppTextStyles.body.copyWith(
              color: daysLeft <= 7 ? AppColors.warning : AppColors.lightGrey,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: EnhancedButton(
                  text: 'تجديد الاشتراك',
                  onPressed: () => _renewSubscription(activeSubscription.id),
                  size: ButtonSize.small,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: EnhancedButton(
                  text: 'إلغاء الاشتراك',
                  onPressed: () => _cancelSubscription(activeSubscription.id),
                  size: ButtonSize.small,
                  style: ButtonStyle.secondary,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSubscriptionHistoryCard(Subscription subscription) {
    final plan = PaymentService.getSubscriptionPlan(subscription.planId);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                plan.name,
                style: AppTextStyles.body.copyWith(
                  color: AppColors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              _buildStatusChip(subscription.status),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            'من ${_formatDate(subscription.startDate)} إلى ${_formatDate(subscription.endDate)}',
            style: AppTextStyles.caption.copyWith(
              color: AppColors.lightGrey,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPaymentHistoryCard(PaymentRecord payment) {
    final plan = PaymentService.getSubscriptionPlan(payment.planId);
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                plan.name,
                style: AppTextStyles.body.copyWith(
                  color: AppColors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '\$${payment.amount.toStringAsFixed(2)}',
                style: AppTextStyles.body.copyWith(
                  color: AppColors.primaryPurple,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                _formatDate(payment.timestamp),
                style: AppTextStyles.caption.copyWith(
                  color: AppColors.lightGrey,
                ),
              ),
              Row(
                children: [
                  Icon(
                    _getPaymentMethodIcon(payment.method),
                    color: AppColors.lightGrey,
                    size: 16,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    _getPaymentMethodName(payment.method),
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.lightGrey,
                    ),
                  ),
                ],
              ),
            ],
          ),
          if (payment.promoCode != null) ...[
            const SizedBox(height: 4),
            Text(
              'كود الخصم: ${payment.promoCode}',
              style: AppTextStyles.caption.copyWith(
                color: AppColors.success,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildStatusChip(SubscriptionStatus status) {
    Color color;
    String text;
    
    switch (status) {
      case SubscriptionStatus.active:
        color = AppColors.success;
        text = 'نشط';
        break;
      case SubscriptionStatus.expired:
        color = AppColors.warning;
        text = 'منتهي';
        break;
      case SubscriptionStatus.cancelled:
        color = AppColors.error;
        text = 'ملغي';
        break;
      case SubscriptionStatus.inactive:
        color = AppColors.lightGrey;
        text = 'غير نشط';
        break;
    }
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withOpacity(0.2),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Text(
        text,
        style: AppTextStyles.caption.copyWith(
          color: color,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        children: [
          Icon(
            Icons.history,
            size: 64,
            color: AppColors.lightGrey.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'لا يوجد سجل اشتراكات',
            style: AppTextStyles.heading.copyWith(
              color: AppColors.lightGrey,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'ابدأ بالاشتراك في إحدى خططنا',
            style: AppTextStyles.body.copyWith(
              color: AppColors.lightGrey,
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _renewSubscription(String subscriptionId) async {
    setState(() {
      _isProcessing = true;
    });

    try {
      final result = await PaymentService.renewSubscription(subscriptionId);
      if (result.success) {
        _showSuccessSnackBar('تم تجديد الاشتراك بنجاح');
        setState(() {});
      } else {
        _showErrorSnackBar(result.message);
      }
    } catch (e) {
      _showErrorSnackBar('فشل في تجديد الاشتراك');
    } finally {
      setState(() {
        _isProcessing = false;
      });
    }
  }

  Future<void> _cancelSubscription(String subscriptionId) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.darkGrey,
        title: Text(
          'إلغاء الاشتراك',
          style: AppTextStyles.heading.copyWith(color: AppColors.white),
        ),
        content: Text(
          'هل أنت متأكد من رغبتك في إلغاء الاشتراك؟',
          style: AppTextStyles.body.copyWith(color: AppColors.white),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              'لا',
              style: AppTextStyles.body.copyWith(color: AppColors.lightGrey),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(
              'نعم',
              style: AppTextStyles.body.copyWith(color: AppColors.error),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await PaymentService.cancelSubscription(subscriptionId);
      if (success) {
        _showSuccessSnackBar('تم إلغاء الاشتراك بنجاح');
        setState(() {});
      } else {
        _showErrorSnackBar('فشل في إلغاء الاشتراك');
      }
    }
  }

  IconData _getPaymentMethodIcon(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.card:
        return Icons.credit_card;
      case PaymentMethod.paypal:
        return Icons.account_balance_wallet;
      case PaymentMethod.applePay:
        return Icons.phone_iphone;
      case PaymentMethod.googlePay:
        return Icons.android;
      case PaymentMethod.bankTransfer:
        return Icons.account_balance;
    }
  }

  String _getPaymentMethodName(PaymentMethod method) {
    switch (method) {
      case PaymentMethod.card:
        return 'بطاقة ائتمان';
      case PaymentMethod.paypal:
        return 'PayPal';
      case PaymentMethod.applePay:
        return 'Apple Pay';
      case PaymentMethod.googlePay:
        return 'Google Pay';
      case PaymentMethod.bankTransfer:
        return 'تحويل بنكي';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
