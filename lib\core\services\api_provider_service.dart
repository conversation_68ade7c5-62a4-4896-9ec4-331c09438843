import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../models/api_provider_model.dart';
import '../security/api_key_manager.dart';

/// خدمة إدارة مقدمي خدمة API
class ApiProviderService {
  static final Dio _dio = Dio();
  static const String _providersKey = 'api_providers';
  static const String _modelsKey = 'available_models';

  static List<ApiProvider> _providers = [];
  static List<AvailableModel> _availableModels = [];

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    await _loadProviders();
    await _setupDefaultProviders();
    await _loadAvailableModels();
  }

  /// إعداد مقدمي الخدمة الافتراضيين
  static Future<void> _setupDefaultProviders() async {
    if (_providers.isEmpty) {
      final defaultProviders = [
        ApiProvider.create(
          name: 'openrouter',
          displayName: 'OpenRouter',
          baseUrl: 'https://openrouter.ai/api/v1',
          type: ApiProviderType.openrouter,
          description: 'وصول لنماذج متعددة عبر OpenRouter',
        ),
        ApiProvider.create(
          name: 'openai',
          displayName: 'OpenAI',
          baseUrl: 'https://api.openai.com/v1',
          type: ApiProviderType.openai,
          description: 'نماذج GPT الرسمية من OpenAI',
        ),
        ApiProvider.create(
          name: 'gemini',
          displayName: 'Google Gemini',
          baseUrl: 'https://generativelanguage.googleapis.com/v1beta',
          type: ApiProviderType.gemini,
          description: 'نماذج Gemini من Google',
        ),
        ApiProvider.create(
          name: 'anthropic',
          displayName: 'Anthropic Claude',
          baseUrl: 'https://api.anthropic.com/v1',
          type: ApiProviderType.anthropic,
          description: 'نماذج Claude من Anthropic',
        ),
        ApiProvider.create(
          name: 'huggingface',
          displayName: 'Hugging Face',
          baseUrl: 'https://api-inference.huggingface.co',
          type: ApiProviderType.huggingface,
          description: 'نماذج مجانية من Hugging Face',
        ),
      ];

      for (final provider in defaultProviders) {
        await addProvider(provider);
      }
    }
  }

  /// إضافة مقدم خدمة جديد
  static Future<void> addProvider(ApiProvider provider) async {
    _providers.removeWhere((p) => p.id == provider.id);
    _providers.add(provider);
    await _saveProviders();

    // اختبار المقدم وجلب النماذج
    if (provider.apiKey != null && provider.apiKey!.isNotEmpty) {
      await testProvider(provider.id);
      await fetchModelsForProvider(provider.id);
    }
  }

  /// تحديث مقدم خدمة
  static Future<void> updateProvider(ApiProvider provider) async {
    final index = _providers.indexWhere((p) => p.id == provider.id);
    if (index != -1) {
      _providers[index] = provider;
      await _saveProviders();

      // إعادة اختبار وجلب النماذج
      if (provider.apiKey != null && provider.apiKey!.isNotEmpty) {
        await testProvider(provider.id);
        await fetchModelsForProvider(provider.id);
      }
    }
  }

  /// حذف مقدم خدمة
  static Future<void> removeProvider(String providerId) async {
    _providers.removeWhere((p) => p.id == providerId);
    _availableModels.removeWhere((m) => m.providerId == providerId);
    await _saveProviders();
    await _saveAvailableModels();
  }

  /// اختبار مقدم خدمة
  static Future<bool> testProvider(String providerId) async {
    final provider = _providers.firstWhere((p) => p.id == providerId);

    try {
      final response = await _dio.get(
        '${provider.baseUrl}/models',
        options: Options(
          headers: {
            ...provider.headers,
            'Authorization': 'Bearer ${provider.apiKey}',
          },
          validateStatus: (status) => status! < 500,
        ),
      );

      final isWorking = response.statusCode == 200;

      // تحديث حالة المقدم
      final updatedProvider = provider.copyWith(
        isWorking: isWorking,
        lastTested: DateTime.now(),
      );

      await updateProvider(updatedProvider);
      return isWorking;
    } catch (e) {
      debugPrint('خطأ في اختبار المقدم $providerId: $e');

      final updatedProvider = provider.copyWith(
        isWorking: false,
        lastTested: DateTime.now(),
      );

      await updateProvider(updatedProvider);
      return false;
    }
  }

  /// جلب النماذج المتاحة لمقدم خدمة
  static Future<List<AvailableModel>> fetchModelsForProvider(
    String providerId,
  ) async {
    final provider = _providers.firstWhere((p) => p.id == providerId);

    try {
      String endpoint = '/models';
      Map<String, String> headers = {
        ...Map<String, String>.from(provider.headers),
        'Authorization': 'Bearer ${provider.apiKey}',
      };

      // تخصيص endpoint حسب نوع المقدم
      switch (provider.type) {
        case ApiProviderType.gemini:
          endpoint = '/models';
          headers.remove('Authorization');
          headers['x-goog-api-key'] = provider.apiKey!;
          break;
        case ApiProviderType.anthropic:
          // Anthropic لا يوفر endpoint للنماذج، نستخدم قائمة ثابتة
          return _getAnthropicModels(providerId);
        case ApiProviderType.huggingface:
          // Hugging Face نماذج مجانية
          return _getHuggingFaceModels(providerId);
        default:
          break;
      }

      final response = await _dio.get(
        '${provider.baseUrl}$endpoint',
        options: Options(headers: headers),
      );

      if (response.statusCode == 200) {
        final List<AvailableModel> models = [];
        final data = response.data;

        if (data['data'] != null) {
          for (final modelData in data['data']) {
            models.add(AvailableModel.fromJson(modelData, providerId));
          }
        }

        // إزالة النماذج القديمة لهذا المقدم
        _availableModels.removeWhere((m) => m.providerId == providerId);

        // إضافة النماذج الجديدة
        _availableModels.addAll(models);
        await _saveAvailableModels();

        // تحديث قائمة النماذج في المقدم
        final updatedProvider = provider.copyWith(
          supportedModels: models.map((m) => m.id).toList(),
        );
        await updateProvider(updatedProvider);

        return models;
      }
    } catch (e) {
      debugPrint('خطأ في جلب النماذج للمقدم $providerId: $e');
    }

    return [];
  }

  /// الحصول على نماذج Anthropic (قائمة ثابتة)
  static List<AvailableModel> _getAnthropicModels(String providerId) {
    return [
      AvailableModel(
        id: 'claude-3-opus-20240229',
        name: 'claude-3-opus-20240229',
        displayName: 'Claude 3 Opus',
        providerId: providerId,
        isActive: true,
        capabilities: {'text': true, 'vision': true},
        maxTokens: 200000,
      ),
      AvailableModel(
        id: 'claude-3-sonnet-20240229',
        name: 'claude-3-sonnet-20240229',
        displayName: 'Claude 3 Sonnet',
        providerId: providerId,
        isActive: true,
        capabilities: {'text': true, 'vision': true},
        maxTokens: 200000,
      ),
      AvailableModel(
        id: 'claude-3-haiku-20240307',
        name: 'claude-3-haiku-20240307',
        displayName: 'Claude 3 Haiku',
        providerId: providerId,
        isActive: true,
        capabilities: {'text': true, 'vision': true},
        maxTokens: 200000,
      ),
    ];
  }

  /// الحصول على نماذج Hugging Face المجانية
  static List<AvailableModel> _getHuggingFaceModels(String providerId) {
    return [
      AvailableModel(
        id: 'microsoft/DialoGPT-medium',
        name: 'microsoft/DialoGPT-medium',
        displayName: 'DialoGPT Medium (مجاني)',
        providerId: providerId,
        isActive: true,
        capabilities: {'text': true},
        maxTokens: 1024,
      ),
      AvailableModel(
        id: 'stabilityai/stable-diffusion-2-1',
        name: 'stabilityai/stable-diffusion-2-1',
        displayName: 'Stable Diffusion 2.1 (مجاني)',
        providerId: providerId,
        isActive: true,
        capabilities: {'image_generation': true},
        maxTokens: 77,
      ),
      AvailableModel(
        id: 'facebook/blenderbot-400M-distill',
        name: 'facebook/blenderbot-400M-distill',
        displayName: 'BlenderBot 400M (مجاني)',
        providerId: providerId,
        isActive: true,
        capabilities: {'text': true},
        maxTokens: 512,
      ),
      AvailableModel(
        id: 'google/flan-t5-large',
        name: 'google/flan-t5-large',
        displayName: 'FLAN-T5 Large (مجاني)',
        providerId: providerId,
        isActive: true,
        capabilities: {'text': true},
        maxTokens: 512,
      ),
    ];
  }

  /// الحصول على جميع مقدمي الخدمة
  static List<ApiProvider> getAllProviders() => List.from(_providers);

  /// الحصول على مقدمي الخدمة النشطين
  static List<ApiProvider> getActiveProviders() =>
      _providers.where((p) => p.isActive && p.isWorking).toList();

  /// الحصول على مقدم خدمة بالمعرف
  static ApiProvider? getProvider(String id) {
    try {
      return _providers.firstWhere((p) => p.id == id);
    } catch (e) {
      return null;
    }
  }

  /// الحصول على جميع النماذج المتاحة
  static List<AvailableModel> getAllModels() => List.from(_availableModels);

  /// الحصول على النماذج لمقدم خدمة محدد
  static List<AvailableModel> getModelsForProvider(String providerId) =>
      _availableModels.where((m) => m.providerId == providerId).toList();

  /// الحصول على النماذج النشطة
  static List<AvailableModel> getActiveModels() =>
      _availableModels.where((m) {
        final provider = getProvider(m.providerId);
        return provider != null && provider.isActive && provider.isWorking;
      }).toList();

  /// حفظ مقدمي الخدمة
  static Future<void> _saveProviders() async {
    final prefs = await SharedPreferences.getInstance();
    final providersJson = _providers.map((p) => p.toJson()).toList();
    await prefs.setString(_providersKey, jsonEncode(providersJson));
  }

  /// تحميل مقدمي الخدمة
  static Future<void> _loadProviders() async {
    final prefs = await SharedPreferences.getInstance();
    final providersString = prefs.getString(_providersKey);

    if (providersString != null) {
      final List<dynamic> providersJson = jsonDecode(providersString);
      _providers =
          providersJson.map((json) => ApiProvider.fromJson(json)).toList();
    }
  }

  /// حفظ النماذج المتاحة
  static Future<void> _saveAvailableModels() async {
    final prefs = await SharedPreferences.getInstance();
    final modelsJson = _availableModels.map((m) => m.toJson()).toList();
    await prefs.setString(_modelsKey, jsonEncode(modelsJson));
  }

  /// تحميل النماذج المتاحة
  static Future<void> _loadAvailableModels() async {
    final prefs = await SharedPreferences.getInstance();
    final modelsString = prefs.getString(_modelsKey);

    if (modelsString != null) {
      final List<dynamic> modelsJson = jsonDecode(modelsString);
      _availableModels =
          modelsJson
              .map((json) => AvailableModel.fromJson(json, json['providerId']))
              .toList();
    }
  }

  /// تحديث مفتاح API لمقدم خدمة
  static Future<void> updateApiKey(String providerId, String apiKey) async {
    final provider = getProvider(providerId);
    if (provider != null) {
      final updatedProvider = provider.copyWith(
        apiKey: apiKey,
        isActive: apiKey.isNotEmpty,
      );

      await updateProvider(updatedProvider);

      // حفظ في ApiKeyManager أيضاً
      switch (provider.type) {
        case ApiProviderType.openai:
          await ApiKeyManager.setOpenAIKey(apiKey);
          break;
        case ApiProviderType.openrouter:
          await ApiKeyManager.setOpenRouterKey(apiKey);
          break;
        case ApiProviderType.gemini:
          await ApiKeyManager.setGeminiKey(apiKey);
          break;
        default:
          break;
      }
    }
  }

  /// تحديث جميع النماذج
  static Future<void> refreshAllModels() async {
    for (final provider in getActiveProviders()) {
      await fetchModelsForProvider(provider.id);
    }
  }

  /// اختبار جميع مقدمي الخدمة
  static Future<void> testAllProviders() async {
    for (final provider in _providers) {
      if (provider.apiKey != null && provider.apiKey!.isNotEmpty) {
        await testProvider(provider.id);
      }
    }
  }
}
