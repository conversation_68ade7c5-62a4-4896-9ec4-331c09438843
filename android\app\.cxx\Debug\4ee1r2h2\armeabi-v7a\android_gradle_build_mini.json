{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\OneDrive\\Desktop\\deepseek_project\\android\\app\\.cxx\\Debug\\4ee1r2h2\\armeabi-v7a", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\OneDrive\\Desktop\\deepseek_project\\android\\app\\.cxx\\Debug\\4ee1r2h2\\armeabi-v7a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}