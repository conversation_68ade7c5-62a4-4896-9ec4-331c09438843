/// نماذج البيانات لدعم المنصات المتعددة

/// جهاز منصة
class PlatformDevice {
  final String id;
  final String name;
  final PlatformType platformType;
  final Map<String, dynamic> capabilities;
  final bool isConnected;
  final DateTime lastSeen;
  final DateTime connectedAt;

  PlatformDevice({
    required this.id,
    required this.name,
    required this.platformType,
    required this.capabilities,
    required this.isConnected,
    required this.lastSeen,
    required this.connectedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'platformType': platformType.toString(),
      'capabilities': capabilities,
      'isConnected': isConnected,
      'lastSeen': lastSeen.toIso8601String(),
      'connectedAt': connectedAt.toIso8601String(),
    };
  }

  factory PlatformDevice.fromMap(Map<String, dynamic> map) {
    return PlatformDevice(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      platformType: PlatformType.values.firstWhere(
        (e) => e.toString() == map['platformType'],
        orElse: () => PlatformType.mobile,
      ),
      capabilities: Map<String, dynamic>.from(map['capabilities'] ?? {}),
      isConnected: map['isConnected'] ?? false,
      lastSeen: DateTime.parse(map['lastSeen']),
      connectedAt: DateTime.parse(map['connectedAt']),
    );
  }
}

/// حالة المزامنة عبر المنصات
class PlatformSyncStatus {
  DateTime? lastSyncTime;
  List<String> syncedDevices;
  bool isAutoSyncEnabled;
  int syncIntervalMinutes;

  PlatformSyncStatus({
    this.lastSyncTime,
    this.syncedDevices = const [],
    this.isAutoSyncEnabled = true,
    this.syncIntervalMinutes = 10,
  });

  Map<String, dynamic> toMap() {
    return {
      'lastSyncTime': lastSyncTime?.toIso8601String(),
      'syncedDevices': syncedDevices,
      'isAutoSyncEnabled': isAutoSyncEnabled,
      'syncIntervalMinutes': syncIntervalMinutes,
    };
  }

  factory PlatformSyncStatus.fromMap(Map<String, dynamic> map) {
    return PlatformSyncStatus(
      lastSyncTime: map['lastSyncTime'] != null 
          ? DateTime.parse(map['lastSyncTime']) 
          : null,
      syncedDevices: List<String>.from(map['syncedDevices'] ?? []),
      isAutoSyncEnabled: map['isAutoSyncEnabled'] ?? true,
      syncIntervalMinutes: map['syncIntervalMinutes'] ?? 10,
    );
  }
}

/// نتيجة المزامنة عبر المنصات
class CrossPlatformSyncResult {
  final bool success;
  final int syncedDevicesCount;
  final int failedDevicesCount;
  final DateTime? syncTime;
  final String? error;

  CrossPlatformSyncResult({
    required this.success,
    this.syncedDevicesCount = 0,
    this.failedDevicesCount = 0,
    this.syncTime,
    this.error,
  });
}

/// معلومات المنصة
class PlatformInfo {
  final PlatformType type;
  final String version;
  final String deviceModel;
  final Map<String, dynamic> specifications;
  final List<PlatformFeature> supportedFeatures;

  PlatformInfo({
    required this.type,
    required this.version,
    required this.deviceModel,
    required this.specifications,
    required this.supportedFeatures,
  });

  Map<String, dynamic> toMap() {
    return {
      'type': type.toString(),
      'version': version,
      'deviceModel': deviceModel,
      'specifications': specifications,
      'supportedFeatures': supportedFeatures.map((f) => f.toString()).toList(),
    };
  }

  factory PlatformInfo.fromMap(Map<String, dynamic> map) {
    return PlatformInfo(
      type: PlatformType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => PlatformType.mobile,
      ),
      version: map['version'] ?? '',
      deviceModel: map['deviceModel'] ?? '',
      specifications: Map<String, dynamic>.from(map['specifications'] ?? {}),
      supportedFeatures: (map['supportedFeatures'] as List? ?? [])
          .map((f) => PlatformFeature.values.firstWhere(
                (e) => e.toString() == f,
                orElse: () => PlatformFeature.basic,
              ))
          .toList(),
    );
  }
}

/// إعدادات المنصة
class PlatformSettings {
  final bool enableCrossPlatformSync;
  final bool enableNotifications;
  final bool enableHapticFeedback;
  final bool enableLocationServices;
  final bool enableBiometrics;
  final Map<String, dynamic> customSettings;

  PlatformSettings({
    this.enableCrossPlatformSync = true,
    this.enableNotifications = true,
    this.enableHapticFeedback = true,
    this.enableLocationServices = false,
    this.enableBiometrics = false,
    this.customSettings = const {},
  });

  Map<String, dynamic> toMap() {
    return {
      'enableCrossPlatformSync': enableCrossPlatformSync,
      'enableNotifications': enableNotifications,
      'enableHapticFeedback': enableHapticFeedback,
      'enableLocationServices': enableLocationServices,
      'enableBiometrics': enableBiometrics,
      'customSettings': customSettings,
    };
  }

  factory PlatformSettings.fromMap(Map<String, dynamic> map) {
    return PlatformSettings(
      enableCrossPlatformSync: map['enableCrossPlatformSync'] ?? true,
      enableNotifications: map['enableNotifications'] ?? true,
      enableHapticFeedback: map['enableHapticFeedback'] ?? true,
      enableLocationServices: map['enableLocationServices'] ?? false,
      enableBiometrics: map['enableBiometrics'] ?? false,
      customSettings: Map<String, dynamic>.from(map['customSettings'] ?? {}),
    );
  }
}

/// رسالة عبر المنصات
class CrossPlatformMessage {
  final String id;
  final String fromDeviceId;
  final String toDeviceId;
  final String messageType;
  final Map<String, dynamic> payload;
  final DateTime sentAt;
  final bool isDelivered;

  CrossPlatformMessage({
    required this.id,
    required this.fromDeviceId,
    required this.toDeviceId,
    required this.messageType,
    required this.payload,
    required this.sentAt,
    this.isDelivered = false,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'fromDeviceId': fromDeviceId,
      'toDeviceId': toDeviceId,
      'messageType': messageType,
      'payload': payload,
      'sentAt': sentAt.toIso8601String(),
      'isDelivered': isDelivered,
    };
  }

  factory CrossPlatformMessage.fromMap(Map<String, dynamic> map) {
    return CrossPlatformMessage(
      id: map['id'] ?? '',
      fromDeviceId: map['fromDeviceId'] ?? '',
      toDeviceId: map['toDeviceId'] ?? '',
      messageType: map['messageType'] ?? '',
      payload: Map<String, dynamic>.from(map['payload'] ?? {}),
      sentAt: DateTime.parse(map['sentAt']),
      isDelivered: map['isDelivered'] ?? false,
    );
  }
}

/// أنواع المنصات
enum PlatformType {
  mobile,
  android,
  ios,
  web,
  windows,
  macos,
  linux,
  wearable,
  tv,
}

/// ميزات المنصة
enum PlatformFeature {
  basic,
  notifications,
  hapticFeedback,
  gestures,
  camera,
  microphone,
  location,
  biometrics,
  keyboardShortcuts,
  dragAndDrop,
  multiWindow,
  contextMenus,
  systemShortcuts,
  taskbarIntegration,
  fileSystem,
  clipboard,
  quickNotifications,
  quickResponses,
  healthMonitoring,
  remoteControl,
  largeScreenUI,
  voiceControl,
}

/// حالة الاتصال
enum ConnectionStatus {
  connected,
  disconnected,
  connecting,
  error,
}

/// نوع المزامنة
enum SyncType {
  manual,
  automatic,
  realtime,
  scheduled,
}
