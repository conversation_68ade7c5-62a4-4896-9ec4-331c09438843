{"dart.flutterSdkPath": "", "dart.previewFlutterUiGuides": true, "dart.previewFlutterUiGuidesCustomTracking": true, "dart.flutterHotReloadOnSave": "always", "dart.flutterCreateAndroidLanguage": "kotlin", "dart.flutterCreateIOSLanguage": "swift", "dart.flutterCreatePlatforms": ["android", "ios", "web"], "dart.debugExternalPackageLibraries": false, "dart.debugSdkLibraries": false, "editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit"}, "files.associations": {"*.dart": "dart"}, "emmet.includeLanguages": {"dart": "html"}}