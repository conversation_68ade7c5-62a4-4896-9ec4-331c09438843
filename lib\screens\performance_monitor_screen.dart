import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/app_colors.dart';
import '../widgets/modern_ui_components.dart';
import '../core/performance/performance_optimizer.dart';
import '../core/performance/memory_manager.dart';
import '../core/performance/performance_models.dart';

/// شاشة مراقب الأداء
class PerformanceMonitorScreen extends ConsumerStatefulWidget {
  const PerformanceMonitorScreen({super.key});

  @override
  ConsumerState<PerformanceMonitorScreen> createState() => _PerformanceMonitorScreenState();
}

class _PerformanceMonitorScreenState extends ConsumerState<PerformanceMonitorScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;
  PerformanceReport? _currentReport;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _initializeServices();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    setState(() => _isLoading = true);
    try {
      await PerformanceOptimizer.initialize();
      await MemoryManager.initialize();
      await _measureCurrentPerformance();
    } catch (e) {
      debugPrint('خطأ في تهيئة مراقب الأداء: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _measureCurrentPerformance() async {
    try {
      _currentReport = await PerformanceOptimizer.measurePerformance();
      setState(() {});
    } catch (e) {
      debugPrint('فشل في قياس الأداء: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.darkGrey,
        title: Text(
          'مراقب الأداء',
          style: TextStyle(color: AppColors.white),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.refresh, color: AppColors.electricBlue),
            onPressed: _refreshPerformance,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primaryPurple,
          unselectedLabelColor: AppColors.lightGrey,
          indicatorColor: AppColors.primaryPurple,
          tabs: const [
            Tab(text: 'نظرة عامة', icon: Icon(Icons.dashboard)),
            Tab(text: 'الذاكرة', icon: Icon(Icons.memory)),
            Tab(text: 'التحسينات', icon: Icon(Icons.tune)),
            Tab(text: 'التشخيص', icon: Icon(Icons.bug_report)),
          ],
        ),
      ),
      body: _isLoading
          ? Center(child: ModernUIComponents.modernLoadingIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildOverviewTab(),
                _buildMemoryTab(),
                _buildOptimizationsTab(),
                _buildDiagnosticsTab(),
              ],
            ),
    );
  }

  Widget _buildOverviewTab() {
    if (_currentReport == null) {
      return Center(
        child: ModernUIComponents.modernButton(
          text: 'قياس الأداء',
          onPressed: _measureCurrentPerformance,
          icon: Icons.speed,
        ),
      );
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildPerformanceScoreCard(_currentReport!),
          const SizedBox(height: 24),
          _buildPerformanceMetrics(_currentReport!),
          const SizedBox(height: 24),
          _buildQuickActions(),
        ],
      ),
    );
  }

  Widget _buildPerformanceScoreCard(PerformanceReport report) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: _getScoreGradient(report.overallScore),
            ),
            child: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    '${report.overallScore.toInt()}',
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 32,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  Text(
                    'النتيجة الإجمالية',
                    style: TextStyle(
                      color: AppColors.white,
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),
          Text(
            report.gradeDescription,
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'آخر قياس: ${_formatTime(report.timestamp)}',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPerformanceMetrics(PerformanceReport report) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: _buildMetricCard(
                'الذاكرة',
                '${report.memoryUsage.toInt()} MB',
                Icons.memory,
                AppColors.primaryPurple,
                report.memoryUsage / 100,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildMetricCard(
                'معدل الإطارات',
                '${report.frameRate.toInt()} FPS',
                Icons.speed,
                AppColors.electricBlue,
                report.frameRate / 60,
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        Row(
          children: [
            Expanded(
              child: _buildMetricCard(
                'وقت الاستجابة',
                '${report.responseTime.toInt()} ms',
                Icons.timer,
                AppColors.glowPink,
                (200 - report.responseTime) / 200,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildMetricCard(
                'البطارية',
                '${report.batteryUsage.toStringAsFixed(1)}%',
                Icons.battery_full,
                AppColors.warning,
                (10 - report.batteryUsage) / 10,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildMetricCard(String title, String value, IconData icon, Color color, double progress) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: AppColors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(
            value: progress.clamp(0.0, 1.0),
            backgroundColor: AppColors.darkGrey,
            valueColor: AlwaysStoppedAnimation<Color>(color),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إجراءات سريعة',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ModernUIComponents.modernButton(
                  text: 'تحسين شامل',
                  onPressed: _runFullOptimization,
                  icon: Icons.tune,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ModernUIComponents.modernButton(
                  text: 'تنظيف الذاكرة',
                  onPressed: _cleanupMemory,
                  icon: Icons.cleaning_services,
                  gradient: LinearGradient(
                    colors: [AppColors.electricBlue, AppColors.lightPurple],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildMemoryTab() {
    final memoryStats = MemoryManager.getMemoryStats();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildMemoryStatsCard(memoryStats),
          const SizedBox(height: 24),
          _buildMemoryActionsCard(),
          const SizedBox(height: 24),
          _buildCacheInfoCard(),
        ],
      ),
    );
  }

  Widget _buildMemoryStatsCard(MemoryStats stats) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائيات الذاكرة',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          _buildMemoryStatItem(
            'استهلاك الذاكرة المقدر',
            '${stats.estimatedMemoryUsageMB.toStringAsFixed(1)} MB',
            Icons.memory,
            AppColors.primaryPurple,
          ),
          const SizedBox(height: 12),
          _buildMemoryStatItem(
            'عناصر التخزين المؤقت',
            '${stats.totalCacheEntries}',
            Icons.storage,
            AppColors.electricBlue,
          ),
          const SizedBox(height: 12),
          _buildMemoryStatItem(
            'معدل نجاح التخزين المؤقت',
            '${stats.cacheHitRate.toStringAsFixed(1)}%',
            Icons.trending_up,
            AppColors.success,
          ),
          if (stats.lastCleanup != null) ...[
            const SizedBox(height: 12),
            _buildMemoryStatItem(
              'آخر تنظيف',
              _formatTime(stats.lastCleanup!),
              Icons.cleaning_services,
              AppColors.warning,
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildMemoryStatItem(String title, String value, IconData icon, Color color) {
    return Row(
      children: [
        Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: color, size: 16),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 12,
                ),
              ),
              Text(
                value,
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildMemoryActionsCard() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إدارة الذاكرة',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ModernUIComponents.modernButton(
            text: 'تنظيف التخزين المؤقت',
            onPressed: _cleanupCache,
            icon: Icons.cleaning_services,
            width: double.infinity,
          ),
          const SizedBox(height: 12),
          ModernUIComponents.modernButton(
            text: 'مسح جميع البيانات المؤقتة',
            onPressed: _clearAllCache,
            icon: Icons.delete_sweep,
            width: double.infinity,
            gradient: LinearGradient(
              colors: [AppColors.error, AppColors.warning],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCacheInfoCard() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'معلومات التخزين المؤقت',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'التخزين المؤقت يساعد في تسريع التطبيق عبر حفظ البيانات المستخدمة بكثرة في الذاكرة.',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 12),
          Text(
            '• يتم تنظيف البيانات المنتهية الصلاحية تلقائياً\n'
            '• البيانات الأقل استخداماً يتم إزالتها عند الحاجة\n'
            '• يمكن مسح التخزين المؤقت يدوياً في أي وقت',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptimizationsTab() {
    final recommendations = PerformanceOptimizer.getOptimizationRecommendations();
    final history = PerformanceOptimizer.optimizationHistory.take(10).toList();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildRecommendationsCard(recommendations),
          const SizedBox(height: 24),
          _buildOptimizationHistoryCard(history),
        ],
      ),
    );
  }

  Widget _buildRecommendationsCard(List<OptimizationRecommendation> recommendations) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'توصيات التحسين',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          if (recommendations.isEmpty)
            Text(
              'لا توجد توصيات حالياً - الأداء جيد!',
              style: TextStyle(
                color: AppColors.success,
                fontSize: 14,
              ),
            )
          else
            ...recommendations.map((rec) => _buildRecommendationItem(rec)),
        ],
      ),
    );
  }

  Widget _buildRecommendationItem(OptimizationRecommendation recommendation) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: _getPriorityColor(recommendation.priority).withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: _getPriorityColor(recommendation.priority).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _getOptimizationIcon(recommendation.type),
                color: _getPriorityColor(recommendation.priority),
                size: 20,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  recommendation.title,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getPriorityColor(recommendation.priority),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  _getPriorityText(recommendation.priority),
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            recommendation.description,
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 12,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'تحسين متوقع: ${recommendation.estimatedImprovement.toInt()}%',
            style: TextStyle(
              color: AppColors.success,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOptimizationHistoryCard(List<OptimizationAction> history) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'تاريخ التحسينات',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          if (history.isEmpty)
            Text(
              'لم يتم تشغيل أي تحسينات بعد',
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 14,
              ),
            )
          else
            ...history.map((action) => _buildHistoryItem(action)),
        ],
      ),
    );
  }

  Widget _buildHistoryItem(OptimizationAction action) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Icon(
            _getOptimizationIcon(action.type),
            color: action.result.success ? AppColors.success : AppColors.error,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  action.result.description,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  _formatTime(action.timestamp),
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
          if (action.result.success)
            Text(
              '+${action.result.improvementPercentage.toInt()}%',
              style: TextStyle(
                color: AppColors.success,
                fontSize: 12,
                fontWeight: FontWeight.bold,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDiagnosticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          ModernUIComponents.glassCard(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'أدوات التشخيص',
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  'قريباً: أدوات تشخيص متقدمة لتحليل الأداء وإيجاد المشاكل',
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  Gradient _getScoreGradient(double score) {
    if (score >= 90) {
      return LinearGradient(colors: [AppColors.success, AppColors.electricBlue]);
    } else if (score >= 70) {
      return LinearGradient(colors: [AppColors.warning, AppColors.primaryPurple]);
    } else {
      return LinearGradient(colors: [AppColors.error, AppColors.glowPink]);
    }
  }

  Color _getPriorityColor(RecommendationPriority priority) {
    switch (priority) {
      case RecommendationPriority.low:
        return AppColors.info;
      case RecommendationPriority.medium:
        return AppColors.warning;
      case RecommendationPriority.high:
        return AppColors.error;
      case RecommendationPriority.critical:
        return AppColors.glowPink;
    }
  }

  String _getPriorityText(RecommendationPriority priority) {
    switch (priority) {
      case RecommendationPriority.low:
        return 'منخفض';
      case RecommendationPriority.medium:
        return 'متوسط';
      case RecommendationPriority.high:
        return 'عالي';
      case RecommendationPriority.critical:
        return 'حرج';
    }
  }

  IconData _getOptimizationIcon(OptimizationType type) {
    switch (type) {
      case OptimizationType.memory:
        return Icons.memory;
      case OptimizationType.rendering:
        return Icons.brush;
      case OptimizationType.network:
        return Icons.wifi;
      case OptimizationType.storage:
        return Icons.storage;
      case OptimizationType.battery:
        return Icons.battery_full;
      case OptimizationType.responseTime:
        return Icons.speed;
    }
  }

  String _formatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);
    
    if (difference.inMinutes < 1) {
      return 'منذ قليل';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  // Action methods
  Future<void> _refreshPerformance() async {
    setState(() => _isLoading = true);
    try {
      await _measureCurrentPerformance();
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _runFullOptimization() async {
    setState(() => _isLoading = true);
    try {
      await PerformanceOptimizer.runFullOptimization();
      await _measureCurrentPerformance();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تشغيل التحسين الشامل بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _cleanupMemory() async {
    try {
      await MemoryManager.optimizeMemory();
      await _measureCurrentPerformance();
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تنظيف الذاكرة بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل في تنظيف الذاكرة'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _cleanupCache() async {
    try {
      await MemoryManager.cleanupCache();
      setState(() {});
      
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم تنظيف التخزين المؤقت'),
          backgroundColor: AppColors.success,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('فشل في تنظيف التخزين المؤقت'),
          backgroundColor: AppColors.error,
        ),
      );
    }
  }

  Future<void> _clearAllCache() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.darkGrey,
        title: Text('تأكيد المسح', style: TextStyle(color: AppColors.white)),
        content: Text(
          'هل أنت متأكد من مسح جميع البيانات المؤقتة؟',
          style: TextStyle(color: AppColors.textSecondary),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text('إلغاء', style: TextStyle(color: AppColors.textSecondary)),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: Text('مسح', style: TextStyle(color: AppColors.error)),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        await MemoryManager.clearAllCache();
        setState(() {});
        
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('تم مسح جميع البيانات المؤقتة'),
            backgroundColor: AppColors.success,
          ),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('فشل في مسح البيانات'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }
}
