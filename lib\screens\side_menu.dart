import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../utils/app_animations.dart';

class SideMenu extends StatelessWidget {
  const SideMenu({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: Colors.transparent,
      child: GlassContainer(
        blur: 18,
        opacity: 0.13,
        borderRadius: BorderRadius.zero,
        gradient: LinearGradient(
          colors: [
            AppColors.darkPurple.withOpacity(0.85),
            AppColors.primaryPurple.withOpacity(0.65),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        child: Safe<PERSON>rea(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 32),
              Center(
                child: GlassContainer(
                  blur: 16,
                  opacity: 0.22,
                  borderRadius: BorderRadius.circular(40),
                  gradient: LinearGradient(
                    colors: [
                      AppColors.primaryPurple.withOpacity(0.25),
                      AppColors.lightPurple.withOpacity(0.18),
                    ],
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                  ),
                  child: CircleAvatar(
                    radius: 32,
                    backgroundColor: Colors.transparent,
                    child: Icon(Icons.person, size: 40, color: Colors.white),
                  ),
                ),
              ),
              const SizedBox(height: 16),
              Center(child: Text('User Name', style: AppTextStyles.heading2)),
              const SizedBox(height: 32),
              ...[ // Improved ListTiles with glass effect
                _buildMenuTile(Icons.history, 'History'),
                _buildMenuTile(Icons.person, 'Profile'),
                _buildMenuTile(Icons.settings, 'Settings'),
                const Spacer(),
                _buildMenuTile(Icons.logout, 'Sign Out', isLogout: true),
                const SizedBox(height: 16),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMenuTile(IconData icon, String label, {bool isLogout = false}) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
      child: GlassContainer(
        blur: 10,
        opacity: isLogout ? 0.10 : 0.16,
        borderRadius: BorderRadius.circular(18),
        gradient: LinearGradient(
          colors: isLogout
              ? [AppColors.accentRed.withOpacity(0.18), AppColors.darkPurple.withOpacity(0.12)]
              : [AppColors.primaryPurple.withOpacity(0.13), AppColors.darkPurple.withOpacity(0.10)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        child: ListTile(
          leading: Icon(icon, color: isLogout ? AppColors.accentRed : Colors.white),
          title: Text(label, style: AppTextStyles.body.copyWith(
            color: isLogout ? AppColors.accentRed : Colors.white,
            fontWeight: isLogout ? FontWeight.bold : FontWeight.normal,
          )),
          onTap: () {},
        ),
      ),
    );
  }
}
