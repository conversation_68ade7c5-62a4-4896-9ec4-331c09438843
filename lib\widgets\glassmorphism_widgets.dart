import 'package:flutter/material.dart';
import 'dart:ui';
import '../utils/app_colors.dart';

/// مكونات Glassmorphism الحديثة المستلهمة من التصميم الجديد
class GlassmorphismWidgets {
  /// بطاقة زجاجية مع تأثيرات حديثة
  static Widget glassCard({
    required Widget child,
    double? width,
    double? height,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    BorderRadius? borderRadius,
    Gradient? gradient,
    double blur = 10.0,
    double opacity = 0.1,
    List<BoxShadow>? shadows,
  }) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      child: ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.circular(20),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
          child: Container(
            padding: padding ?? const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: gradient ?? AppColors.glassGradient,
              borderRadius: borderRadius ?? BorderRadius.circular(20),
              border: Border.all(color: AppColors.glassBorder, width: 1),
              boxShadow:
                  shadows ??
                  [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
            ),
            child: child,
          ),
        ),
      ),
    );
  }

  /// زر زجاجي مع تأثيرات تفاعلية
  static Widget glassButton({
    required String text,
    required VoidCallback? onPressed,
    double? width,
    double? height,
    EdgeInsetsGeometry? padding,
    BorderRadius? borderRadius,
    Gradient? gradient,
    TextStyle? textStyle,
    IconData? icon,
    bool isLoading = false,
  }) {
    return Container(
      width: width,
      height: height ?? 56,
      child: ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: isLoading ? null : onPressed,
              child: Container(
                padding:
                    padding ??
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
                decoration: BoxDecoration(
                  gradient: gradient ?? AppColors.primaryGradient,
                  borderRadius: borderRadius ?? BorderRadius.circular(16),
                  border: Border.all(color: AppColors.glassBorder, width: 1),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primaryPurple.withValues(alpha: 0.3),
                      blurRadius: 20,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (isLoading)
                      const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColors.white,
                          ),
                        ),
                      )
                    else if (icon != null) ...[
                      Icon(icon, color: AppColors.white, size: 20),
                      const SizedBox(width: 8),
                    ],
                    if (!isLoading)
                      Text(
                        text,
                        style:
                            textStyle ??
                            const TextStyle(
                              color: AppColors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// حاوية زجاجية للمحتوى
  static Widget glassContainer({
    required Widget child,
    double? width,
    double? height,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    BorderRadius? borderRadius,
    Color? backgroundColor,
    double blur = 10.0,
    double opacity = 0.1,
  }) {
    return Container(
      width: width,
      height: height,
      margin: margin,
      child: ClipRRect(
        borderRadius: borderRadius ?? BorderRadius.circular(16),
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
          child: Container(
            padding: padding ?? const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: backgroundColor ?? AppColors.glassBackground,
              borderRadius: borderRadius ?? BorderRadius.circular(16),
              border: Border.all(color: AppColors.glassBorder, width: 1),
            ),
            child: child,
          ),
        ),
      ),
    );
  }

  /// شريط تطبيق زجاجي
  static PreferredSizeWidget glassAppBar({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = true,
    double elevation = 0,
    Color? backgroundColor,
    TextStyle? titleStyle,
  }) {
    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: ClipRRect(
        child: BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
          child: AppBar(
            title: Text(
              title,
              style:
                  titleStyle ??
                  const TextStyle(
                    color: AppColors.white,
                    fontSize: 20,
                    fontWeight: FontWeight.w600,
                  ),
            ),
            centerTitle: centerTitle,
            backgroundColor: backgroundColor ?? AppColors.glassBackground,
            elevation: elevation,
            leading: leading,
            actions: actions,
            flexibleSpace: Container(
              decoration: BoxDecoration(
                gradient: AppColors.glassGradient,
                border: Border(
                  bottom: BorderSide(color: AppColors.glassBorder, width: 1),
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// بطاقة متحركة مع تأثيرات
  static Widget animatedGlassCard({
    required Widget child,
    required AnimationController controller,
    double? width,
    double? height,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    BorderRadius? borderRadius,
    Gradient? gradient,
    Duration? duration,
  }) {
    return AnimatedBuilder(
      animation: controller,
      builder: (context, _) {
        return Transform.scale(
          scale: 0.95 + (controller.value * 0.05),
          child: Opacity(
            opacity: controller.value,
            child: glassCard(
              width: width,
              height: height,
              padding: padding,
              margin: margin,
              borderRadius: borderRadius,
              gradient: gradient,
              child: child,
            ),
          ),
        );
      },
    );
  }

  /// تأثير الضوء المتحرك في الخلفية
  static Widget animatedBackground({
    required Widget child,
    List<Color>? colors,
    Duration duration = const Duration(seconds: 8),
  }) {
    return AnimatedContainer(
      duration: duration,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors:
              colors ??
              [AppColors.background, AppColors.darkGrey, AppColors.background],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          stops: const [0.0, 0.5, 1.0],
        ),
      ),
      child: child,
    );
  }

  /// تأثير الجسيمات المتحركة
  static Widget particleBackground({
    required Widget child,
    int particleCount = 50,
    Color particleColor = AppColors.primaryPurple,
  }) {
    return Stack(
      children: [
        // خلفية متدرجة
        Container(
          decoration: const BoxDecoration(
            gradient: AppColors.backgroundGradient,
          ),
        ),
        // الجسيمات المتحركة (يمكن تطويرها لاحقاً)
        child,
      ],
    );
  }
}

/// مكون خاص للتأثيرات الزجاجية المتقدمة
class AdvancedGlassEffect extends StatelessWidget {
  final Widget child;
  final double blur;
  final Color tintColor;
  final double tintOpacity;
  final BorderRadius? borderRadius;
  final Border? border;

  const AdvancedGlassEffect({
    Key? key,
    required this.child,
    this.blur = 10.0,
    this.tintColor = Colors.white,
    this.tintOpacity = 0.1,
    this.borderRadius,
    this.border,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.circular(16),
      child: BackdropFilter(
        filter: ImageFilter.blur(sigmaX: blur, sigmaY: blur),
        child: Container(
          decoration: BoxDecoration(
            color: tintColor.withValues(alpha: tintOpacity),
            borderRadius: borderRadius ?? BorderRadius.circular(16),
            border:
                border ??
                Border.all(color: Colors.white.withValues(alpha: 0.2), width: 1),
          ),
          child: child,
        ),
      ),
    );
  }
}
