# 🚀 نظام APIs الموحد - DeepSeek AI

## 📋 نظرة عامة

تم تطوير نظام APIs موحد ومتقدم يوفر **بوابة واحدة** لجميع مقدمي خدمات الذكاء الاصطناعي مع إمكانيات متقدمة لإدارة النماذج والتبديل السلس بينها.

## ✨ المميزات الرئيسية

### 🔗 بوابة API موحدة
- **نقطة دخول واحدة** لجميع مقدمي الخدمة
- **تبديل سلس** بين APIs دون تأثير على التطبيق
- **إدارة تلقائية** للمفاتيح والتوثيق

### 📋 اكتشاف النماذج التلقائي
- **جلب تلقائي** للنماذج المتاحة من كل API
- **عرض تفصيلي** لقدرات كل نموذج
- **تحديث فوري** عند إضافة APIs جديدة

### ⚙️ إدارة متقدمة
- **واجهة شاملة** لإدارة مقدمي الخدمة
- **اختبار الاتصال** التلقائي
- **إحصائيات مفصلة** للاستخدام

### 🔄 مرونة عالية
- **دعم متعدد المقدمين**: OpenAI, OpenRouter, Gemini, Anthropic
- **تبديل فوري** بين النماذج
- **استمرارية الخدمة** حتى عند تعطل مقدم واحد

## 🏗️ البنية التقنية

### الملفات الأساسية

```
lib/core/services/
├── unified_api_gateway.dart          # البوابة الموحدة الرئيسية
├── api_provider_service.dart         # خدمة إدارة مقدمي الخدمة
└── enhanced_ai_service.dart          # خدمة الذكاء الاصطناعي المحسنة

lib/screens/
└── unified_api_management_screen.dart # شاشة إدارة APIs

lib/widgets/
└── model_selector_widget.dart        # ويدجت اختيار النموذج

lib/examples/
└── unified_api_example.dart          # مثال شامل للاستخدام
```

## 🚀 كيفية الاستخدام

### 1. التهيئة الأساسية

```dart
import '../core/services/unified_api_gateway.dart';

// تهيئة النظام
await UnifiedApiGateway.initialize();
```

### 2. إرسال رسالة دردشة

```dart
final response = await UnifiedApiGateway.sendChatRequest(
  message: 'مرحبا، كيف يمكنني مساعدتك؟',
  temperature: 0.7,
  maxTokens: 1000,
);

print(response['content']); // النص المُولد
print(response['model']);   // النموذج المستخدم
print(response['usage']);   // إحصائيات الاستخدام
```

### 3. إنشاء صورة

```dart
final imageUrl = await UnifiedApiGateway.sendImageGenerationRequest(
  prompt: 'منظر طبيعي جميل مع جبال وبحيرة',
  size: '1024x1024',
  quality: 'standard',
);
```

### 4. تغيير المقدم والنموذج

```dart
// تعيين مقدم ونموذج جديد
await UnifiedApiGateway.setActiveProvider(
  'openai',
  modelId: 'gpt-4',
);

// الحصول على المقدم النشط
final activeProvider = UnifiedApiGateway.getActiveProvider();
final activeModel = UnifiedApiGateway.getActiveModel();
```

### 5. إدارة النماذج

```dart
// الحصول على جميع النماذج المتاحة
final models = UnifiedApiGateway.getAvailableModels();

// تحديث النماذج لجميع المقدمين
await UnifiedApiGateway.refreshAllModels();

// الحصول على إحصائيات النظام
final stats = UnifiedApiGateway.getUsageStats();
```

## 🎨 واجهة المستخدم

### شاشة إدارة APIs الموحدة

```dart
Navigator.pushNamed(context, '/unified_api_management');
```

**المميزات:**
- 📊 **3 تبويبات**: المقدمين، النماذج، الإحصائيات
- 🔄 **تحديث فوري** للنماذج
- ✅ **اختبار الاتصال** التلقائي
- 📈 **إحصائيات مفصلة**

### ويدجت اختيار النموذج

```dart
ModelSelectorWidget(
  compactMode: true, // وضع مضغوط
  showProviderInfo: true, // عرض معلومات المقدم
  onModelSelected: (providerId, modelId) {
    // تم اختيار نموذج جديد
  },
)
```

## 🔧 إضافة مقدم خدمة جديد

### 1. إنشاء مقدم جديد

```dart
final newProvider = ApiProvider.create(
  name: 'custom_ai',
  displayName: 'Custom AI Service',
  baseUrl: 'https://api.custom-ai.com/v1',
  type: ApiProviderType.openai, // نوع متوافق
  description: 'خدمة ذكاء اصطناعي مخصصة',
);

await ApiProviderService.addProvider(newProvider);
```

### 2. تحديث مفتاح API

```dart
await ApiProviderService.updateApiKey(
  'custom_ai',
  'your-api-key-here',
);
```

### 3. جلب النماذج

```dart
final models = await ApiProviderService.fetchModelsForProvider('custom_ai');
```

## 📊 مراقبة الأداء

### إحصائيات النظام

```dart
final stats = UnifiedApiGateway.getUsageStats();

print('المقدم النشط: ${stats['active_provider']}');
print('النموذج النشط: ${stats['active_model']}');
print('المقدمين النشطين: ${stats['active_providers']}');
print('النماذج المتاحة: ${stats['available_models']}');
```

### اختبار الاتصال

```dart
// اختبار المقدم النشط
final isWorking = await UnifiedApiGateway.testActiveProvider();

// اختبار مقدم محدد
final isProviderWorking = await ApiProviderService.testProvider('openai');
```

## 🛡️ الأمان والموثوقية

### إدارة المفاتيح الآمنة
- **تشفير محلي** لجميع مفاتيح API
- **تخزين آمن** باستخدام `flutter_secure_storage`
- **عدم تسريب** المفاتيح في السجلات

### معالجة الأخطاء
- **إعادة المحاولة التلقائية** عند فشل الطلبات
- **التبديل التلقائي** للمقدم البديل
- **رسائل خطأ واضحة** باللغة العربية

### التحقق من الصحة
- **فحص تلقائي** لصحة مفاتيح API
- **اختبار دوري** لحالة المقدمين
- **تحديث تلقائي** لحالة الاتصال

## 🔮 المميزات المستقبلية

### قيد التطوير
- 🤖 **دعم المزيد من المقدمين**: Cohere, Hugging Face, Local Models
- 📈 **تحليلات متقدمة**: استهلاك الرموز، تكلفة الاستخدام
- 🔄 **توزيع الأحمال**: توزيع الطلبات بين عدة مقدمين
- 💾 **ذاكرة تخزين ذكية**: تخزين مؤقت للاستجابات المتكررة

### تحسينات الأداء
- ⚡ **طلبات متوازية**: إرسال طلبات متعددة في نفس الوقت
- 🎯 **اختيار ذكي للنموذج**: اختيار أفضل نموذج حسب نوع المهمة
- 📊 **تحليل الأداء**: مراقبة سرعة واستقرار كل مقدم

## 📞 الدعم والمساعدة

### الوصول السريع
- **زر APIs** في الشاشة الرئيسية
- **قائمة الإعدادات** → إدارة APIs
- **شاشة الدردشة** → اختيار النموذج

### استكشاف الأخطاء
1. **تحقق من الاتصال بالإنترنت**
2. **تأكد من صحة مفاتيح API**
3. **جرب تحديث النماذج**
4. **اختبر مقدم خدمة آخر**

---

## 🎯 الخلاصة

نظام APIs الموحد يوفر:
- ✅ **بوابة واحدة** لجميع خدمات الذكاء الاصطناعي
- ✅ **تبديل سلس** بين المقدمين والنماذج
- ✅ **اكتشاف تلقائي** للنماذج المتاحة
- ✅ **إدارة شاملة** وواجهة سهلة الاستخدام
- ✅ **أمان عالي** وموثوقية متقدمة

**النتيجة**: تجربة مستخدم متميزة مع مرونة تقنية عالية! 🚀
