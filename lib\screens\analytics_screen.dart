import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/analytics/usage_analytics.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../widgets/enhanced_widgets.dart';

/// شاشة الإحصائيات والتحليلات
class AnalyticsScreen extends ConsumerStatefulWidget {
  const AnalyticsScreen({super.key});

  @override
  ConsumerState<AnalyticsScreen> createState() => _AnalyticsScreenState();
}

class _AnalyticsScreenState extends ConsumerState<AnalyticsScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  UsageStats? _stats;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadStats();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadStats() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final stats = await UsageAnalytics.getUsageStats();
      setState(() {
        _stats = stats;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar('خطأ في تحميل الإحصائيات: $e');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
      ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
      ),
    );
  }

  Future<void> _clearAnalytics() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.darkGrey,
        title: Text(
          'مسح الإحصائيات',
          style: AppTextStyles.heading.copyWith(color: AppColors.white),
        ),
        content: Text(
          'هل أنت متأكد من رغبتك في مسح جميع بيانات الإحصائيات؟ لا يمكن التراجع عن هذا الإجراء.',
          style: AppTextStyles.body.copyWith(color: AppColors.white),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: Text(
              'إلغاء',
              style: AppTextStyles.body.copyWith(color: AppColors.lightPurple),
            ),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: Text(
              'مسح',
              style: AppTextStyles.body.copyWith(color: AppColors.error),
            ),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await UsageAnalytics.clearAnalytics();
      await _loadStats();
      _showSuccessSnackBar('تم مسح الإحصائيات بنجاح');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.darkGrey,
        title: Text(
          'الإحصائيات والتحليلات',
          style: AppTextStyles.heading.copyWith(color: AppColors.white),
        ),
        actions: [
          IconButton(
            onPressed: _loadStats,
            icon: const Icon(Icons.refresh, color: AppColors.white),
          ),
          IconButton(
            onPressed: _clearAnalytics,
            icon: const Icon(Icons.delete_outline, color: AppColors.error),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primaryPurple,
          unselectedLabelColor: AppColors.lightGrey,
          indicatorColor: AppColors.primaryPurple,
          tabs: const [
            Tab(text: 'عام', icon: Icon(Icons.analytics)),
            Tab(text: 'الأدوات', icon: Icon(Icons.build)),
            Tab(text: 'المقدمين', icon: Icon(Icons.cloud)),
          ],
        ),
      ),
      body: _isLoading
          ? const Center(
              child: CircularProgressIndicator(color: AppColors.primaryPurple),
            )
          : _stats == null
              ? _buildErrorState()
              : TabBarView(
                  controller: _tabController,
                  children: [
                    _buildGeneralTab(),
                    _buildToolsTab(),
                    _buildProvidersTab(),
                  ],
                ),
    );
  }

  Widget _buildErrorState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppColors.error.withOpacity(0.5),
          ),
          const SizedBox(height: 16),
          Text(
            'خطأ في تحميل الإحصائيات',
            style: AppTextStyles.heading.copyWith(color: AppColors.error),
          ),
          const SizedBox(height: 8),
          Text(
            'يرجى المحاولة مرة أخرى',
            style: AppTextStyles.body.copyWith(color: AppColors.lightGrey),
          ),
          const SizedBox(height: 24),
          EnhancedButton(
            text: 'إعادة المحاولة',
            onPressed: _loadStats,
            size: ButtonSize.medium,
          ),
        ],
      ),
    );
  }

  Widget _buildGeneralTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatsCard(
            'إحصائيات عامة',
            [
              _buildStatItem('إجمالي الجلسات', '${_stats!.totalSessions}', Icons.timeline),
              _buildStatItem('إجمالي استخدام الأدوات', '${_stats!.totalToolUsage}', Icons.build),
              _buildStatItem('إجمالي الرموز المستخدمة', '${_stats!.totalTokensUsed}', Icons.generating_tokens),
              _buildStatItem(
                'إجمالي وقت الاستخدام',
                '${_stats!.totalSessionTimeMinutes} دقيقة',
                Icons.access_time,
              ),
              _buildStatItem(
                'متوسط وقت الجلسة',
                '${_stats!.averageSessionTimeMinutes} دقيقة',
                Icons.timer,
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildStatsCard(
            'الأكثر استخداماً',
            [
              _buildStatItem('الأداة الأكثر استخداماً', _stats!.mostUsedTool, Icons.star),
              _buildStatItem('المقدم الأكثر استخداماً', _stats!.mostUsedProvider, Icons.cloud),
              if (_stats!.lastSessionDate != null)
                _buildStatItem(
                  'آخر جلسة',
                  _formatDate(_stats!.lastSessionDate!),
                  Icons.schedule,
                ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildToolsTab() {
    final toolsUsage = _stats!.toolsUsage.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatsCard(
            'استخدام الأدوات',
            toolsUsage.isEmpty
                ? [
                    _buildEmptyState('لم يتم استخدام أي أداة بعد'),
                  ]
                : toolsUsage
                    .map((entry) => _buildUsageItem(
                          _getToolDisplayName(entry.key),
                          entry.value,
                          _getToolIcon(entry.key),
                        ))
                    .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildProvidersTab() {
    final providersUsage = _stats!.providersUsage.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    final tokensUsage = _stats!.tokensUsed.entries.toList()
      ..sort((a, b) => b.value.compareTo(a.value));

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildStatsCard(
            'استخدام المقدمين',
            providersUsage.isEmpty
                ? [
                    _buildEmptyState('لم يتم استخدام أي مقدم بعد'),
                  ]
                : providersUsage
                    .map((entry) => _buildUsageItem(
                          _getProviderDisplayName(entry.key),
                          entry.value,
                          _getProviderIcon(entry.key),
                        ))
                    .toList(),
          ),
          const SizedBox(height: 16),
          _buildStatsCard(
            'استخدام الرموز',
            tokensUsage.isEmpty
                ? [
                    _buildEmptyState('لم يتم استخدام أي رموز بعد'),
                  ]
                : tokensUsage
                    .map((entry) => _buildUsageItem(
                          '${_getProviderDisplayName(entry.key)} - رموز',
                          entry.value,
                          Icons.generating_tokens,
                        ))
                    .toList(),
          ),
        ],
      ),
    );
  }

  Widget _buildStatsCard(String title, List<Widget> children) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.lightPurple.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: AppTextStyles.heading.copyWith(
              color: AppColors.primaryPurple,
              fontSize: 18,
            ),
          ),
          const SizedBox(height: 16),
          ...children,
        ],
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: AppColors.lightPurple, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              label,
              style: AppTextStyles.body.copyWith(color: AppColors.white),
            ),
          ),
          Text(
            value,
            style: AppTextStyles.body.copyWith(
              color: AppColors.primaryPurple,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsageItem(String name, int count, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Icon(icon, color: AppColors.lightPurple, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              name,
              style: AppTextStyles.body.copyWith(color: AppColors.white),
            ),
          ),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.primaryPurple.withOpacity(0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              '$count',
              style: AppTextStyles.body.copyWith(
                color: AppColors.primaryPurple,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEmptyState(String message) {
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Center(
        child: Text(
          message,
          style: AppTextStyles.body.copyWith(
            color: AppColors.lightGrey,
            fontStyle: FontStyle.italic,
          ),
        ),
      ),
    );
  }

  String _getToolDisplayName(String toolKey) {
    const toolNames = {
      'chat': 'الدردشة الذكية',
      'image_generation': 'إنشاء الصور',
      'text_summarization': 'تلخيص النصوص',
      'data_analysis': 'تحليل البيانات',
      'code_generation': 'إنشاء الكود',
      'translation': 'الترجمة الذكية',
      'writing_assistant': 'مساعد الكتابة',
      'image_analysis': 'تحليل الصور',
    };
    return toolNames[toolKey] ?? toolKey;
  }

  IconData _getToolIcon(String toolKey) {
    const toolIcons = {
      'chat': Icons.chat,
      'image_generation': Icons.image,
      'text_summarization': Icons.summarize,
      'data_analysis': Icons.analytics,
      'code_generation': Icons.code,
      'translation': Icons.translate,
      'writing_assistant': Icons.edit,
      'image_analysis': Icons.photo_camera,
    };
    return toolIcons[toolKey] ?? Icons.build;
  }

  String _getProviderDisplayName(String providerKey) {
    const providerNames = {
      'openai': 'OpenAI',
      'anthropic': 'Anthropic',
      'google': 'Google',
      'deepseek': 'DeepSeek',
      'local': 'محلي',
    };
    return providerNames[providerKey] ?? providerKey;
  }

  IconData _getProviderIcon(String providerKey) {
    const providerIcons = {
      'openai': Icons.psychology,
      'anthropic': Icons.smart_toy,
      'google': Icons.search,
      'deepseek': Icons.insights,
      'local': Icons.computer,
    };
    return providerIcons[providerKey] ?? Icons.cloud;
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'اليوم';
    } else if (difference.inDays == 1) {
      return 'أمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
