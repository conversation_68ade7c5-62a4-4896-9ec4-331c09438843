# 🚀 دليل التشغيل المفصل - DeepSeek AI

## 📋 المتطلبات الأساسية

### 🔧 متطلبات النظام
- **نظام التشغيل**: Windows 10/11, macOS 10.14+, أو Linux
- **الذاكرة**: 8 GB RAM كحد أدنى (16 GB مُوصى به)
- **التخزين**: 10 GB مساحة فارغة
- **الإنترنت**: اتصال مستقر للـ APIs

### 📱 متطلبات Flutter
```bash
Flutter SDK: 3.7.2 أو أحدث
Dart SDK: 3.0 أو أحدث
```

### 🔑 مفاتيح API المطلوبة
- **OpenAI API Key** (للدردشة وإنشاء الصور)
- **Google Gemini API Key** (اختياري)
- **Anthropic Claude API Key** (اختياري)

---

## 🛠️ خطوات التثبيت

### 1️⃣ **تثبيت Flutter**

#### Windows:
```bash
# تحميل Flutter SDK
git clone https://github.com/flutter/flutter.git -b stable
# إضافة Flutter إلى PATH
set PATH=%PATH%;C:\path\to\flutter\bin
```

#### macOS/Linux:
```bash
# تحميل Flutter SDK
git clone https://github.com/flutter/flutter.git -b stable
# إضافة Flutter إلى PATH
export PATH="$PATH:`pwd`/flutter/bin"
```

### 2️⃣ **التحقق من التثبيت**
```bash
flutter doctor
```

### 3️⃣ **استنساخ المشروع**
```bash
git clone https://github.com/amara-ai/deepseek_project.git
cd deepseek_project
```

### 4️⃣ **تثبيت الاعتماديات**
```bash
flutter pub get
```

### 5️⃣ **إعداد مفاتيح API**

#### الطريقة الأولى: من خلال التطبيق
1. شغل التطبيق
2. اذهب إلى الإعدادات
3. أدخل مفاتيح API في الحقول المخصصة

#### الطريقة الثانية: ملف البيئة
```bash
# إنشاء ملف .env في جذر المشروع
echo "OPENAI_API_KEY=your_openai_key_here" > .env
echo "GEMINI_API_KEY=your_gemini_key_here" >> .env
```

---

## 🚀 تشغيل التطبيق

### 📱 على الأندرويد
```bash
# تشغيل المحاكي
flutter emulators --launch <emulator_name>

# تشغيل التطبيق
flutter run
```

### 🖥️ على Windows
```bash
flutter run -d windows
```

### 🌐 على الويب
```bash
flutter run -d chrome
```

### 🍎 على iOS (macOS فقط)
```bash
flutter run -d ios
```

---

## 🔧 حل المشاكل الشائعة

### ❌ **مشكلة: Flutter command not found**
```bash
# التحقق من PATH
echo $PATH  # Linux/macOS
echo %PATH% # Windows

# إضافة Flutter إلى PATH
export PATH="$PATH:/path/to/flutter/bin"
```

### ❌ **مشكلة: No devices found**
```bash
# تشغيل المحاكي يدوياً
flutter emulators
flutter emulators --launch <emulator_name>

# أو تشغيل على Windows
flutter run -d windows
```

### ❌ **مشكلة: API keys not working**
1. تأكد من صحة المفاتيح
2. تحقق من الاتصال بالإنترنت
3. راجع حدود الاستخدام في حسابك

### ❌ **مشكلة: Build failed**
```bash
# تنظيف المشروع
flutter clean
flutter pub get

# إعادة البناء
flutter run
```

---

## 🧪 تشغيل الاختبارات

### اختبارات الوحدة
```bash
flutter test test/unit_tests.dart
```

### اختبارات التكامل
```bash
flutter test integration_test/
```

### اختبار أدوات الذكاء الاصطناعي
```bash
dart run test_tools.dart
```

---

## 📊 مراقبة الأداء

### 🔍 **فحص الذاكرة**
```bash
flutter run --profile
```

### 📈 **تحليل الأداء**
```bash
flutter run --trace-startup
```

### 🐛 **تصحيح الأخطاء**
```bash
flutter run --debug
```

---

## 🔒 إعدادات الأمان

### 🔐 **حماية مفاتيح API**
- لا تشارك مفاتيح API مع أحد
- استخدم متغيرات البيئة للإنتاج
- فعّل التشفير في التطبيق

### 🛡️ **أذونات التطبيق**
```xml
<!-- Android: android/app/src/main/AndroidManifest.xml -->
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

---

## 📱 إعداد المحاكيات

### Android Studio
1. افتح Android Studio
2. Tools → AVD Manager
3. Create Virtual Device
4. اختر Pixel 4 أو أحدث
5. اختر API Level 30+

### VS Code
1. ثبت إضافة Flutter
2. Ctrl+Shift+P → Flutter: Launch Emulator
3. اختر المحاكي المطلوب

---

## 🌟 ميزات متقدمة

### 🎨 **تخصيص الواجهة**
```dart
// في lib/utils/app_colors.dart
static const Color primaryPurple = Color(0xFF6C5CE7);
```

### 🔧 **إعدادات متقدمة**
```dart
// في lib/core/config/app_config.dart
static const bool enableDebugMode = true;
static const int maxRetries = 3;
```

---

## 📞 الدعم والمساعدة

### 🐛 **الإبلاغ عن الأخطاء**
- GitHub Issues: [رابط المشروع]
- البريد الإلكتروني: <EMAIL>

### 📚 **الموارد المفيدة**
- [Flutter Documentation](https://flutter.dev/docs)
- [OpenAI API Docs](https://platform.openai.com/docs)
- [Google AI Studio](https://makersuite.google.com/)

### 💬 **المجتمع**
- Discord: [رابط الخادم]
- Telegram: [رابط المجموعة]

---

## 🎯 نصائح للأداء الأمثل

### ⚡ **تحسين السرعة**
1. استخدم `flutter run --release` للإنتاج
2. فعّل التخزين المؤقت
3. قلل من استدعاءات API غير الضرورية

### 💾 **توفير البيانات**
1. استخدم الضغط للصور
2. فعّل التخزين المحلي
3. استخدم نماذج أصغر عند الإمكان

### 🔋 **توفير البطارية**
1. قلل من الرسوم المتحركة
2. استخدم الوضع المظلم
3. أوقف الخدمات غير المستخدمة

---

## ✅ قائمة التحقق النهائية

- [ ] تم تثبيت Flutter بنجاح
- [ ] تم استنساخ المشروع
- [ ] تم تثبيت جميع الاعتماديات
- [ ] تم إعداد مفاتيح API
- [ ] تم تشغيل التطبيق بنجاح
- [ ] تم اختبار الميزات الأساسية
- [ ] تم فحص الأداء

🎉 **مبروك! تطبيق DeepSeek AI جاهز للاستخدام!**
