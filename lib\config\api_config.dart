// ملف التكوين لمفاتيح API
// في الإنتاج، يجب استخدام متغيرات البيئة أو خدمة إدارة المفاتيح

import '../core/security/api_key_manager.dart';

class ApiConfig {
  // مفاتيح API - تم نقلها للتخزين الآمن
  static Future<String?> get geminiApiKey => ApiKeyManager.getGeminiKey();
  static Future<String?> get openAiApiKey => ApiKeyManager.getOpenAIKey();
  static Future<String?> get deepseekApiKey => ApiKeyManager.getDeepSeekKey();

  // نقاط النهاية
  static const String geminiBaseUrl =
      'https://generativelanguage.googleapis.com/v1beta/models/';
  static const String openAiBaseUrl = 'https://api.openai.com/v1/';
  static const String deepseekBaseUrl = 'https://api.deepseek.com/v1/';

  // النماذج المتاحة
  static const Map<String, String> availableModels = {
    'gemini-pro': 'Gemini Pro',
    'gemini-pro-vision': 'Gemini Pro Vision',
    'gpt-4': 'GPT-4',
    'gpt-3.5-turbo': 'GPT-3.5 Turbo',
    'deepseek-chat': 'DeepSeek Chat',
    'deepseek-coder': 'DeepSeek Coder',
  };

  // إعدادات افتراضية
  static const String defaultModel = 'gemini-pro';
  static const int defaultMaxTokens = 2048;
  static const double defaultTemperature = 0.7;

  // حدود الاستخدام
  static const int maxRequestsPerMinute = 60;
  static const int maxTokensPerRequest = 4096;
  static const int maxConversationLength = 50;

  // إعدادات الذاكرة المؤقتة
  static const Duration cacheExpiration = Duration(minutes: 30);
  static const int maxCacheSize = 100; // عدد العناصر المخزنة

  // إعدادات الأمان
  static const bool enableRequestLogging = false; // في الإنتاج فقط
  static const bool enableErrorReporting = true;
  static const bool enableAnalytics = false;

  // التحقق من صحة المفتاح
  static bool isApiKeyValid(String key) {
    return key.isNotEmpty && key != 'YOUR_API_KEY_HERE' && key.length > 20;
  }

  // الحصول على المفتاح النشط
  static Future<String?> getActiveApiKey(String model) async {
    if (model.startsWith('gemini')) {
      return await geminiApiKey;
    } else if (model.startsWith('gpt')) {
      return await openAiApiKey;
    } else if (model.startsWith('deepseek')) {
      return await deepseekApiKey;
    }
    return await openAiApiKey; // افتراضي
  }

  // الحصول على URL الأساسي للنموذج
  static String getBaseUrl(String model) {
    if (model.startsWith('gemini')) {
      return geminiBaseUrl;
    } else if (model.startsWith('gpt')) {
      return openAiBaseUrl;
    } else if (model.startsWith('deepseek')) {
      return deepseekBaseUrl;
    }
    return geminiBaseUrl; // افتراضي
  }
}

// إعدادات البيئة
enum Environment { development, staging, production }

class EnvironmentConfig {
  static Environment current = Environment.development;

  static bool get isDevelopment => current == Environment.development;
  static bool get isStaging => current == Environment.staging;
  static bool get isProduction => current == Environment.production;

  static String get apiUrl {
    switch (current) {
      case Environment.development:
        return 'http://localhost:3000/api';
      case Environment.staging:
        return 'https://staging-api.deepseek-app.com';
      case Environment.production:
        return 'https://api.deepseek-app.com';
    }
  }

  static Map<String, String> get headers {
    return {
      'Content-Type': 'application/json',
      'X-App-Version': '1.0.0',
      'X-Platform': 'Flutter',
      if (isDevelopment) 'X-Debug-Mode': 'true',
    };
  }
}
