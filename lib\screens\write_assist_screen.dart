import 'package:flutter/material.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../core/services/enhanced_ai_service.dart';

class WriteAssistScreen extends StatefulWidget {
  const WriteAssistScreen({super.key});

  @override
  State<WriteAssistScreen> createState() => _WriteAssistScreenState();
}

class _WriteAssistScreenState extends State<WriteAssistScreen> {
  final TextEditingController _controller = TextEditingController();
  String? _result;
  bool _loading = false;
  String _selectedStyle = 'إبداعي';

  final List<String> _styles = ['إبداعي', 'احترافي', 'أكاديمي', 'تقني', 'ودي'];

  final List<Map<String, dynamic>> _quickExamples = [
    {
      'icon': Icons.article,
      'title': 'مقال عن أهمية التكنولوجيا في التعليم',
      'color': Colors.blue,
    },
    {
      'icon': Icons.email,
      'title': 'رسالة رسمية لطلب وظيفة',
      'color': Colors.green,
    },
    {
      'icon': Icons.book,
      'title': 'قصة قصيرة عن المغامرة',
      'color': Colors.orange,
    },
    {
      'icon': Icons.business,
      'title': 'تقرير عن أداء الشركة',
      'color': Colors.purple,
    },
  ];

  Future<void> _writeAssist() async {
    if (_controller.text.trim().isEmpty) return;

    setState(() {
      _loading = true;
      _result = null;
    });

    try {
      final styleMap = {
        'إبداعي': 'creative',
        'احترافي': 'professional',
        'أكاديمي': 'academic',
        'تقني': 'technical',
        'ودي': 'casual',
      };

      final result = await EnhancedAIService.advancedWritingAssist(
        topic: _controller.text.trim(),
        style: styleMap[_selectedStyle] ?? 'creative',
        language: 'ar',
        targetLength: 500,
      );
      
      setState(() {
        _result = result['content'];
        _loading = false;
      });
    } catch (e) {
      setState(() {
        _loading = false;
      });
      
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('حدث خطأ: ${e.toString()}'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.darkPurple.withValues(alpha: 0.9),
              AppColors.background,
              AppColors.primaryPurple.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              // شريط علوي
              Container(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    _buildBackButton(),
                    const SizedBox(width: 16),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(
                                Icons.edit,
                                color: AppColors.accentBlue,
                                size: 24,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'مساعدة الكتابة بالذكاء الاصطناعي',
                                style: AppTextStyles.heading2.copyWith(
                                  color: Colors.white,
                                ),
                              ),
                            ],
                          ),
                          Text(
                            'اكتب محتوى إبداعي ومتميز',
                            style: AppTextStyles.body.copyWith(
                              color: AppColors.lightGrey,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // المحتوى الرئيسي
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // موضوع الكتابة
                      _buildTopicInputSection(),
                      
                      const SizedBox(height: 24),
                      
                      // اختيار أسلوب الكتابة
                      _buildStyleSelection(),
                      
                      const SizedBox(height: 24),
                      
                      // أمثلة سريعة
                      _buildQuickExamples(),
                      
                      const SizedBox(height: 32),
                      
                      // زر الكتابة
                      _buildWriteButton(),
                      
                      // النتيجة
                      if (_result != null) _buildResult(),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBackButton() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.darkGrey.withValues(alpha: 0.5),
            AppColors.midGrey.withValues(alpha: 0.5),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppColors.accentBlue.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () => Navigator.pop(context),
          borderRadius: BorderRadius.circular(12),
          child: const Padding(
            padding: EdgeInsets.all(12),
            child: Icon(
              Icons.arrow_back,
              color: Colors.white,
              size: 20,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTopicInputSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.darkGrey.withValues(alpha: 0.8),
            AppColors.darkGrey.withValues(alpha: 0.6),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.accentBlue.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.accentBlue.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.accentBlue.withValues(alpha: 0.2),
                      AppColors.accentBlue.withValues(alpha: 0.1),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.topic,
                  color: AppColors.accentBlue,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'موضوع الكتابة',
                style: AppTextStyles.heading3.copyWith(
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          TextField(
            controller: _controller,
            maxLines: 6,
            style: AppTextStyles.body.copyWith(color: Colors.white),
            decoration: InputDecoration(
              hintText: 'اكتب موضوع الكتابة أو الفكرة التي تريد تطويرها...',
              hintStyle: AppTextStyles.body.copyWith(
                color: AppColors.lightGrey,
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: AppColors.accentBlue.withValues(alpha: 0.3),
                ),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: AppColors.accentBlue.withValues(alpha: 0.3),
                ),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide(
                  color: AppColors.accentBlue,
                  width: 2,
                ),
              ),
              filled: true,
              fillColor: AppColors.darkGrey.withValues(alpha: 0.3),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStyleSelection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'اختر أسلوب الكتابة',
          style: AppTextStyles.heading3.copyWith(
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 50,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: _styles.length,
            itemBuilder: (context, index) {
              final style = _styles[index];
              final isSelected = style == _selectedStyle;
              
              return Container(
                margin: const EdgeInsets.only(right: 12),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      setState(() {
                        _selectedStyle = style;
                      });
                    },
                    borderRadius: BorderRadius.circular(25),
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 20,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        gradient: isSelected
                            ? LinearGradient(
                                colors: [
                                  AppColors.primaryPurple,
                                  AppColors.accentBlue,
                                ],
                              )
                            : LinearGradient(
                                colors: [
                                  AppColors.darkGrey.withValues(alpha: 0.5),
                                  AppColors.midGrey.withValues(alpha: 0.5),
                                ],
                              ),
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                          color: isSelected
                              ? AppColors.accentBlue
                              : AppColors.accentBlue.withValues(alpha: 0.3),
                          width: 1,
                        ),
                      ),
                      child: Text(
                        style,
                        style: AppTextStyles.body.copyWith(
                          color: Colors.white,
                          fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildQuickExamples() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'أمثلة سريعة',
          style: AppTextStyles.heading3.copyWith(
            color: Colors.white,
          ),
        ),
        const SizedBox(height: 16),
        ...List.generate(_quickExamples.length, (index) {
          final example = _quickExamples[index];
          return Container(
            margin: const EdgeInsets.only(bottom: 12),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () {
                  _controller.text = example['title'];
                },
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        AppColors.darkGrey.withValues(alpha: 0.6),
                        AppColors.darkGrey.withValues(alpha: 0.4),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: (example['color'] as Color).withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    children: [
                      Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: (example['color'] as Color).withValues(alpha: 0.2),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Icon(
                          example['icon'],
                          color: example['color'],
                          size: 20,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: Text(
                          example['title'],
                          style: AppTextStyles.body.copyWith(
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  Widget _buildWriteButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryPurple,
            AppColors.accentBlue,
          ],
        ),
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryPurple.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _loading ? null : _writeAssist,
          borderRadius: BorderRadius.circular(28),
          child: Center(
            child: _loading
                ? const SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: Colors.white,
                      strokeWidth: 2,
                    ),
                  )
                : Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.auto_awesome,
                        color: Colors.white,
                        size: 24,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'ابدأ الكتابة',
                        style: AppTextStyles.button.copyWith(
                          color: Colors.white,
                        ),
                      ),
                    ],
                  ),
          ),
        ),
      ),
    );
  }

  Widget _buildResult() {
    return Container(
      margin: const EdgeInsets.only(top: 24),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.accentGreen.withValues(alpha: 0.2),
            AppColors.accentGreen.withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.accentGreen.withValues(alpha: 0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: AppColors.accentGreen.withValues(alpha: 0.2),
            blurRadius: 20,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.accentGreen.withValues(alpha: 0.3),
                      AppColors.accentGreen.withValues(alpha: 0.2),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.check_circle,
                  color: AppColors.accentGreen,
                  size: 24,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'المحتوى المكتوب',
                style: AppTextStyles.heading3.copyWith(
                  color: Colors.white,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Text(
            _result!,
            style: AppTextStyles.body.copyWith(
              color: Colors.white,
              height: 1.6,
            ),
          ),
        ],
      ),
    );
  }
}
