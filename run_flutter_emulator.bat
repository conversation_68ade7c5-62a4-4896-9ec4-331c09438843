@echo off
chcp 65001 >nul
title Flutter Emulator Runner

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    🚀 Flutter Emulator Runner                ║
echo ║                      تشغيل Flutter بالمحاكي                 ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 📋 التحقق من بيئة Flutter...
flutter doctor --android-licenses >nul 2>&1

echo 📱 البحث عن المحاكيات المتاحة...
flutter devices

echo.
echo 🔄 تنظيف المشروع...
flutter clean

echo 📦 تحديث الاعتماديات...
flutter pub get

echo.
echo 🏗️ بناء وتشغيل التطبيق على المحاكي...
echo ⏳ قد يستغرق هذا بضع دقائق في المرة الأولى...
echo.

flutter run

echo.
echo ✅ تم الانتهاء!
echo.
pause
