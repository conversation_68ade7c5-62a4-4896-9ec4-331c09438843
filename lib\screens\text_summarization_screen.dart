import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:io';
import 'package:file_picker/file_picker.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../core/services/enhanced_ai_service.dart';
import '../widgets/modern_ui_components.dart';

class TextSummarizationScreen extends StatefulWidget {
  const TextSummarizationScreen({super.key});

  @override
  State<TextSummarizationScreen> createState() => _TextSummarizationScreenState();
}

class _TextSummarizationScreenState extends State<TextSummarizationScreen>
    with TickerProviderStateMixin {
  final TextEditingController _textController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  String _summary = '';
  bool _isLoading = false;
  String _selectedStyle = 'شامل';
  int _maxLength = 500;
  String? _selectedFile;

  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;

  final List<String> _summaryStyles = [
    'شامل',
    'نقاط رئيسية',
    'ملخص سريع',
    'تحليلي',
    'إبداعي',
  ];

  final List<Map<String, dynamic>> _quickActions = [
    {
      'icon': Icons.file_upload_outlined,
      'title': 'رفع ملف',
      'subtitle': 'اختر ملف نصي',
      'color': Colors.blue,
    },
    {
      'icon': Icons.content_paste,
      'title': 'لصق النص',
      'subtitle': 'من الحافظة',
      'color': Colors.green,
    },
    {
      'icon': Icons.clear_all,
      'title': 'مسح الكل',
      'subtitle': 'بدء جديد',
      'color': Colors.orange,
    },
    {
      'icon': Icons.save_outlined,
      'title': 'حفظ الملخص',
      'subtitle': 'تصدير النتيجة',
      'color': Colors.purple,
    },
  ];

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );
    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.forward();
  }

  @override
  void dispose() {
    _textController.dispose();
    _scrollController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _summarizeText() async {
    if (_textController.text.trim().isEmpty) {
      _showSnackBar('يرجى إدخال النص المراد تلخيصه', isError: true);
      return;
    }

    setState(() {
      _isLoading = true;
      _summary = '';
    });

    try {
      final result = await EnhancedAIService.summarizeText(
        text: _textController.text.trim(),
        style: _selectedStyle.toLowerCase(),
        maxLength: _maxLength,
      );

      setState(() {
        _summary = result;
        _isLoading = false;
      });

      _showSnackBar('تم إنشاء الملخص بنجاح!');
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showSnackBar('حدث خطأ أثناء التلخيص: ${e.toString()}', isError: true);
    }
  }

  Future<void> _pickFile() async {
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['txt', 'md', 'doc', 'docx'],
      );

      if (result != null) {
        File file = File(result.files.single.path!);
        String content = await file.readAsString();

        setState(() {
          _textController.text = content;
          _selectedFile = result.files.single.name;
        });

        _showSnackBar('تم تحميل الملف بنجاح');
      }
    } catch (e) {
      _showSnackBar('فشل في تحميل الملف: ${e.toString()}', isError: true);
    }
  }

  Future<void> _pasteFromClipboard() async {
    try {
      ClipboardData? data = await Clipboard.getData(Clipboard.kTextPlain);
      if (data != null && data.text != null) {
        setState(() {
          _textController.text = data.text!;
        });
        _showSnackBar('تم لصق النص من الحافظة');
      } else {
        _showSnackBar('لا يوجد نص في الحافظة', isError: true);
      }
    } catch (e) {
      _showSnackBar('فشل في لصق النص: ${e.toString()}', isError: true);
    }
  }

  void _clearAll() {
    setState(() {
      _textController.clear();
      _summary = '';
      _selectedFile = null;
    });
    _showSnackBar('تم مسح جميع البيانات');
  }

  Future<void> _saveSummary() async {
    if (_summary.isEmpty) {
      _showSnackBar('لا يوجد ملخص لحفظه', isError: true);
      return;
    }

    try {
      await Clipboard.setData(ClipboardData(text: _summary));
      _showSnackBar('تم نسخ الملخص إلى الحافظة');
    } catch (e) {
      _showSnackBar('فشل في نسخ الملخص: ${e.toString()}', isError: true);
    }
  }

  void _showSnackBar(String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? Colors.red : AppColors.primaryPurple,
        duration: const Duration(seconds: 3),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.darkPurple.withValues(alpha: 0.9),
              AppColors.background,
              AppColors.primaryPurple.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                _buildHeader(),
                _buildQuickActions(),
                Expanded(
                  child: SingleChildScrollView(
                    controller: _scrollController,
                    padding: const EdgeInsets.all(16),
                    child: Column(
                      children: [
                        _buildInputSection(),
                        const SizedBox(height: 20),
                        _buildSettingsSection(),
                        const SizedBox(height: 20),
                        _buildSummarizeButton(),
                        if (_summary.isNotEmpty || _isLoading) ...[
                          const SizedBox(height: 20),
                          _buildResultSection(),
                        ],
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Container(
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: AppColors.electricBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => Navigator.pop(context),
                borderRadius: BorderRadius.circular(12),
                child: const Padding(
                  padding: EdgeInsets.all(12),
                  child: Icon(Icons.arrow_back, color: Colors.white, size: 20),
                ),
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      Icons.summarize_outlined,
                      color: AppColors.electricBlue,
                      size: 24,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'تلخيص النص',
                      style: AppTextStyles.heading2.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
                Text(
                  'لخص النصوص الطويلة بذكاء',
                  style: AppTextStyles.body.copyWith(
                    color: AppColors.textSecondary,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActions() {
    return Container(
      height: 100,
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _quickActions.length,
        itemBuilder: (context, index) {
          final action = _quickActions[index];
          return Container(
            width: 120,
            margin: const EdgeInsets.only(right: 12),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: () => _handleQuickAction(index),
                borderRadius: BorderRadius.circular(16),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      colors: [
                        action['color'].withValues(alpha: 0.2),
                        action['color'].withValues(alpha: 0.1),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: action['color'].withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        action['icon'],
                        color: action['color'],
                        size: 24,
                      ),
                      const SizedBox(height: 4),
                      Text(
                        action['title'],
                        style: AppTextStyles.caption.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        action['subtitle'],
                        style: AppTextStyles.caption.copyWith(
                          color: AppColors.textSecondary,
                          fontSize: 10,
                        ),
                        textAlign: TextAlign.center,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  void _handleQuickAction(int index) {
    switch (index) {
      case 0:
        _pickFile();
        break;
      case 1:
        _pasteFromClipboard();
        break;
      case 2:
        _clearAll();
        break;
      case 3:
        _saveSummary();
        break;
    }
  }

  Widget _buildInputSection() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.electricBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.text_fields,
                  color: AppColors.electricBlue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'النص المراد تلخيصه',
                  style: AppTextStyles.body.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                if (_selectedFile != null) ...[
                  const Spacer(),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: AppColors.primaryPurple.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      _selectedFile!,
                      style: AppTextStyles.caption.copyWith(
                        color: AppColors.electricBlue,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.electricBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: TextField(
              controller: _textController,
              style: AppTextStyles.body.copyWith(color: Colors.white),
              maxLines: 8,
              decoration: InputDecoration(
                hintText: 'اكتب أو الصق النص هنا...',
                hintStyle: AppTextStyles.body.copyWith(
                  color: AppColors.textSecondary,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSettingsSection() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.electricBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.settings_outlined,
                color: AppColors.electricBlue,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'إعدادات التلخيص',
                style: AppTextStyles.body.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // نمط التلخيص
          Text(
            'نمط التلخيص',
            style: AppTextStyles.body.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _summaryStyles.map((style) {
              final isSelected = style == _selectedStyle;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedStyle = style;
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  decoration: BoxDecoration(
                    gradient: isSelected
                        ? LinearGradient(
                            colors: [
                              AppColors.primaryPurple,
                              AppColors.electricBlue,
                            ],
                          )
                        : LinearGradient(
                            colors: [
                              AppColors.darkGrey.withValues(alpha: 0.5),
                              AppColors.darkGrey.withValues(alpha: 0.3),
                            ],
                          ),
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected
                          ? AppColors.electricBlue
                          : AppColors.electricBlue.withValues(alpha: 0.3),
                      width: 1,
                    ),
                  ),
                  child: Text(
                    style,
                    style: AppTextStyles.body.copyWith(
                      color: Colors.white,
                      fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: 16),

          // طول الملخص
          Text(
            'الحد الأقصى لطول الملخص: $_maxLength كلمة',
            style: AppTextStyles.body.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: AppColors.electricBlue,
              inactiveTrackColor: AppColors.darkGrey,
              thumbColor: AppColors.primaryPurple,
              overlayColor: AppColors.primaryPurple.withValues(alpha: 0.2),
            ),
            child: Slider(
              value: _maxLength.toDouble(),
              min: 100,
              max: 1000,
              divisions: 18,
              onChanged: (value) {
                setState(() {
                  _maxLength = value.round();
                });
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSummarizeButton() {
    return Container(
      width: double.infinity,
      height: 56,
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppColors.primaryPurple,
            AppColors.electricBlue,
          ],
        ),
        borderRadius: BorderRadius.circular(28),
        boxShadow: [
          BoxShadow(
            color: AppColors.primaryPurple.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: _isLoading ? null : _summarizeText,
          borderRadius: BorderRadius.circular(28),
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 24),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (_isLoading) ...[
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'جاري التلخيص...',
                    style: AppTextStyles.body.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ] else ...[
                  Icon(
                    Icons.auto_awesome,
                    color: Colors.white,
                    size: 20,
                  ),
                  const SizedBox(width: 12),
                  Text(
                    'تلخيص النص',
                    style: AppTextStyles.body.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildResultSection() {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppColors.electricBlue.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Icon(
                  Icons.summarize,
                  color: AppColors.electricBlue,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'الملخص',
                  style: AppTextStyles.body.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                if (_summary.isNotEmpty)
                  GestureDetector(
                    onTap: () {
                      Clipboard.setData(ClipboardData(text: _summary));
                      _showSnackBar('تم نسخ الملخص');
                    },
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppColors.primaryPurple.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.copy,
                        color: AppColors.electricBlue,
                        size: 16,
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: AppColors.darkGrey.withValues(alpha: 0.5),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppColors.electricBlue.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: _isLoading
                ? Center(
                    child: Column(
                      children: [
                        CircularProgressIndicator(
                          valueColor: AlwaysStoppedAnimation<Color>(
                            AppColors.electricBlue,
                          ),
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'جاري تحليل النص وإنشاء الملخص...',
                          style: AppTextStyles.body.copyWith(
                            color: AppColors.textSecondary,
                          ),
                        ),
                      ],
                    ),
                  )
                : SelectableText(
                    _summary,
                    style: AppTextStyles.body.copyWith(
                      color: Colors.white,
                      height: 1.6,
                    ),
                  ),
          ),
        ],
      ),
    );
  }
}