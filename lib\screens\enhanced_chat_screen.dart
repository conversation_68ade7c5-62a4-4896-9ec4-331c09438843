import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:uuid/uuid.dart';
import 'package:image_picker/image_picker.dart';
// import 'package:file_picker/file_picker.dart'; // معطل مؤقتاً
import 'package:permission_handler/permission_handler.dart';

import '../core/services/enhanced_ai_service.dart';
import '../core/models/conversation_model.dart';
import '../core/providers/app_state_provider.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../widgets/enhanced_widgets.dart';
import '../widgets/glassmorphism_widgets.dart';
import '../widgets/animated_background.dart';

class EnhancedChatScreen extends ConsumerStatefulWidget {
  final String? conversationId;

  const EnhancedChatScreen({super.key, this.conversationId});

  @override
  ConsumerState<EnhancedChatScreen> createState() => _EnhancedChatScreenState();
}

class _EnhancedChatScreenState extends ConsumerState<EnhancedChatScreen>
    with TickerProviderStateMixin {
  late String _conversationId;
  late ConversationModel _conversation;
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();
  late AnimationController _typingController;
  bool _isTyping = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _conversationId = widget.conversationId ?? const Uuid().v4();
    _conversation = ConversationModel.create(
      title: 'محادثة جديدة',
      type: ConversationType.chat,
    );

    _typingController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // إضافة رسالة ترحيب
    _addWelcomeMessage();
  }

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    _typingController.dispose();
    super.dispose();
  }

  void _addWelcomeMessage() {
    // لا نضيف رسالة ترحيب تلقائية - سنعرض الشاشة الفارغة
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      extendBodyBehindAppBar: true,
      appBar: _buildGlassAppBar(),
      body: AnimatedBackground(
        enableParticles: false,
        enableGradientAnimation: true,
        particleCount: 0,
        child: Column(
          children: [
            Expanded(child: _buildMessagesList()),
            _buildTypingIndicator(),
            _buildGlassMessageInput(),
          ],
        ),
      ),
    );
  }

  /// شريط تطبيق زجاجي حديث
  PreferredSizeWidget _buildGlassAppBar() {
    return GlassmorphismWidgets.glassAppBar(
      title: _conversation.title,
      centerTitle: false,
      actions: [
        // عداد الرسائل
        Container(
          margin: const EdgeInsets.only(right: 8),
          child: GlassmorphismWidgets.glassContainer(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            borderRadius: BorderRadius.circular(16),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  Icons.chat_bubble_outline_rounded,
                  size: 16,
                  color: AppColors.white.withValues(alpha: 0.9),
                ),
                const SizedBox(width: 6),
                Text(
                  '${_conversation.messages.length}',
                  style: AppTextStyles.caption.copyWith(
                    color: AppColors.white.withValues(alpha: 0.9),
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ),
        // زر الخيارات
        Container(
          margin: const EdgeInsets.only(right: 8),
          child: Material(
            color: Colors.transparent,
            child: InkWell(
              onTap: _showChatOptions,
              borderRadius: BorderRadius.circular(20),
              child: Container(
                padding: const EdgeInsets.all(8),
                child: Icon(
                  Icons.more_vert_rounded,
                  color: AppColors.white.withValues(alpha: 0.9),
                  size: 20,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildMessagesList() {
    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + kToolbarHeight + 16,
        left: 16,
        right: 16,
        bottom: 16,
      ),
      child:
          _conversation.messages.isEmpty
              ? _buildEmptyState()
              : ListView.builder(
                controller: _scrollController,
                physics: const BouncingScrollPhysics(),
                itemCount: _conversation.messages.length,
                itemBuilder: (context, index) {
                  final message = _conversation.messages[index];
                  return _buildMessageBubble(message, index);
                },
              ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            // أيقونة متحركة
            TweenAnimationBuilder<double>(
              tween: Tween(begin: 0.0, end: 1.0),
              duration: const Duration(milliseconds: 1000),
              builder: (context, value, child) {
                return Transform.scale(
                  scale: 0.8 + (0.2 * value),
                  child: Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      gradient: AppColors.primaryGradient,
                      borderRadius: BorderRadius.circular(60),
                      boxShadow: [
                        BoxShadow(
                          color: AppColors.primaryPurple.withValues(alpha: 0.3),
                          blurRadius: 20,
                          spreadRadius: 5,
                        ),
                      ],
                    ),
                    child: const Icon(
                      Icons.smart_toy_rounded,
                      size: 60,
                      color: AppColors.white,
                    ),
                  ),
                );
              },
            ),
            const SizedBox(height: 32),
            // النص الترحيبي
            Text(
              'مرحباً بك! 👋',
              style: AppTextStyles.heading1.copyWith(
                fontSize: 28,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'أنا مساعدك الذكي',
              style: AppTextStyles.heading2.copyWith(
                color: AppColors.primaryPurple,
                fontSize: 20,
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 32),
              child: Text(
                'يمكنني مساعدتك في الإجابة على أسئلتك، حل المشاكل، أو مجرد الدردشة معك',
                style: AppTextStyles.body.copyWith(
                  color: AppColors.white.withValues(alpha: 0.8),
                  height: 1.5,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            const SizedBox(height: 32),
            // اقتراحات سريعة
            _buildQuickSuggestions(),
            const SizedBox(height: 24),
            // نص تشجيعي
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
              decoration: BoxDecoration(
                color: AppColors.glassBackground,
                borderRadius: BorderRadius.circular(20),
                border: Border.all(color: AppColors.glassBorder),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.keyboard_arrow_down,
                    color: AppColors.primaryPurple,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'ابدأ بكتابة رسالتك أدناه',
                    style: AppTextStyles.caption.copyWith(
                      color: AppColors.white.withValues(alpha: 0.9),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickSuggestions() {
    final suggestions = [
      {'icon': Icons.help_outline, 'text': 'اسأل سؤال'},
      {'icon': Icons.lightbulb_outline, 'text': 'اطلب فكرة'},
      {'icon': Icons.chat_bubble_outline, 'text': 'ابدأ محادثة'},
    ];

    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children:
          suggestions.map((suggestion) {
            return GestureDetector(
              onTap: () {
                _messageController.text = suggestion['text'] as String;
              },
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 10,
                ),
                decoration: BoxDecoration(
                  gradient: AppColors.glassGradient,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: AppColors.glassBorder),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      suggestion['icon'] as IconData,
                      color: AppColors.primaryPurple,
                      size: 18,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      suggestion['text'] as String,
                      style: AppTextStyles.caption.copyWith(
                        color: AppColors.white.withValues(alpha: 0.9),
                        fontSize: 13,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
    );
  }

  Widget _buildMessageBubble(MessageModel message, int index) {
    final isUser = message.role == MessageRole.user;

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      child: Row(
        mainAxisAlignment:
            isUser ? MainAxisAlignment.end : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (!isUser) _buildAvatar(false),
          if (!isUser) const SizedBox(width: 12),
          Flexible(
            child: Container(
              margin: EdgeInsets.only(
                left: isUser ? 50 : 0,
                right: isUser ? 0 : 50,
              ),
              child: GlassmorphismWidgets.glassCard(
                gradient:
                    isUser
                        ? AppColors.primaryGradient
                        : AppColors.glassGradient,
                padding: const EdgeInsets.all(16),
                borderRadius: BorderRadius.circular(20),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      message.content,
                      style: AppTextStyles.body.copyWith(
                        color: AppColors.white,
                        height: 1.4,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          _formatTime(message.timestamp),
                          style: AppTextStyles.caption.copyWith(
                            color: AppColors.white.withValues(alpha: 0.6),
                          ),
                        ),
                        if (isUser) ...[
                          const SizedBox(width: 8),
                          Icon(
                            _getMessageStatusIcon(message.status),
                            size: 16,
                            color: AppColors.white.withValues(alpha: 0.6),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (isUser) const SizedBox(width: 12),
          if (isUser) _buildAvatar(true),
        ],
      ),
    );
  }

  Widget _buildAvatar(bool isUser) {
    return GlassmorphismWidgets.glassContainer(
      width: 40,
      height: 40,
      padding: EdgeInsets.zero,
      borderRadius: BorderRadius.circular(20),
      child: Container(
        decoration: BoxDecoration(
          gradient:
              isUser ? AppColors.primaryGradient : AppColors.secondaryGradient,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(
          isUser ? Icons.person_rounded : Icons.smart_toy_rounded,
          color: AppColors.white,
          size: 20,
        ),
      ),
    );
  }

  Widget _buildTypingIndicator() {
    if (!_isTyping) return const SizedBox.shrink();

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      child: Row(
        children: [
          _buildAvatar(false),
          const SizedBox(width: 12),
          EnhancedCard(
            backgroundColor: AppColors.darkGrey,
            padding: const EdgeInsets.all(16),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'يكتب',
                  style: AppTextStyles.body.copyWith(
                    color: AppColors.white.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(width: 8),
                SizedBox(
                  width: 20,
                  height: 20,
                  child: EnhancedLoadingIndicator(
                    type: LoadingType.dots,
                    size: 20,
                    color: AppColors.primaryPurple,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// مربع إدخال زجاجي حديث
  Widget _buildGlassMessageInput() {
    return Container(
      padding: const EdgeInsets.fromLTRB(12, 8, 12, 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            Colors.transparent,
            AppColors.darkGrey.withValues(alpha: 0.4),
          ],
        ),
      ),
      child: SafeArea(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 4),
          decoration: BoxDecoration(
            color: AppColors.glassBackground.withValues(alpha: 0.9),
            borderRadius: BorderRadius.circular(30),
            border: Border.all(
              color: AppColors.glassBorder.withValues(alpha: 0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                spreadRadius: 1,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              // زر الإرفاق
              Container(
                margin: const EdgeInsets.only(left: 4),
                decoration: BoxDecoration(
                  gradient: AppColors.secondaryGradient,
                  borderRadius: BorderRadius.circular(24),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primaryPurple.withValues(alpha: 0.3),
                      blurRadius: 6,
                      spreadRadius: 1,
                    ),
                  ],
                ),
                child: Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: _showAttachmentOptions,
                    borderRadius: BorderRadius.circular(24),
                    child: Container(
                      width: 48,
                      height: 48,
                      padding: EdgeInsets.zero,
                      child: const Icon(
                        Icons.attach_file_rounded,
                        color: AppColors.white,
                        size: 20,
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // حقل النص
              Expanded(
                child: Container(
                  constraints: const BoxConstraints(
                    minHeight: 48,
                    maxHeight: 120,
                  ),
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 12,
                  ),
                  child: TextField(
                    controller: _messageController,
                    style: const TextStyle(
                      color: AppColors.white,
                      fontSize: 16,
                      height: 1.4,
                    ),
                    decoration: const InputDecoration(
                      hintText: 'اكتب رسالتك هنا... 💬',
                      hintStyle: TextStyle(
                        color: AppColors.mutedText,
                        fontSize: 16,
                      ),
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                      isDense: true,
                    ),
                    maxLines: 4,
                    minLines: 1,
                    textDirection: TextDirection.rtl,
                    onSubmitted: (_) => _sendMessage(),
                    onChanged: (text) {
                      setState(() {});
                    },
                  ),
                ),
              ),
              const SizedBox(width: 8),
              // زر الإرسال/المايكروفون
              Container(
                margin: const EdgeInsets.only(right: 4),
                child: AnimatedContainer(
                  duration: const Duration(milliseconds: 200),
                  decoration: BoxDecoration(
                    gradient:
                        _messageController.text.trim().isNotEmpty
                            ? AppColors.primaryGradient
                            : AppColors.glassGradient,
                    borderRadius: BorderRadius.circular(24),
                    boxShadow:
                        _messageController.text.trim().isNotEmpty
                            ? [
                              BoxShadow(
                                color: AppColors.primaryPurple.withValues(
                                  alpha: 0.4,
                                ),
                                blurRadius: 8,
                                spreadRadius: 1,
                              ),
                            ]
                            : [
                              BoxShadow(
                                color: AppColors.primaryPurple.withValues(
                                  alpha: 0.2,
                                ),
                                blurRadius: 4,
                                spreadRadius: 0,
                              ),
                            ],
                  ),
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap:
                          _messageController.text.trim().isNotEmpty
                              ? (_isLoading ? null : _sendMessage)
                              : () {
                                // وظيفة المايكروفون (يمكن إضافتها لاحقاً)
                                ScaffoldMessenger.of(context).showSnackBar(
                                  const SnackBar(
                                    content: Text('ميزة التسجيل الصوتي قريباً'),
                                    duration: Duration(seconds: 2),
                                  ),
                                );
                              },
                      borderRadius: BorderRadius.circular(24),
                      child: Container(
                        width: 48,
                        height: 48,
                        padding: EdgeInsets.zero,
                        child:
                            _isLoading
                                ? const SizedBox(
                                  width: 20,
                                  height: 20,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      AppColors.white,
                                    ),
                                  ),
                                )
                                : Icon(
                                  _messageController.text.trim().isNotEmpty
                                      ? Icons.send_rounded
                                      : Icons.mic_rounded,
                                  color: AppColors.white,
                                  size: 20,
                                ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _sendMessage() async {
    final messageText = _messageController.text.trim();
    if (messageText.isEmpty || _isLoading) return;

    // إضافة رسالة المستخدم
    final userMessage = MessageModel.create(
      content: messageText,
      role: MessageRole.user,
    );

    setState(() {
      _conversation = _conversation.addMessage(userMessage);
      _messageController.clear();
      _isLoading = true;
      _isTyping = true;
    });

    _scrollToBottom();

    try {
      // إرسال الرسالة للذكاء الاصطناعي
      final aiResponse = await EnhancedAIService.createSmartChat(
        message: messageText,
        conversationId: _conversationId,
        context:
            _conversation.messages
                .take(_conversation.messages.length - 1)
                .toList(),
      );

      setState(() {
        _conversation = _conversation.addMessage(aiResponse);
        _isLoading = false;
        _isTyping = false;
      });

      _scrollToBottom();

      // حفظ المحادثة
      ref.read(conversationsProvider.notifier).addConversation(_conversation);
    } catch (e) {
      setState(() {
        _isLoading = false;
        _isTyping = false;
      });

      // إضافة رسالة خطأ في المحادثة
      final errorMessage = MessageModel.create(
        content:
            'عذراً، حدث خطأ: ${e.toString()}\n\nللحصول على ردود من الذكاء الاصطناعي، يرجى إضافة مفتاح OpenAI API في الإعدادات.',
        role: MessageRole.assistant,
      );

      setState(() {
        _conversation = _conversation.addMessage(errorMessage);
      });

      _showErrorMessage('فشل في إرسال الرسالة: ${e.toString()}');
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 300),
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _showErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showChatOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.darkGrey,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppColors.white.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),
                _buildOptionTile(
                  icon: Icons.edit,
                  title: 'تعديل عنوان المحادثة',
                  onTap: _editConversationTitle,
                ),
                _buildOptionTile(
                  icon: Icons.share,
                  title: 'مشاركة المحادثة',
                  onTap: _shareConversation,
                ),
                _buildOptionTile(
                  icon: Icons.download,
                  title: 'تصدير المحادثة',
                  onTap: _exportConversation,
                ),
                _buildOptionTile(
                  icon: Icons.delete,
                  title: 'حذف المحادثة',
                  onTap: _deleteConversation,
                  isDestructive: true,
                ),
              ],
            ),
          ),
    );
  }

  Widget _buildOptionTile({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? AppColors.error : AppColors.primaryPurple,
      ),
      title: Text(
        title,
        style: AppTextStyles.body.copyWith(
          color: isDestructive ? AppColors.error : AppColors.white,
        ),
      ),
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
    );
  }

  void _showAttachmentOptions() {
    showModalBottomSheet(
      context: context,
      backgroundColor: AppColors.darkGrey,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: AppColors.white.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 20),
                Text('إرفاق ملف', style: AppTextStyles.heading2),
                const SizedBox(height: 16),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    _buildAttachmentOption(
                      icon: Icons.photo,
                      label: 'صورة',
                      color: AppColors.accentBlue,
                      onTap: _pickImage,
                    ),
                    _buildAttachmentOption(
                      icon: Icons.description,
                      label: 'مستند',
                      color: AppColors.accentGreen,
                      onTap: _pickDocument,
                    ),
                    _buildAttachmentOption(
                      icon: Icons.mic,
                      label: 'صوت',
                      color: AppColors.accentOrange,
                      onTap: _recordAudio,
                    ),
                  ],
                ),
                const SizedBox(height: 16),
              ],
            ),
          ),
    );
  }

  Widget _buildAttachmentOption({
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: () {
        Navigator.pop(context);
        onTap();
      },
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: color,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(icon, color: AppColors.white, size: 30),
          ),
          const SizedBox(height: 8),
          Text(label, style: AppTextStyles.caption),
        ],
      ),
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes} د';
    } else if (difference.inDays < 1) {
      return '${difference.inHours} س';
    } else {
      return '${dateTime.day}/${dateTime.month}';
    }
  }

  IconData _getMessageStatusIcon(MessageStatus status) {
    switch (status) {
      case MessageStatus.sending:
        return Icons.access_time;
      case MessageStatus.sent:
        return Icons.check;
      case MessageStatus.delivered:
        return Icons.done_all;
      case MessageStatus.failed:
        return Icons.error;
      case MessageStatus.typing:
        return Icons.more_horiz;
    }
  }

  void _editConversationTitle() {
    // تنفيذ تعديل العنوان
  }

  void _shareConversation() {
    // تنفيذ مشاركة المحادثة
  }

  void _exportConversation() {
    // تنفيذ تصدير المحادثة
  }

  void _deleteConversation() {
    // تنفيذ حذف المحادثة
  }

  // وظائف الإرفاق الحقيقية
  Future<void> _pickImage() async {
    try {
      // عرض خيارات اختيار الصورة
      showModalBottomSheet(
        context: context,
        backgroundColor: AppColors.darkGrey,
        shape: const RoundedRectangleBorder(
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        builder:
            (context) => Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Container(
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                      color: AppColors.white.withValues(alpha: 0.3),
                      borderRadius: BorderRadius.circular(2),
                    ),
                  ),
                  const SizedBox(height: 20),
                  Text('اختيار صورة 🖼️', style: AppTextStyles.heading2),
                  const SizedBox(height: 16),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      _buildImageSourceOption(
                        icon: Icons.camera_alt,
                        label: 'كاميرا',
                        onTap: () async {
                          Navigator.pop(context);
                          await _pickImageFromSource(ImageSource.camera);
                        },
                      ),
                      _buildImageSourceOption(
                        icon: Icons.photo_library,
                        label: 'معرض الصور',
                        onTap: () async {
                          Navigator.pop(context);
                          await _pickImageFromSource(ImageSource.gallery);
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
      );
    } catch (e) {
      _showErrorSnackBar('خطأ في اختيار الصورة: $e');
    }
  }

  Future<void> _pickImageFromSource(ImageSource source) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? image = await picker.pickImage(
        source: source,
        maxWidth: 1920,
        maxHeight: 1080,
        imageQuality: 85,
      );

      if (image != null) {
        await _handleImageSelected(image);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في اختيار الصورة: $e');
    }
  }

  Future<void> _pickDocument() async {
    // معطل مؤقتاً - يحتاج مكتبة file_picker
    _showErrorSnackBar('اختيار الملفات غير متاح حالياً');
    /*
    try {
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx', 'txt', 'xlsx', 'pptx'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;
        await _handleDocumentSelected(file);
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في اختيار المستند: $e');
    }
    */
  }

  Future<void> _recordAudio() async {
    try {
      // طلب إذن الميكروفون
      final permission = await Permission.microphone.request();

      if (permission.isGranted) {
        _showInfoDialog(
          'تسجيل صوتي 🎤',
          'ميزة التسجيل الصوتي قيد التطوير.\n\nسيتم إضافتها قريباً لتتمكن من:\n• تسجيل رسائل صوتية\n• تحويل الصوت لنص\n• الرد بالصوت من الذكاء الاصطناعي',
        );
      } else {
        _showErrorSnackBar('يجب السماح باستخدام الميكروفون');
      }
    } catch (e) {
      _showErrorSnackBar('خطأ في تسجيل الصوت: $e');
    }
  }

  Widget _buildImageSourceOption({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppColors.primaryPurple,
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(icon, color: AppColors.white, size: 30),
          ),
          const SizedBox(height: 8),
          Text(label, style: AppTextStyles.caption),
        ],
      ),
    );
  }

  Future<void> _handleImageSelected(XFile image) async {
    try {
      // إضافة رسالة بالصورة
      final imageMessage = MessageModel.create(
        content:
            '🖼️ تم إرفاق صورة: ${image.name}\n\nجاري تحليل الصورة بالذكاء الاصطناعي...',
        role: MessageRole.user,
      );

      setState(() {
        _conversation = _conversation.addMessage(imageMessage);
      });

      _showSuccessSnackBar('تم إرفاق الصورة بنجاح 🖼️');

      // محاولة تحليل الصورة
      await _analyzeImage(image);
    } catch (e) {
      _showErrorSnackBar('خطأ في معالجة الصورة: $e');
    }
  }

  Future<void> _analyzeImage(XFile image) async {
    try {
      // استخدام الخدمة الجديدة لتحليل الصور
      final analysisResult = await EnhancedAIService.analyzeImage(
        imagePath: image.path,
        conversationId: _conversation.id,
        customPrompt: 'قم بتحليل هذه الصورة وأخبرني ماذا ترى فيها بالتفصيل.',
      );

      setState(() {
        _conversation = _conversation.addMessage(analysisResult);
      });
    } catch (e) {
      final errorMessage = MessageModel.create(
        content: 'عذراً، حدث خطأ في تحليل الصورة: $e',
        role: MessageRole.assistant,
      );

      setState(() {
        _conversation = _conversation.addMessage(errorMessage);
      });
    }
  }

  void _showInfoDialog(String title, String content) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppColors.darkGrey,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Text(title, style: AppTextStyles.heading2),
            content: Text(content, style: AppTextStyles.body),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(
                  'حسناً',
                  style: AppTextStyles.button.copyWith(
                    color: AppColors.primaryPurple,
                  ),
                ),
              ),
            ],
          ),
    );
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      ),
    );
  }
}
