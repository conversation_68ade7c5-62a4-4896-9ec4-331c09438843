import 'package:flutter/material.dart';
import '../core/services/enhanced_ai_service.dart';
import '../core/services/real_tools_service.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';

/// مركز أدوات الذكاء الاصطناعي المحسن
class AIToolsHub {
  /// إنشاء صورة بالذكاء الاصطناعي
  static Future<String> generateImage({
    required String prompt,
    String size = '1024x1024',
    String quality = 'standard',
    String style = 'vivid',
  }) async {
    return await RealToolsService.generateImage(
      prompt: prompt,
      size: size,
      quality: quality,
      style: style,
    );
  }

  /// تحسين وصف الصورة
  static Future<String> enhanceImagePrompt(String originalPrompt) async {
    return await RealToolsService.enhanceImagePrompt(originalPrompt);
  }

  /// تلخيص النص المتقدم
  static Future<String> summarizeText({
    required String text,
    String style = 'comprehensive',
    int maxLength = 300,
  }) async {
    return await RealToolsService.summarizeText(
      text: text,
      style: style,
      maxLength: maxLength,
    );
  }

  /// تحليل البيانات المتقدم
  static Future<Map<String, dynamic>> analyzeData({
    required String data,
    String analysisType = 'comprehensive',
    bool includeCharts = false,
    bool includeRecommendations = true,
  }) async {
    return await RealToolsService.analyzeData(
      data: data,
      analysisType: analysisType,
      includeCharts: includeCharts,
      includeRecommendations: includeRecommendations,
    );
  }

  /// إنشاء خطة ذكية
  static Future<Map<String, dynamic>> createPlan({
    required String goal,
    String timeframe = 'شهر واحد',
    String difficulty = 'متوسط',
    List<String> constraints = const [],
    List<String> resources = const [],
  }) async {
    return await RealToolsService.createPlan(
      goal: goal,
      timeframe: timeframe,
      difficulty: difficulty,
      constraints: constraints,
      resources: resources,
    );
  }

  /// مساعدة في الكتابة المتقدمة
  static Future<Map<String, dynamic>> assistWriting({
    required String topic,
    String style = 'creative',
    String language = 'ar',
    int targetLength = 500,
    String? audience,
    String? tone,
  }) async {
    return await EnhancedAIService.advancedWritingAssist(
      topic: topic,
      style: style,
      language: language,
      targetLength: targetLength,
      audience: audience,
      tone: tone,
    );
  }

  /// محادثة ذكية
  static Future<dynamic> smartChat({
    required String message,
    required String conversationId,
    String model = 'gpt-3.5-turbo',
    double temperature = 0.7,
    int maxTokens = 2048,
    List<dynamic>? context,
  }) async {
    return await EnhancedAIService.createSmartChat(
      message: message,
      conversationId: conversationId,
      model: model,
      temperature: temperature,
      maxTokens: maxTokens,
      context: context,
    );
  }

  /// الحصول على قائمة بجميع الأدوات المتاحة
  static List<Map<String, dynamic>> getAvailableTools() {
    return [
      {
        'id': 'image_generation',
        'name': 'إنشاء الصور',
        'description': 'إنشاء صور فنية باستخدام الذكاء الاصطناعي',
        'icon': Icons.image,
        'color': AppColors.primaryPurple,
        'category': 'إبداعي',
      },
      {
        'id': 'text_summarization',
        'name': 'تلخيص النصوص',
        'description': 'تلخيص النصوص الطويلة بطريقة ذكية',
        'icon': Icons.summarize,
        'color': AppColors.accentBlue,
        'category': 'نصوص',
      },
      {
        'id': 'data_analysis',
        'name': 'تحليل البيانات',
        'description': 'تحليل البيانات واستخراج الرؤى',
        'icon': Icons.analytics,
        'color': AppColors.accentRed,
        'category': 'تحليل',
      },
      {
        'id': 'plan_creation',
        'name': 'إنشاء الخطط',
        'description': 'إنشاء خطط تفصيلية لتحقيق الأهداف',
        'icon': Icons.calendar_today,
        'color': AppColors.accentGreen,
        'category': 'تخطيط',
      },
      {
        'id': 'writing_assistance',
        'name': 'مساعدة الكتابة',
        'description': 'مساعدة في كتابة المحتوى بأساليب مختلفة',
        'icon': Icons.edit,
        'color': AppColors.accentOrange,
        'category': 'كتابة',
      },
      {
        'id': 'smart_chat',
        'name': 'المحادثة الذكية',
        'description': 'محادثة تفاعلية مع الذكاء الاصطناعي',
        'icon': Icons.chat,
        'color': AppColors.lightPurple,
        'category': 'محادثة',
      },
    ];
  }

  /// الحصول على إحصائيات الاستخدام
  static Map<String, dynamic> getUsageStats() {
    return EnhancedAIService.getUsageStats();
  }

  /// مسح الذاكرة المؤقتة
  static void clearCache() {
    EnhancedAIService.clearCache();
  }

  /// التحقق من توفر الأدوات
  static Future<Map<String, bool>> checkToolsAvailability() async {
    final tools = getAvailableTools();
    final availability = <String, bool>{};

    for (final tool in tools) {
      try {
        // محاولة بسيطة للتحقق من توفر كل أداة
        switch (tool['id']) {
          case 'image_generation':
            // التحقق من توفر مفتاح OpenAI للصور
            availability[tool['id']] = true; // يمكن تحسين هذا
            break;
          case 'text_summarization':
          case 'data_analysis':
          case 'plan_creation':
          case 'writing_assistance':
          case 'smart_chat':
            // التحقق من توفر مفاتيح API
            availability[tool['id']] = true; // يمكن تحسين هذا
            break;
          default:
            availability[tool['id']] = false;
        }
      } catch (e) {
        availability[tool['id']] = false;
      }
    }

    return availability;
  }

  /// الحصول على أمثلة للاستخدام
  static Map<String, List<String>> getUsageExamples() {
    return {
      'image_generation': [
        'منظر طبيعي خلاب لغروب الشمس على البحر',
        'قطة فضائية ترتدي بدلة رائد فضاء',
        'مدينة مستقبلية مع سيارات طائرة',
        'لوحة فنية بأسلوب فان جوخ',
      ],
      'text_summarization': [
        'مقال إخباري طويل',
        'بحث علمي أو ورقة أكاديمية',
        'تقرير أعمال مفصل',
        'محتوى تعليمي',
      ],
      'data_analysis': [
        'بيانات المبيعات الشهرية',
        'إحصائيات الموقع الإلكتروني',
        'نتائج الاستبيانات',
        'البيانات المالية',
      ],
      'plan_creation': [
        'خطة لتعلم لغة جديدة',
        'خطة لإنقاص الوزن',
        'خطة لبدء مشروع تجاري',
        'خطة للسفر والسياحة',
      ],
      'writing_assistance': [
        'مقال عن التكنولوجيا',
        'رسالة رسمية',
        'قصة قصيرة',
        'محتوى تسويقي',
      ],
      'smart_chat': [
        'أسئلة عامة',
        'طلب المساعدة في حل المشاكل',
        'النقاش حول موضوع معين',
        'طلب النصائح والإرشادات',
      ],
    };
  }
}
