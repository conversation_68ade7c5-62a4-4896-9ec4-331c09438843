import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../utils/app_colors.dart';
import '../widgets/modern_ui_components.dart';
import '../core/platform/platform_support_service.dart';
import '../core/platform/companion_app_service.dart';
import '../core/platform/platform_models.dart';

/// شاشة إدارة المنصات
class PlatformManagementScreen extends ConsumerStatefulWidget {
  const PlatformManagementScreen({super.key});

  @override
  ConsumerState<PlatformManagementScreen> createState() => _PlatformManagementScreenState();
}

class _PlatformManagementScreenState extends ConsumerState<PlatformManagementScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _initializeServices();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _initializeServices() async {
    setState(() => _isLoading = true);
    try {
      await PlatformSupportService.initialize();
      await CompanionAppService.initialize();
    } catch (e) {
      debugPrint('خطأ في تهيئة خدمات المنصات: $e');
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.darkGrey,
        title: Text(
          'إدارة المنصات',
          style: TextStyle(color: AppColors.white),
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.sync, color: AppColors.electricBlue),
            onPressed: _syncAllPlatforms,
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          labelColor: AppColors.primaryPurple,
          unselectedLabelColor: AppColors.lightGrey,
          indicatorColor: AppColors.primaryPurple,
          tabs: const [
            Tab(text: 'الأجهزة', icon: Icon(Icons.devices)),
            Tab(text: 'التطبيقات المصاحبة', icon: Icon(Icons.phone_android)),
            Tab(text: 'المزامنة', icon: Icon(Icons.sync)),
          ],
        ),
      ),
      body: _isLoading
          ? Center(child: ModernUIComponents.modernLoadingIndicator())
          : TabBarView(
              controller: _tabController,
              children: [
                _buildDevicesTab(),
                _buildCompanionAppsTab(),
                _buildSyncTab(),
              ],
            ),
    );
  }

  Widget _buildDevicesTab() {
    final connectedDevices = PlatformSupportService.getConnectedDevices();
    final currentPlatform = PlatformSupportService.currentPlatform;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCurrentPlatformCard(currentPlatform),
          const SizedBox(height: 24),
          _buildConnectedDevicesSection(connectedDevices),
          const SizedBox(height: 24),
          _buildAddDeviceSection(),
        ],
      ),
    );
  }

  Widget _buildCurrentPlatformCard(PlatformType platform) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: AppColors.primaryGradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  _getPlatformIcon(platform),
                  color: AppColors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المنصة الحالية',
                      style: TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      _getPlatformName(platform),
                      style: TextStyle(
                        color: AppColors.white,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                decoration: BoxDecoration(
                  color: AppColors.success.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(16),
                ),
                child: Text(
                  'نشط',
                  style: TextStyle(
                    color: AppColors.success,
                    fontSize: 12,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildPlatformFeatures(platform),
        ],
      ),
    );
  }

  Widget _buildPlatformFeatures(PlatformType platform) {
    final features = _getSupportedFeatures(platform);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الميزات المدعومة',
          style: TextStyle(
            color: AppColors.white,
            fontSize: 14,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: features.map((feature) => Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.primaryPurple.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              feature,
              style: TextStyle(
                color: AppColors.primaryPurple,
                fontSize: 10,
              ),
            ),
          )).toList(),
        ),
      ],
    );
  }

  Widget _buildConnectedDevicesSection(List<PlatformDevice> devices) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'الأجهزة المتصلة',
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                '${devices.length} جهاز',
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          if (devices.isEmpty)
            _buildEmptyDevicesState()
          else
            ...devices.map((device) => _buildDeviceCard(device)),
        ],
      ),
    );
  }

  Widget _buildEmptyDevicesState() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Icon(
            Icons.devices_other,
            size: 48,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 12),
          Text(
            'لا توجد أجهزة متصلة',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
          Text(
            'قم بإضافة أجهزة جديدة للمزامنة',
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceCard(PlatformDevice device) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: _getPlatformColor(device.platformType).withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(10),
            ),
            child: Icon(
              _getPlatformIcon(device.platformType),
              color: _getPlatformColor(device.platformType),
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  device.name,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  _getPlatformName(device.platformType),
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Container(
            width: 8,
            height: 8,
            decoration: BoxDecoration(
              color: device.isConnected ? AppColors.success : AppColors.error,
              shape: BoxShape.circle,
            ),
          ),
          const SizedBox(width: 8),
          IconButton(
            icon: Icon(
              Icons.more_vert,
              color: AppColors.textSecondary,
              size: 20,
            ),
            onPressed: () => _showDeviceOptions(device),
          ),
        ],
      ),
    );
  }

  Widget _buildAddDeviceSection() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إضافة جهاز جديد',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ModernUIComponents.modernButton(
            text: 'البحث عن أجهزة',
            onPressed: _searchForDevices,
            icon: Icons.search,
            width: double.infinity,
          ),
        ],
      ),
    );
  }

  Widget _buildCompanionAppsTab() {
    final pairedDevices = CompanionAppService.pairedDevices;
    final companionStatus = CompanionAppService.getCompanionStatus();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildCompanionStatusCard(companionStatus),
          const SizedBox(height: 24),
          _buildPairedDevicesSection(pairedDevices),
          const SizedBox(height: 24),
          _buildPairingSection(),
        ],
      ),
    );
  }

  Widget _buildCompanionStatusCard(CompanionStatus status) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'حالة التطبيقات المصاحبة',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatusItem(
                  'الأجهزة المقترنة',
                  '${status.pairedDevicesCount}',
                  Icons.link,
                  AppColors.primaryPurple,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatusItem(
                  'الرسائل الحديثة',
                  '${status.recentMessagesCount}',
                  Icons.message,
                  AppColors.electricBlue,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusItem(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: AppColors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 10,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildPairedDevicesSection(List<PairedDevice> devices) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'الأجهزة المقترنة',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          if (devices.isEmpty)
            _buildEmptyPairedDevicesState()
          else
            ...devices.map((device) => _buildPairedDeviceCard(device)),
        ],
      ),
    );
  }

  Widget _buildEmptyPairedDevicesState() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Icon(
            Icons.phone_android,
            size: 48,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 12),
          Text(
            'لا توجد أجهزة مقترنة',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPairedDeviceCard(PairedDevice device) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppColors.darkGrey.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Icon(
            _getPlatformIcon(device.platformType),
            color: AppColors.primaryPurple,
            size: 24,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  device.name,
                  style: TextStyle(
                    color: AppColors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  'آخر ظهور: ${_formatLastSeen(device.lastSeen)}',
                  style: TextStyle(
                    color: AppColors.textSecondary,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            icon: Icon(
              Icons.more_vert,
              color: AppColors.textSecondary,
              size: 20,
            ),
            onPressed: () => _showPairedDeviceOptions(device),
          ),
        ],
      ),
    );
  }

  Widget _buildPairingSection() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إقران جهاز جديد',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ModernUIComponents.modernButton(
            text: 'إنشاء رمز إقران',
            onPressed: _generatePairingCode,
            icon: Icons.qr_code,
            width: double.infinity,
          ),
        ],
      ),
    );
  }

  Widget _buildSyncTab() {
    final syncStatus = PlatformSupportService.syncStatus;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSyncStatusCard(syncStatus),
          const SizedBox(height: 24),
          _buildSyncActionsSection(),
        ],
      ),
    );
  }

  Widget _buildSyncStatusCard(PlatformSyncStatus status) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'حالة المزامنة',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Icon(
                status.isAutoSyncEnabled ? Icons.sync : Icons.sync_disabled,
                color: status.isAutoSyncEnabled ? AppColors.success : AppColors.error,
                size: 24,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      status.isAutoSyncEnabled ? 'المزامنة التلقائية مفعلة' : 'المزامنة التلقائية معطلة',
                      style: TextStyle(
                        color: AppColors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (status.lastSyncTime != null)
                      Text(
                        'آخر مزامنة: ${_formatLastSync(status.lastSyncTime!)}',
                        style: TextStyle(
                          color: AppColors.textSecondary,
                          fontSize: 12,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSyncActionsSection() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إجراءات المزامنة',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          ModernUIComponents.modernButton(
            text: 'مزامنة فورية',
            onPressed: _syncNow,
            icon: Icons.sync,
            width: double.infinity,
          ),
          const SizedBox(height: 12),
          ModernUIComponents.modernButton(
            text: 'إعدادات المزامنة',
            onPressed: _showSyncSettings,
            icon: Icons.settings,
            width: double.infinity,
            gradient: LinearGradient(
              colors: [AppColors.darkGrey, AppColors.midGrey],
            ),
          ),
        ],
      ),
    );
  }

  // Helper methods
  IconData _getPlatformIcon(PlatformType platform) {
    switch (platform) {
      case PlatformType.android:
        return Icons.android;
      case PlatformType.ios:
        return Icons.phone_iphone;
      case PlatformType.web:
        return Icons.web;
      case PlatformType.windows:
        return Icons.desktop_windows;
      case PlatformType.macos:
        return Icons.laptop_mac;
      case PlatformType.linux:
        return Icons.computer;
      case PlatformType.wearable:
        return Icons.watch;
      case PlatformType.tv:
        return Icons.tv;
      default:
        return Icons.devices;
    }
  }

  String _getPlatformName(PlatformType platform) {
    switch (platform) {
      case PlatformType.android:
        return 'أندرويد';
      case PlatformType.ios:
        return 'آيفون';
      case PlatformType.web:
        return 'الويب';
      case PlatformType.windows:
        return 'ويندوز';
      case PlatformType.macos:
        return 'ماك';
      case PlatformType.linux:
        return 'لينكس';
      case PlatformType.wearable:
        return 'ساعة ذكية';
      case PlatformType.tv:
        return 'تلفزيون ذكي';
      default:
        return 'جهاز محمول';
    }
  }

  Color _getPlatformColor(PlatformType platform) {
    switch (platform) {
      case PlatformType.android:
        return AppColors.success;
      case PlatformType.ios:
        return AppColors.info;
      case PlatformType.web:
        return AppColors.electricBlue;
      case PlatformType.windows:
        return AppColors.primaryPurple;
      case PlatformType.macos:
        return AppColors.lightPurple;
      case PlatformType.linux:
        return AppColors.warning;
      case PlatformType.wearable:
        return AppColors.glowPink;
      case PlatformType.tv:
        return AppColors.error;
      default:
        return AppColors.primaryPurple;
    }
  }

  List<String> _getSupportedFeatures(PlatformType platform) {
    switch (platform) {
      case PlatformType.web:
        return ['اختصارات لوحة المفاتيح', 'السحب والإفلات', 'الإشعارات'];
      case PlatformType.windows:
      case PlatformType.macos:
      case PlatformType.linux:
        return ['نوافذ متعددة', 'قوائم السياق', 'اختصارات النظام'];
      case PlatformType.android:
      case PlatformType.ios:
        return ['الاهتزاز', 'الإيماءات', 'الكاميرا', 'الموقع'];
      case PlatformType.wearable:
        return ['إشعارات سريعة', 'مراقبة الصحة'];
      case PlatformType.tv:
        return ['التحكم عن بعد', 'واجهة كبيرة'];
      default:
        return ['الميزات الأساسية'];
    }
  }

  String _formatLastSeen(DateTime lastSeen) {
    final now = DateTime.now();
    final difference = now.difference(lastSeen);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inMinutes < 60) {
      return '${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return '${difference.inHours} ساعة';
    } else {
      return '${difference.inDays} يوم';
    }
  }

  String _formatLastSync(DateTime lastSync) {
    final now = DateTime.now();
    final difference = now.difference(lastSync);
    
    if (difference.inMinutes < 1) {
      return 'منذ قليل';
    } else if (difference.inMinutes < 60) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inHours < 24) {
      return 'منذ ${difference.inHours} ساعة';
    } else {
      return 'منذ ${difference.inDays} يوم';
    }
  }

  // Action methods
  Future<void> _syncAllPlatforms() async {
    setState(() => _isLoading = true);
    try {
      await PlatformSupportService.syncAcrossAllPlatforms();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('تم مزامنة جميع المنصات بنجاح'),
          backgroundColor: AppColors.success,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _showDeviceOptions(PlatformDevice device) {
    // TODO: إظهار خيارات الجهاز
  }

  void _searchForDevices() {
    // TODO: البحث عن أجهزة جديدة
  }

  void _generatePairingCode() {
    final code = CompanionAppService.generatePairingCode();
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppColors.darkGrey,
        title: Text('رمز الإقران', style: TextStyle(color: AppColors.white)),
        content: Text(
          'استخدم هذا الرمز لإقران الجهاز:\n\n$code',
          style: TextStyle(color: AppColors.textSecondary),
          textAlign: TextAlign.center,
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('حسناً', style: TextStyle(color: AppColors.primaryPurple)),
          ),
        ],
      ),
    );
  }

  void _showPairedDeviceOptions(PairedDevice device) {
    // TODO: إظهار خيارات الجهاز المقترن
  }

  Future<void> _syncNow() async {
    await _syncAllPlatforms();
  }

  void _showSyncSettings() {
    // TODO: إظهار إعدادات المزامنة
  }
}
