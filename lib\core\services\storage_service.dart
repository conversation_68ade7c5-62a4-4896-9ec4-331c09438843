import 'package:hive_flutter/hive_flutter.dart';
// import 'package:shared_preferences/shared_preferences.dart';  // غير مستخدم
import '../models/conversation_model.dart';
import '../models/user_model.dart';

/// خدمة التخزين المحلي المتقدمة
class StorageService {
  static const String _conversationsBox = 'conversations';
  static const String _userBox = 'user';
  static const String _settingsBox = 'settings';
  static const String _cacheBox = 'cache';

  static bool _isInitialized = false;

  /// تهيئة خدمة التخزين
  static Future<void> initialize() async {
    if (_isInitialized) return;

    await Hive.initFlutter();

    // تسجيل المحولات
    _registerAdapters();

    // فتح الصناديق
    await _openBoxes();

    _isInitialized = true;
  }

  /// تسجيل محولات Hive
  static void _registerAdapters() {
    if (!Hive.isAdapterRegistered(0)) {
      Hive.registerAdapter(ConversationModelAdapter());
    }
    if (!Hive.isAdapterRegistered(1)) {
      Hive.registerAdapter(MessageModelAdapter());
    }
    if (!Hive.isAdapterRegistered(2)) {
      Hive.registerAdapter(UserModelAdapter());
    }
    if (!Hive.isAdapterRegistered(3)) {
      Hive.registerAdapter(MessageRoleAdapter());
    }
    if (!Hive.isAdapterRegistered(4)) {
      Hive.registerAdapter(MessageStatusAdapter());
    }
    if (!Hive.isAdapterRegistered(5)) {
      Hive.registerAdapter(ConversationTypeAdapter());
    }
  }

  /// فتح جميع الصناديق
  static Future<void> _openBoxes() async {
    await Future.wait([
      Hive.openBox<ConversationModel>(_conversationsBox),
      Hive.openBox<UserModel>(_userBox),
      Hive.openBox(_settingsBox),
      Hive.openBox(_cacheBox),
    ]);
  }

  /// الحصول على صندوق المحادثات
  static Box<ConversationModel> get _conversationsBoxInstance =>
      Hive.box<ConversationModel>(_conversationsBox);

  /// الحصول على صندوق المستخدم
  static Box<UserModel> get _userBoxInstance =>
      Hive.box<UserModel>(_userBox);

  /// الحصول على صندوق الإعدادات
  static Box get _settingsBoxInstance => Hive.box(_settingsBox);

  /// الحصول على صندوق التخزين المؤقت
  static Box get _cacheBoxInstance => Hive.box(_cacheBox);

  // ==================== المحادثات ====================

  /// حفظ محادثة
  static Future<void> saveConversation(ConversationModel conversation) async {
    await _conversationsBoxInstance.put(conversation.id, conversation);
  }

  /// الحصول على محادثة
  static ConversationModel? getConversation(String id) {
    return _conversationsBoxInstance.get(id);
  }

  /// الحصول على جميع المحادثات
  static List<ConversationModel> getAllConversations() {
    return _conversationsBoxInstance.values.toList()
      ..sort((a, b) => b.updatedAt.compareTo(a.updatedAt));
  }

  /// البحث في المحادثات
  static List<ConversationModel> searchConversations(String query) {
    final allConversations = getAllConversations();
    return allConversations.where((conversation) {
      return conversation.title.toLowerCase().contains(query.toLowerCase()) ||
             conversation.messages.any((message) =>
                 message.content.toLowerCase().contains(query.toLowerCase()));
    }).toList();
  }

  /// حذف محادثة
  static Future<void> deleteConversation(String id) async {
    await _conversationsBoxInstance.delete(id);
  }

  /// حذف جميع المحادثات
  static Future<void> deleteAllConversations() async {
    await _conversationsBoxInstance.clear();
  }

  /// الحصول على إحصائيات المحادثات
  static Map<String, dynamic> getConversationStats() {
    final conversations = getAllConversations();
    final totalMessages = conversations.fold<int>(
      0,
      (sum, conversation) => sum + conversation.messages.length,
    );

    return {
      'total_conversations': conversations.length,
      'total_messages': totalMessages,
      'average_messages_per_conversation':
          conversations.isNotEmpty ? totalMessages / conversations.length : 0,
      'most_recent_conversation': conversations.isNotEmpty
          ? conversations.first.updatedAt.toIso8601String()
          : null,
    };
  }

  // ==================== المستخدم ====================

  /// حفظ بيانات المستخدم
  static Future<void> saveUser(UserModel user) async {
    await _userBoxInstance.put('current_user', user);
  }

  /// الحصول على المستخدم الحالي
  static UserModel? getCurrentUser() {
    return _userBoxInstance.get('current_user');
  }

  /// تحديث إحصائيات المستخدم
  static Future<void> updateUserStats(Map<String, dynamic> updates) async {
    final currentUser = getCurrentUser();
    if (currentUser != null) {
      final updatedStats = currentUser.stats.copyWith(
        totalConversations: updates['total_conversations'] ?? currentUser.stats.totalConversations,
        totalMessages: updates['total_messages'] ?? currentUser.stats.totalMessages,
        totalTokensUsed: updates['total_tokens_used'] ?? currentUser.stats.totalTokensUsed,
        totalImagesGenerated: updates['total_images_generated'] ?? currentUser.stats.totalImagesGenerated,
        totalTextsAnalyzed: updates['total_texts_analyzed'] ?? currentUser.stats.totalTextsAnalyzed,
        totalPlansCreated: updates['total_plans_created'] ?? currentUser.stats.totalPlansCreated,
        lastActivityAt: DateTime.now(),
      );

      final updatedUser = currentUser.copyWith(stats: updatedStats);
      await saveUser(updatedUser);
    }
  }

  /// حذف بيانات المستخدم
  static Future<void> deleteUser() async {
    await _userBoxInstance.clear();
  }

  // ==================== الإعدادات ====================

  /// حفظ إعداد
  static Future<void> saveSetting(String key, dynamic value) async {
    await _settingsBoxInstance.put(key, value);
  }

  /// الحصول على إعداد
  static T? getSetting<T>(String key, {T? defaultValue}) {
    return _settingsBoxInstance.get(key, defaultValue: defaultValue) as T?;
  }

  /// حذف إعداد
  static Future<void> deleteSetting(String key) async {
    await _settingsBoxInstance.delete(key);
  }

  /// الحصول على جميع الإعدادات
  static Map<String, dynamic> getAllSettings() {
    return Map<String, dynamic>.from(_settingsBoxInstance.toMap());
  }

  /// حذف جميع الإعدادات
  static Future<void> deleteAllSettings() async {
    await _settingsBoxInstance.clear();
  }

  // ==================== التخزين المؤقت ====================

  /// حفظ في التخزين المؤقت
  static Future<void> cacheData(String key, dynamic data, {Duration? expiry}) async {
    final cacheItem = {
      'data': data,
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'expiry': expiry?.inMilliseconds,
    };
    await _cacheBoxInstance.put(key, cacheItem);
  }

  /// الحصول من التخزين المؤقت
  static T? getCachedData<T>(String key) {
    final cacheItem = _cacheBoxInstance.get(key);
    if (cacheItem == null) return null;

    final timestamp = cacheItem['timestamp'] as int;
    final expiry = cacheItem['expiry'] as int?;

    if (expiry != null) {
      final expiryTime = timestamp + expiry;
      if (DateTime.now().millisecondsSinceEpoch > expiryTime) {
        // البيانات منتهية الصلاحية
        _cacheBoxInstance.delete(key);
        return null;
      }
    }

    return cacheItem['data'] as T?;
  }

  /// حذف من التخزين المؤقت
  static Future<void> deleteCachedData(String key) async {
    await _cacheBoxInstance.delete(key);
  }

  /// مسح التخزين المؤقت المنتهي الصلاحية
  static Future<void> clearExpiredCache() async {
    final now = DateTime.now().millisecondsSinceEpoch;
    final keysToDelete = <String>[];

    for (final key in _cacheBoxInstance.keys) {
      final cacheItem = _cacheBoxInstance.get(key);
      if (cacheItem != null && cacheItem is Map) {
        final timestamp = cacheItem['timestamp'] as int?;
        final expiry = cacheItem['expiry'] as int?;

        if (timestamp != null && expiry != null) {
          final expiryTime = timestamp + expiry;
          if (now > expiryTime) {
            keysToDelete.add(key.toString());
          }
        }
      }
    }

    for (final key in keysToDelete) {
      await _cacheBoxInstance.delete(key);
    }
  }

  /// مسح جميع البيانات المؤقتة
  static Future<void> clearAllCache() async {
    await _cacheBoxInstance.clear();
  }

  // ==================== النسخ الاحتياطي والاستعادة ====================

  /// إنشاء نسخة احتياطية
  static Future<Map<String, dynamic>> createBackup() async {
    return {
      'conversations': getAllConversations().map((c) => c.toJson()).toList(),
      'user': getCurrentUser()?.toJson(),
      'settings': getAllSettings(),
      'timestamp': DateTime.now().toIso8601String(),
      'version': '1.0',
    };
  }

  /// استعادة من النسخة الاحتياطية
  static Future<void> restoreFromBackup(Map<String, dynamic> backup) async {
    try {
      // استعادة المحادثات
      if (backup['conversations'] != null) {
        await deleteAllConversations();
        final conversations = backup['conversations'] as List;
        for (final conversationJson in conversations) {
          final conversation = ConversationModel.fromJson(conversationJson);
          await saveConversation(conversation);
        }
      }

      // استعادة المستخدم
      if (backup['user'] != null) {
        final user = UserModel.fromJson(backup['user']);
        await saveUser(user);
      }

      // استعادة الإعدادات
      if (backup['settings'] != null) {
        await deleteAllSettings();
        final settings = backup['settings'] as Map<String, dynamic>;
        for (final entry in settings.entries) {
          await saveSetting(entry.key, entry.value);
        }
      }
    } catch (e) {
      throw Exception('فشل في استعادة النسخة الاحتياطية: $e');
    }
  }

  // ==================== الصيانة ====================

  /// ضغط قاعدة البيانات
  static Future<void> compactDatabase() async {
    await _conversationsBoxInstance.compact();
    await _userBoxInstance.compact();
    await _settingsBoxInstance.compact();
    await _cacheBoxInstance.compact();
  }

  /// الحصول على حجم قاعدة البيانات
  static Future<Map<String, int>> getDatabaseSize() async {
    return {
      'conversations': _conversationsBoxInstance.length,
      'user': _userBoxInstance.length,
      'settings': _settingsBoxInstance.length,
      'cache': _cacheBoxInstance.length,
    };
  }

  /// تنظيف قاعدة البيانات
  static Future<void> cleanupDatabase() async {
    // مسح التخزين المؤقت المنتهي الصلاحية
    await clearExpiredCache();

    // ضغط قاعدة البيانات
    await compactDatabase();

    // حذف المحادثات القديمة (أكثر من 6 أشهر)
    final sixMonthsAgo = DateTime.now().subtract(const Duration(days: 180));
    final conversations = getAllConversations();

    for (final conversation in conversations) {
      if (conversation.updatedAt.isBefore(sixMonthsAgo)) {
        await deleteConversation(conversation.id);
      }
    }
  }

  /// إغلاق جميع الصناديق
  static Future<void> close() async {
    await Hive.close();
    _isInitialized = false;
  }
}

// ==================== محولات Hive ====================

class ConversationModelAdapter extends TypeAdapter<ConversationModel> {
  @override
  final int typeId = 0;

  @override
  ConversationModel read(BinaryReader reader) {
    return ConversationModel.fromJson(Map<String, dynamic>.from(reader.readMap()));
  }

  @override
  void write(BinaryWriter writer, ConversationModel obj) {
    writer.writeMap(obj.toJson());
  }
}

class MessageModelAdapter extends TypeAdapter<MessageModel> {
  @override
  final int typeId = 1;

  @override
  MessageModel read(BinaryReader reader) {
    return MessageModel.fromJson(Map<String, dynamic>.from(reader.readMap()));
  }

  @override
  void write(BinaryWriter writer, MessageModel obj) {
    writer.writeMap(obj.toJson());
  }
}

class UserModelAdapter extends TypeAdapter<UserModel> {
  @override
  final int typeId = 2;

  @override
  UserModel read(BinaryReader reader) {
    return UserModel.fromJson(Map<String, dynamic>.from(reader.readMap()));
  }

  @override
  void write(BinaryWriter writer, UserModel obj) {
    writer.writeMap(obj.toJson());
  }
}

class MessageRoleAdapter extends TypeAdapter<MessageRole> {
  @override
  final int typeId = 3;

  @override
  MessageRole read(BinaryReader reader) {
    return MessageRole.values[reader.readByte()];
  }

  @override
  void write(BinaryWriter writer, MessageRole obj) {
    writer.writeByte(obj.index);
  }
}

class MessageStatusAdapter extends TypeAdapter<MessageStatus> {
  @override
  final int typeId = 4;

  @override
  MessageStatus read(BinaryReader reader) {
    return MessageStatus.values[reader.readByte()];
  }

  @override
  void write(BinaryWriter writer, MessageStatus obj) {
    writer.writeByte(obj.index);
  }
}

class ConversationTypeAdapter extends TypeAdapter<ConversationType> {
  @override
  final int typeId = 5;

  @override
  ConversationType read(BinaryReader reader) {
    return ConversationType.values[reader.readByte()];
  }

  @override
  void write(BinaryWriter writer, ConversationType obj) {
    writer.writeByte(obj.index);
  }
}
