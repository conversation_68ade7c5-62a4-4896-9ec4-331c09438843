import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import '../storage/storage_service.dart';

/// خدمة الإشعارات المتقدمة
class AdvancedNotificationService {
  static const String _notificationSettingsKey = 'notification_settings';
  static const String _scheduledNotificationsKey = 'scheduled_notifications';
  
  static FlutterLocalNotificationsPlugin? _flutterLocalNotificationsPlugin;
  static bool _isInitialized = false;
  static NotificationSettings _settings = NotificationSettings.defaultSettings();
  static List<ScheduledNotification> _scheduledNotifications = [];

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      _flutterLocalNotificationsPlugin = FlutterLocalNotificationsPlugin();

      // إعدادات Android
      const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
      
      // إعدادات iOS
      const iosSettings = DarwinInitializationSettings(
        requestAlertPermission: true,
        requestBadgePermission: true,
        requestSoundPermission: true,
      );

      const initializationSettings = InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      );

      await _flutterLocalNotificationsPlugin!.initialize(
        initializationSettings,
        onDidReceiveNotificationResponse: _onNotificationTapped,
      );

      // تحميل الإعدادات
      await _loadSettings();
      await _loadScheduledNotifications();

      // طلب الأذونات
      await _requestPermissions();

      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة الإشعارات المتقدمة');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الإشعارات: $e');
    }
  }

  /// طلب أذونات الإشعارات
  static Future<bool> _requestPermissions() async {
    if (_flutterLocalNotificationsPlugin == null) return false;

    final androidPlugin = _flutterLocalNotificationsPlugin!
        .resolvePlatformSpecificImplementation<AndroidFlutterLocalNotificationsPlugin>();
    
    if (androidPlugin != null) {
      final granted = await androidPlugin.requestNotificationsPermission();
      debugPrint('🔔 أذونات الإشعارات: ${granted ? "مُمنوحة" : "مرفوضة"}');
      return granted ?? false;
    }

    return true; // iOS يطلب الأذونات تلقائياً
  }

  /// معالجة النقر على الإشعار
  static void _onNotificationTapped(NotificationResponse response) {
    debugPrint('👆 تم النقر على الإشعار: ${response.payload}');
    
    // يمكن إضافة منطق التنقل هنا
    if (response.payload != null) {
      _handleNotificationPayload(response.payload!);
    }
  }

  /// معالجة محتوى الإشعار
  static void _handleNotificationPayload(String payload) {
    try {
      // يمكن إضافة منطق معالجة البيانات هنا
      debugPrint('📦 معالجة محتوى الإشعار: $payload');
    } catch (e) {
      debugPrint('❌ خطأ في معالجة محتوى الإشعار: $e');
    }
  }

  /// إرسال إشعار فوري
  static Future<void> showInstantNotification({
    required String title,
    required String body,
    String? payload,
    NotificationPriority priority = NotificationPriority.normal,
    NotificationCategory category = NotificationCategory.general,
  }) async {
    if (!_isInitialized || _flutterLocalNotificationsPlugin == null) {
      debugPrint('⚠️ خدمة الإشعارات غير مهيأة');
      return;
    }

    if (!_settings.enabled || !_shouldShowNotification(category)) {
      debugPrint('🔕 الإشعارات معطلة لهذه الفئة: $category');
      return;
    }

    try {
      final id = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      
      final androidDetails = AndroidNotificationDetails(
        'deepseek_${category.name}',
        _getCategoryDisplayName(category),
        channelDescription: 'إشعارات ${_getCategoryDisplayName(category)}',
        importance: _getAndroidImportance(priority),
        priority: _getAndroidPriority(priority),
        icon: '@mipmap/ic_launcher',
        color: const Color(0xFF6C5CE7),
        enableVibration: _settings.vibration,
        playSound: _settings.sound,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _flutterLocalNotificationsPlugin!.show(
        id,
        title,
        body,
        notificationDetails,
        payload: payload,
      );

      debugPrint('📢 تم إرسال الإشعار: $title');
    } catch (e) {
      debugPrint('❌ خطأ في إرسال الإشعار: $e');
    }
  }

  /// جدولة إشعار
  static Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledTime,
    String? payload,
    NotificationPriority priority = NotificationPriority.normal,
    NotificationCategory category = NotificationCategory.general,
    bool repeat = false,
    RepeatInterval? repeatInterval,
  }) async {
    if (!_isInitialized || _flutterLocalNotificationsPlugin == null) {
      debugPrint('⚠️ خدمة الإشعارات غير مهيأة');
      return;
    }

    if (!_settings.enabled || !_shouldShowNotification(category)) {
      debugPrint('🔕 الإشعارات معطلة لهذه الفئة: $category');
      return;
    }

    try {
      final id = DateTime.now().millisecondsSinceEpoch ~/ 1000;
      
      final androidDetails = AndroidNotificationDetails(
        'deepseek_scheduled_${category.name}',
        'إشعارات مجدولة - ${_getCategoryDisplayName(category)}',
        channelDescription: 'إشعارات مجدولة لـ ${_getCategoryDisplayName(category)}',
        importance: _getAndroidImportance(priority),
        priority: _getAndroidPriority(priority),
        icon: '@mipmap/ic_launcher',
        color: const Color(0xFF6C5CE7),
        enableVibration: _settings.vibration,
        playSound: _settings.sound,
      );

      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      const notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      if (repeat && repeatInterval != null) {
        await _flutterLocalNotificationsPlugin!.periodicallyShow(
          id,
          title,
          body,
          repeatInterval,
          notificationDetails,
          payload: payload,
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
        );
      } else {
        await _flutterLocalNotificationsPlugin!.zonedSchedule(
          id,
          title,
          body,
          _convertToTZDateTime(scheduledTime),
          notificationDetails,
          payload: payload,
          androidScheduleMode: AndroidScheduleMode.exactAllowWhileIdle,
          uiLocalNotificationDateInterpretation:
              UILocalNotificationDateInterpretation.absoluteTime,
        );
      }

      // حفظ الإشعار المجدول
      final scheduledNotification = ScheduledNotification(
        id: id,
        title: title,
        body: body,
        scheduledTime: scheduledTime,
        category: category,
        repeat: repeat,
        repeatInterval: repeatInterval,
      );

      _scheduledNotifications.add(scheduledNotification);
      await _saveScheduledNotifications();

      debugPrint('⏰ تم جدولة الإشعار: $title في ${scheduledTime.toString()}');
    } catch (e) {
      debugPrint('❌ خطأ في جدولة الإشعار: $e');
    }
  }

  /// إلغاء إشعار مجدول
  static Future<void> cancelScheduledNotification(int id) async {
    if (_flutterLocalNotificationsPlugin == null) return;

    try {
      await _flutterLocalNotificationsPlugin!.cancel(id);
      
      _scheduledNotifications.removeWhere((notification) => notification.id == id);
      await _saveScheduledNotifications();
      
      debugPrint('❌ تم إلغاء الإشعار المجدول: $id');
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء الإشعار: $e');
    }
  }

  /// إلغاء جميع الإشعارات
  static Future<void> cancelAllNotifications() async {
    if (_flutterLocalNotificationsPlugin == null) return;

    try {
      await _flutterLocalNotificationsPlugin!.cancelAll();
      _scheduledNotifications.clear();
      await _saveScheduledNotifications();
      debugPrint('🧹 تم إلغاء جميع الإشعارات');
    } catch (e) {
      debugPrint('❌ خطأ في إلغاء جميع الإشعارات: $e');
    }
  }

  /// إشعارات خاصة بالذكاء الاصطناعي
  static Future<void> showAINotification({
    required String title,
    required String body,
    AINotificationType type = AINotificationType.taskComplete,
  }) async {
    String emoji = '';
    NotificationCategory category = NotificationCategory.ai;
    
    switch (type) {
      case AINotificationType.taskComplete:
        emoji = '✅';
        break;
      case AINotificationType.taskFailed:
        emoji = '❌';
        break;
      case AINotificationType.newFeature:
        emoji = '🆕';
        break;
      case AINotificationType.reminder:
        emoji = '⏰';
        break;
      case AINotificationType.update:
        emoji = '🔄';
        break;
    }

    await showInstantNotification(
      title: '$emoji $title',
      body: body,
      category: category,
      priority: type == AINotificationType.taskFailed 
          ? NotificationPriority.high 
          : NotificationPriority.normal,
    );
  }

  /// تحديث إعدادات الإشعارات
  static Future<void> updateSettings(NotificationSettings newSettings) async {
    _settings = newSettings;
    await _saveSettings();
    debugPrint('⚙️ تم تحديث إعدادات الإشعارات');
  }

  /// الحصول على الإعدادات الحالية
  static NotificationSettings get settings => _settings;

  /// الحصول على الإشعارات المجدولة
  static List<ScheduledNotification> get scheduledNotifications => 
      List.unmodifiable(_scheduledNotifications);

  /// حفظ الإعدادات
  static Future<void> _saveSettings() async {
    await StorageService.saveData(_notificationSettingsKey, _settings.toMap());
  }

  /// تحميل الإعدادات
  static Future<void> _loadSettings() async {
    try {
      final data = await StorageService.getData(_notificationSettingsKey);
      if (data != null) {
        _settings = NotificationSettings.fromMap(data);
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل إعدادات الإشعارات: $e');
    }
  }

  /// حفظ الإشعارات المجدولة
  static Future<void> _saveScheduledNotifications() async {
    final data = _scheduledNotifications.map((n) => n.toMap()).toList();
    await StorageService.saveData(_scheduledNotificationsKey, data);
  }

  /// تحميل الإشعارات المجدولة
  static Future<void> _loadScheduledNotifications() async {
    try {
      final data = await StorageService.getData(_scheduledNotificationsKey);
      if (data != null && data is List) {
        _scheduledNotifications = data
            .map((item) => ScheduledNotification.fromMap(item))
            .toList();
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الإشعارات المجدولة: $e');
    }
  }

  /// التحقق من إمكانية إظهار الإشعار
  static bool _shouldShowNotification(NotificationCategory category) {
    switch (category) {
      case NotificationCategory.ai:
        return _settings.aiNotifications;
      case NotificationCategory.system:
        return _settings.systemNotifications;
      case NotificationCategory.reminder:
        return _settings.reminderNotifications;
      case NotificationCategory.general:
        return true;
    }
  }

  /// الحصول على اسم الفئة
  static String _getCategoryDisplayName(NotificationCategory category) {
    switch (category) {
      case NotificationCategory.ai:
        return 'الذكاء الاصطناعي';
      case NotificationCategory.system:
        return 'النظام';
      case NotificationCategory.reminder:
        return 'التذكيرات';
      case NotificationCategory.general:
        return 'عام';
    }
  }

  /// تحويل الأولوية إلى Android
  static Importance _getAndroidImportance(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Importance.low;
      case NotificationPriority.normal:
        return Importance.defaultImportance;
      case NotificationPriority.high:
        return Importance.high;
      case NotificationPriority.urgent:
        return Importance.max;
    }
  }

  static Priority _getAndroidPriority(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Priority.low;
      case NotificationPriority.normal:
        return Priority.defaultPriority;
      case NotificationPriority.high:
        return Priority.high;
      case NotificationPriority.urgent:
        return Priority.max;
    }
  }

  /// تحويل DateTime إلى TZDateTime
  static dynamic _convertToTZDateTime(DateTime dateTime) {
    // في التطبيق الحقيقي، يجب استخدام timezone package
    return dateTime;
  }
}

/// إعدادات الإشعارات
class NotificationSettings {
  final bool enabled;
  final bool sound;
  final bool vibration;
  final bool aiNotifications;
  final bool systemNotifications;
  final bool reminderNotifications;

  NotificationSettings({
    required this.enabled,
    required this.sound,
    required this.vibration,
    required this.aiNotifications,
    required this.systemNotifications,
    required this.reminderNotifications,
  });

  static NotificationSettings defaultSettings() {
    return NotificationSettings(
      enabled: true,
      sound: true,
      vibration: true,
      aiNotifications: true,
      systemNotifications: true,
      reminderNotifications: true,
    );
  }

  NotificationSettings copyWith({
    bool? enabled,
    bool? sound,
    bool? vibration,
    bool? aiNotifications,
    bool? systemNotifications,
    bool? reminderNotifications,
  }) {
    return NotificationSettings(
      enabled: enabled ?? this.enabled,
      sound: sound ?? this.sound,
      vibration: vibration ?? this.vibration,
      aiNotifications: aiNotifications ?? this.aiNotifications,
      systemNotifications: systemNotifications ?? this.systemNotifications,
      reminderNotifications: reminderNotifications ?? this.reminderNotifications,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'enabled': enabled,
      'sound': sound,
      'vibration': vibration,
      'aiNotifications': aiNotifications,
      'systemNotifications': systemNotifications,
      'reminderNotifications': reminderNotifications,
    };
  }

  factory NotificationSettings.fromMap(Map<String, dynamic> map) {
    return NotificationSettings(
      enabled: map['enabled'] ?? true,
      sound: map['sound'] ?? true,
      vibration: map['vibration'] ?? true,
      aiNotifications: map['aiNotifications'] ?? true,
      systemNotifications: map['systemNotifications'] ?? true,
      reminderNotifications: map['reminderNotifications'] ?? true,
    );
  }
}

/// إشعار مجدول
class ScheduledNotification {
  final int id;
  final String title;
  final String body;
  final DateTime scheduledTime;
  final NotificationCategory category;
  final bool repeat;
  final RepeatInterval? repeatInterval;

  ScheduledNotification({
    required this.id,
    required this.title,
    required this.body,
    required this.scheduledTime,
    required this.category,
    required this.repeat,
    this.repeatInterval,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'body': body,
      'scheduledTime': scheduledTime.toIso8601String(),
      'category': category.toString(),
      'repeat': repeat,
      'repeatInterval': repeatInterval?.toString(),
    };
  }

  factory ScheduledNotification.fromMap(Map<String, dynamic> map) {
    return ScheduledNotification(
      id: map['id'] ?? 0,
      title: map['title'] ?? '',
      body: map['body'] ?? '',
      scheduledTime: DateTime.parse(map['scheduledTime']),
      category: NotificationCategory.values.firstWhere(
        (e) => e.toString() == map['category'],
        orElse: () => NotificationCategory.general,
      ),
      repeat: map['repeat'] ?? false,
      repeatInterval: map['repeatInterval'] != null
          ? RepeatInterval.values.firstWhere(
              (e) => e.toString() == map['repeatInterval'],
              orElse: () => RepeatInterval.daily,
            )
          : null,
    );
  }
}

/// فئات الإشعارات
enum NotificationCategory {
  general,
  ai,
  system,
  reminder,
}

/// أولوية الإشعارات
enum NotificationPriority {
  low,
  normal,
  high,
  urgent,
}

/// أنواع إشعارات الذكاء الاصطناعي
enum AINotificationType {
  taskComplete,
  taskFailed,
  newFeature,
  reminder,
  update,
}
