import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:io';

/// خدمة تحويل النص إلى كلام
class TextToSpeechService {
  static const MethodChannel _channel = MethodChannel('text_to_speech');
  static bool _isInitialized = false;
  static bool _isSpeaking = false;
  static double _speechRate = 0.5;
  static double _pitch = 1.0;
  static String _language = 'ar-SA'; // العربية السعودية

  /// تهيئة الخدمة
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      if (Platform.isAndroid || Platform.isIOS) {
        await _channel.invokeMethod('initialize');
      }
      _isInitialized = true;
      debugPrint('✅ تم تهيئة خدمة تحويل النص إلى كلام');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة TTS: $e');
      // استخدام الخدمة الافتراضية للويب
      _isInitialized = true;
    }
  }

  /// تحويل النص إلى كلام
  static Future<void> speak(String text) async {
    if (!_isInitialized) await initialize();
    if (text.trim().isEmpty) return;

    try {
      _isSpeaking = true;

      if (kIsWeb) {
        // استخدام Web Speech API للويب
        await _speakWeb(text);
      } else if (Platform.isAndroid || Platform.isIOS) {
        // استخدام المكونات الأصلية للموبايل
        await _channel.invokeMethod('speak', {
          'text': text,
          'language': _language,
          'rate': _speechRate,
          'pitch': _pitch,
        });
      } else {
        // للمنصات الأخرى (Windows, macOS, Linux)
        await _speakDesktop(text);
      }

      debugPrint('🔊 تم تشغيل النص: ${text.substring(0, text.length > 50 ? 50 : text.length)}...');
    } catch (e) {
      debugPrint('❌ خطأ في تحويل النص إلى كلام: $e');
      _isSpeaking = false;
      rethrow;
    }
  }

  /// إيقاف الكلام
  static Future<void> stop() async {
    if (!_isInitialized) return;

    try {
      if (kIsWeb) {
        await _stopWeb();
      } else if (Platform.isAndroid || Platform.isIOS) {
        await _channel.invokeMethod('stop');
      } else {
        await _stopDesktop();
      }

      _isSpeaking = false;
      debugPrint('⏹️ تم إيقاف الكلام');
    } catch (e) {
      debugPrint('❌ خطأ في إيقاف الكلام: $e');
    }
  }

  /// إيقاف مؤقت
  static Future<void> pause() async {
    if (!_isInitialized || !_isSpeaking) return;

    try {
      if (Platform.isAndroid || Platform.isIOS) {
        await _channel.invokeMethod('pause');
      }
      debugPrint('⏸️ تم إيقاف الكلام مؤقتاً');
    } catch (e) {
      debugPrint('❌ خطأ في الإيقاف المؤقت: $e');
    }
  }

  /// استئناف الكلام
  static Future<void> resume() async {
    if (!_isInitialized) return;

    try {
      if (Platform.isAndroid || Platform.isIOS) {
        await _channel.invokeMethod('resume');
      }
      debugPrint('▶️ تم استئناف الكلام');
    } catch (e) {
      debugPrint('❌ خطأ في استئناف الكلام: $e');
    }
  }

  /// تعيين سرعة الكلام (0.1 - 2.0)
  static Future<void> setSpeechRate(double rate) async {
    if (rate < 0.1 || rate > 2.0) {
      throw ArgumentError('سرعة الكلام يجب أن تكون بين 0.1 و 2.0');
    }

    _speechRate = rate;
    
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        await _channel.invokeMethod('setSpeechRate', {'rate': rate});
      }
      debugPrint('🎛️ تم تعيين سرعة الكلام: $rate');
    } catch (e) {
      debugPrint('❌ خطأ في تعيين سرعة الكلام: $e');
    }
  }

  /// تعيين نبرة الصوت (0.5 - 2.0)
  static Future<void> setPitch(double pitch) async {
    if (pitch < 0.5 || pitch > 2.0) {
      throw ArgumentError('نبرة الصوت يجب أن تكون بين 0.5 و 2.0');
    }

    _pitch = pitch;
    
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        await _channel.invokeMethod('setPitch', {'pitch': pitch});
      }
      debugPrint('🎵 تم تعيين نبرة الصوت: $pitch');
    } catch (e) {
      debugPrint('❌ خطأ في تعيين نبرة الصوت: $e');
    }
  }

  /// تعيين اللغة
  static Future<void> setLanguage(String language) async {
    _language = language;
    
    try {
      if (Platform.isAndroid || Platform.isIOS) {
        await _channel.invokeMethod('setLanguage', {'language': language});
      }
      debugPrint('🌐 تم تعيين اللغة: $language');
    } catch (e) {
      debugPrint('❌ خطأ في تعيين اللغة: $e');
    }
  }

  /// الحصول على اللغات المتاحة
  static Future<List<String>> getAvailableLanguages() async {
    if (!_isInitialized) await initialize();

    try {
      if (Platform.isAndroid || Platform.isIOS) {
        final result = await _channel.invokeMethod('getAvailableLanguages');
        return List<String>.from(result);
      } else {
        // قائمة افتراضية للمنصات الأخرى
        return [
          'ar-SA', // العربية السعودية
          'ar-EG', // العربية المصرية
          'en-US', // الإنجليزية الأمريكية
          'en-GB', // الإنجليزية البريطانية
          'fr-FR', // الفرنسية
          'de-DE', // الألمانية
          'es-ES', // الإسبانية
        ];
      }
    } catch (e) {
      debugPrint('❌ خطأ في الحصول على اللغات: $e');
      return ['ar-SA', 'en-US'];
    }
  }

  /// التحقق من حالة الكلام
  static bool get isSpeaking => _isSpeaking;

  /// الحصول على سرعة الكلام الحالية
  static double get speechRate => _speechRate;

  /// الحصول على نبرة الصوت الحالية
  static double get pitch => _pitch;

  /// الحصول على اللغة الحالية
  static String get language => _language;

  /// تحويل النص إلى كلام للويب
  static Future<void> _speakWeb(String text) async {
    // محاكاة تحويل النص إلى كلام للويب
    // في التطبيق الحقيقي، يمكن استخدام Web Speech API
    await Future.delayed(Duration(milliseconds: text.length * 50));
    _isSpeaking = false;
  }

  /// إيقاف الكلام للويب
  static Future<void> _stopWeb() async {
    // محاكاة إيقاف الكلام للويب
    _isSpeaking = false;
  }

  /// تحويل النص إلى كلام لسطح المكتب
  static Future<void> _speakDesktop(String text) async {
    // محاكاة تحويل النص إلى كلام لسطح المكتب
    // يمكن تطوير هذا لاستخدام مكتبات نظام التشغيل
    await Future.delayed(Duration(milliseconds: text.length * 60));
    _isSpeaking = false;
  }

  /// إيقاف الكلام لسطح المكتب
  static Future<void> _stopDesktop() async {
    // محاكاة إيقاف الكلام لسطح المكتب
    _isSpeaking = false;
  }

  /// تحويل نص طويل إلى أجزاء صغيرة
  static List<String> _splitTextIntoChunks(String text, {int maxLength = 200}) {
    if (text.length <= maxLength) return [text];

    final chunks = <String>[];
    final sentences = text.split(RegExp(r'[.!?؟]'));
    
    String currentChunk = '';
    
    for (final sentence in sentences) {
      if (currentChunk.length + sentence.length <= maxLength) {
        currentChunk += sentence + '. ';
      } else {
        if (currentChunk.isNotEmpty) {
          chunks.add(currentChunk.trim());
          currentChunk = sentence + '. ';
        } else {
          // الجملة طويلة جداً، قسمها بالكلمات
          final words = sentence.split(' ');
          String wordChunk = '';
          
          for (final word in words) {
            if (wordChunk.length + word.length <= maxLength) {
              wordChunk += word + ' ';
            } else {
              if (wordChunk.isNotEmpty) {
                chunks.add(wordChunk.trim());
                wordChunk = word + ' ';
              }
            }
          }
          
          if (wordChunk.isNotEmpty) {
            currentChunk = wordChunk;
          }
        }
      }
    }
    
    if (currentChunk.isNotEmpty) {
      chunks.add(currentChunk.trim());
    }
    
    return chunks;
  }

  /// تحويل نص طويل إلى كلام بالتقسيم
  static Future<void> speakLongText(String text) async {
    final chunks = _splitTextIntoChunks(text);
    
    for (final chunk in chunks) {
      if (!_isSpeaking) break; // إذا تم إيقاف الكلام
      
      await speak(chunk);
      
      // انتظار قصير بين الأجزاء
      await Future.delayed(const Duration(milliseconds: 500));
    }
  }

  /// إعدادات صوتية مُعدة مسبقاً
  static Future<void> applyVoicePreset(VoicePreset preset) async {
    switch (preset) {
      case VoicePreset.normal:
        await setSpeechRate(0.5);
        await setPitch(1.0);
        break;
      case VoicePreset.slow:
        await setSpeechRate(0.3);
        await setPitch(1.0);
        break;
      case VoicePreset.fast:
        await setSpeechRate(0.8);
        await setPitch(1.0);
        break;
      case VoicePreset.deep:
        await setSpeechRate(0.4);
        await setPitch(0.7);
        break;
      case VoicePreset.high:
        await setSpeechRate(0.5);
        await setPitch(1.3);
        break;
    }
  }
}

/// إعدادات صوتية مُعدة مسبقاً
enum VoicePreset {
  normal,  // عادي
  slow,    // بطيء
  fast,    // سريع
  deep,    // عميق
  high,    // حاد
}

/// معلومات اللغة
class LanguageInfo {
  final String code;
  final String name;
  final String nativeName;

  const LanguageInfo({
    required this.code,
    required this.name,
    required this.nativeName,
  });

  static const List<LanguageInfo> supportedLanguages = [
    LanguageInfo(code: 'ar-SA', name: 'Arabic (Saudi)', nativeName: 'العربية السعودية'),
    LanguageInfo(code: 'ar-EG', name: 'Arabic (Egypt)', nativeName: 'العربية المصرية'),
    LanguageInfo(code: 'en-US', name: 'English (US)', nativeName: 'English (US)'),
    LanguageInfo(code: 'en-GB', name: 'English (UK)', nativeName: 'English (UK)'),
    LanguageInfo(code: 'fr-FR', name: 'French', nativeName: 'Français'),
    LanguageInfo(code: 'de-DE', name: 'German', nativeName: 'Deutsch'),
    LanguageInfo(code: 'es-ES', name: 'Spanish', nativeName: 'Español'),
  ];
}
