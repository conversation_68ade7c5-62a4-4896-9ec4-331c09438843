import 'package:flutter/material.dart';
import 'dart:async';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../widgets/animated_icons.dart';

// معالج الأخطاء المركزي
class ErrorHandler {
  static final Map<String, String> _errorMessages = {
    'NO_INTERNET': 'لا يوجد اتصال بالإنترنت. تحقق من اتصالك وحاول مرة أخرى.',
    'TIMEOUT': 'انتهت مهلة الطلب. حاول مرة أخرى لاحقاً.',
    'SERVER_ERROR': 'خطأ في الخادم. نعمل على حل المشكلة.',
    'INVALID_API_KEY': 'مفتاح API غير صالح. تحقق من الإعدادات.',
    'RATE_LIMIT': 'تم تجاوز حد الطلبات. انتظر قليلاً وحاول مرة أخرى.',
    'INVALID_REQUEST': 'طلب غير صالح. تحقق من البيانات المدخلة.',
    'UNAUTHORIZED': 'غير مصرح. تحقق من صلاحياتك.',
    'NOT_FOUND': 'المورد المطلوب غير موجود.',
    'FORMAT_ERROR': 'خطأ في تنسيق البيانات.',
    'MAX_RETRIES_EXCEEDED': 'فشلت جميع المحاولات. حاول مرة أخرى لاحقاً.',
    'UNKNOWN_ERROR': 'حدث خطأ غير متوقع. حاول مرة أخرى.',
  };

  // معالجة الأخطاء وإرجاع رسالة مناسبة
  static String getErrorMessage(dynamic error) {
    if (error is AIException) {
      return _errorMessages[error.code] ?? error.message;
    } else if (error is TimeoutException) {
      return _errorMessages['TIMEOUT']!;
    } else if (error is FormatException) {
      return _errorMessages['FORMAT_ERROR']!;
    } else {
      return _errorMessages['UNKNOWN_ERROR']!;
    }
  }

  // عرض رسالة خطأ مع رسوم متحركة
  static void showError(BuildContext context, dynamic error) {
    final message = getErrorMessage(error);

    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => ErrorDialog(message: message, error: error),
    );
  }

  // عرض رسالة خطأ بسيطة (SnackBar)
  static void showSnackBarError(BuildContext context, dynamic error) {
    final message = getErrorMessage(error);

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            const Icon(Icons.error_outline, color: Colors.white),
            const SizedBox(width: 8),
            Expanded(
              child: Text(message, style: const TextStyle(color: Colors.white)),
            ),
          ],
        ),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        action: SnackBarAction(
          label: 'حسناً',
          textColor: Colors.white,
          onPressed: () {},
        ),
      ),
    );
  }

  // تسجيل الأخطاء (للتطوير والتحليل)
  static void logError(dynamic error, {StackTrace? stackTrace}) {
    // في الإنتاج، يمكن إرسال الأخطاء إلى خدمة مثل Sentry أو Firebase Crashlytics
    debugPrint('=== خطأ ===');
    debugPrint('Error: $error');
    if (stackTrace != null) {
      debugPrint('StackTrace: $stackTrace');
    }
    debugPrint('===========');
  }

  // معالجة الأخطاء مع إعادة المحاولة
  static Future<T> handleWithRetry<T>({
    required Future<T> Function() operation,
    int maxRetries = 3,
    Duration retryDelay = const Duration(seconds: 2),
    void Function(dynamic error, int attempt)? onRetry,
  }) async {
    int attempt = 0;

    while (attempt < maxRetries) {
      try {
        return await operation();
      } catch (error) {
        attempt++;

        if (attempt >= maxRetries) {
          throw error;
        }

        if (onRetry != null) {
          onRetry(error, attempt);
        }

        await Future.delayed(retryDelay * attempt);
      }
    }

    throw Exception('فشلت جميع المحاولات');
  }
}

// نافذة حوار مخصصة للأخطاء
class ErrorDialog extends StatefulWidget {
  final String message;
  final dynamic error;

  const ErrorDialog({Key? key, required this.message, this.error})
    : super(key: key);

  @override
  State<ErrorDialog> createState() => _ErrorDialogState();
}

class _ErrorDialogState extends State<ErrorDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(
      begin: 0.8,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.elasticOut));

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.easeIn));

    _controller.forward();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: ScaleTransition(
            scale: _scaleAnimation,
            child: Dialog(
              backgroundColor: Colors.transparent,
              child: Container(
                padding: const EdgeInsets.all(24),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppColors.darkGrey,
                      AppColors.darkGrey.withOpacity(0.9),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(
                    color: AppColors.error.withOpacity(0.3),
                    width: 1,
                  ),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.error.withOpacity(0.2),
                      blurRadius: 20,
                      offset: const Offset(0, 10),
                    ),
                  ],
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const PulsingIcon(
                      icon: Icons.error_outline,
                      color: AppColors.error,
                      size: 48,
                    ),
                    const SizedBox(height: 16),
                    Text(
                      'حدث خطأ',
                      style: AppTextStyles.heading2.copyWith(
                        color: AppColors.error,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Text(
                      widget.message,
                      style: AppTextStyles.body,
                      textAlign: TextAlign.center,
                    ),
                    if (widget.error is AIException &&
                        (widget.error as AIException).details != null) ...[
                      const SizedBox(height: 16),
                      Container(
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppColors.darkPurple.withOpacity(0.3),
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'تفاصيل تقنية:',
                              style: AppTextStyles.caption.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              'كود الخطأ: ${(widget.error as AIException).code}',
                              style: AppTextStyles.caption,
                            ),
                            if ((widget.error as AIException).statusCode !=
                                null)
                              Text(
                                'رمز الحالة: ${(widget.error as AIException).statusCode}',
                                style: AppTextStyles.caption,
                              ),
                          ],
                        ),
                      ),
                    ],
                    const SizedBox(height: 24),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      children: [
                        TextButton(
                          onPressed: () => Navigator.of(context).pop(),
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [
                                  AppColors.midGrey,
                                  AppColors.midGrey.withOpacity(0.8),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text('إغلاق', style: AppTextStyles.button),
                          ),
                        ),
                        TextButton(
                          onPressed: () {
                            Navigator.of(context).pop();
                            // يمكن إضافة منطق إعادة المحاولة هنا
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 24,
                              vertical: 12,
                            ),
                            decoration: BoxDecoration(
                              gradient: const LinearGradient(
                                colors: [
                                  AppColors.primaryPurple,
                                  AppColors.lightPurple,
                                ],
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              'إعادة المحاولة',
                              style: AppTextStyles.button,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }
}

// استثناء مخصص للذكاء الاصطناعي (نقله من ai_service.dart)
class AIException implements Exception {
  final String message;
  final String code;
  final int? statusCode;
  final Map<String, dynamic>? details;

  AIException(
    this.message, {
    required this.code,
    this.statusCode,
    this.details,
  });

  @override
  String toString() => 'AIException: $message (Code: $code)';
}

// Widget لعرض حالة الخطأ في الواجهة
class ErrorStateWidget extends StatelessWidget {
  final String message;
  final VoidCallback? onRetry;
  final IconData icon;

  const ErrorStateWidget({
    Key? key,
    required this.message,
    this.onRetry,
    this.icon = Icons.error_outline,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            WavingIcon(icon: icon, color: AppColors.error, size: 64),
            const SizedBox(height: 24),
            Text(
              'عذراً!',
              style: AppTextStyles.heading2.copyWith(color: AppColors.error),
            ),
            const SizedBox(height: 16),
            Text(
              message,
              style: AppTextStyles.body,
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 32),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('إعادة المحاولة'),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryPurple,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 24,
                    vertical: 12,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
}
