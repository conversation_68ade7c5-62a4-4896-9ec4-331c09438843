import 'package:flutter/foundation.dart';
import '../storage/storage_service.dart';
import 'user_management_service.dart';

/// خدمة مساحات العمل المشتركة
class WorkspaceService {
  static const String _workspacesKey = 'shared_workspaces';
  static const String _projectsKey = 'workspace_projects';
  static const String _documentsKey = 'workspace_documents';
  static const String _activitiesKey = 'workspace_activities';
  
  static bool _isInitialized = false;
  static Map<String, Workspace> _workspaces = {};
  static Map<String, WorkspaceProject> _projects = {};
  static Map<String, WorkspaceDocument> _documents = {};
  static List<WorkspaceActivity> _activities = [];

  /// تهيئة خدمة مساحات العمل
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadWorkspaces();
      await _loadProjects();
      await _loadDocuments();
      await _loadActivities();
      
      _isInitialized = true;
      debugPrint('🏢 تم تهيئة خدمة مساحات العمل المشتركة');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة مساحات العمل: $e');
    }
  }

  /// إنشاء مساحة عمل جديدة
  static Future<Workspace> createWorkspace({
    required String name,
    required String description,
    String? avatar,
    List<String>? memberIds,
    WorkspaceType type = WorkspaceType.team,
  }) async {
    final workspace = Workspace(
      id: 'ws_${DateTime.now().millisecondsSinceEpoch}',
      name: name,
      description: description,
      avatar: avatar,
      ownerId: UserManagementService.currentUser?.id ?? '',
      memberIds: memberIds ?? [],
      type: type,
      settings: WorkspaceSettings(),
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    // إضافة المنشئ كعضو
    if (UserManagementService.currentUser != null && 
        !workspace.memberIds.contains(UserManagementService.currentUser!.id)) {
      workspace.memberIds.add(UserManagementService.currentUser!.id);
    }

    _workspaces[workspace.id] = workspace;
    await _saveWorkspaces();
    
    // تسجيل النشاط
    await _logActivity(
      workspaceId: workspace.id,
      type: ActivityType.workspaceCreated,
      description: 'تم إنشاء مساحة العمل',
    );
    
    debugPrint('✅ تم إنشاء مساحة عمل جديدة: $name');
    return workspace;
  }

  /// إنشاء مشروع في مساحة العمل
  static Future<WorkspaceProject> createProject({
    required String workspaceId,
    required String name,
    required String description,
    DateTime? deadline,
    ProjectPriority priority = ProjectPriority.medium,
    List<String>? assignedMemberIds,
  }) async {
    final project = WorkspaceProject(
      id: 'proj_${DateTime.now().millisecondsSinceEpoch}',
      workspaceId: workspaceId,
      name: name,
      description: description,
      status: ProjectStatus.planning,
      priority: priority,
      progress: 0.0,
      deadline: deadline,
      assignedMemberIds: assignedMemberIds ?? [],
      createdBy: UserManagementService.currentUser?.id ?? '',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _projects[project.id] = project;
    await _saveProjects();
    
    // تسجيل النشاط
    await _logActivity(
      workspaceId: workspaceId,
      type: ActivityType.projectCreated,
      description: 'تم إنشاء مشروع جديد: $name',
      projectId: project.id,
    );
    
    debugPrint('✅ تم إنشاء مشروع جديد: $name');
    return project;
  }

  /// إنشاء مستند في مساحة العمل
  static Future<WorkspaceDocument> createDocument({
    required String workspaceId,
    String? projectId,
    required String title,
    required String content,
    DocumentType type = DocumentType.text,
    List<String>? sharedWithIds,
  }) async {
    final document = WorkspaceDocument(
      id: 'doc_${DateTime.now().millisecondsSinceEpoch}',
      workspaceId: workspaceId,
      projectId: projectId,
      title: title,
      content: content,
      type: type,
      version: 1,
      isLocked: false,
      lockedBy: null,
      sharedWithIds: sharedWithIds ?? [],
      createdBy: UserManagementService.currentUser?.id ?? '',
      lastEditedBy: UserManagementService.currentUser?.id ?? '',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    _documents[document.id] = document;
    await _saveDocuments();
    
    // تسجيل النشاط
    await _logActivity(
      workspaceId: workspaceId,
      type: ActivityType.documentCreated,
      description: 'تم إنشاء مستند جديد: $title',
      documentId: document.id,
      projectId: projectId,
    );
    
    debugPrint('✅ تم إنشاء مستند جديد: $title');
    return document;
  }

  /// تحديث مستند (التحرير التعاوني)
  static Future<bool> updateDocument({
    required String documentId,
    String? title,
    String? content,
    bool incrementVersion = true,
  }) async {
    try {
      final document = _documents[documentId];
      if (document == null) return false;

      // التحقق من القفل
      if (document.isLocked && 
          document.lockedBy != UserManagementService.currentUser?.id) {
        debugPrint('❌ المستند مقفل من قبل مستخدم آخر');
        return false;
      }

      final updatedDocument = WorkspaceDocument(
        id: document.id,
        workspaceId: document.workspaceId,
        projectId: document.projectId,
        title: title ?? document.title,
        content: content ?? document.content,
        type: document.type,
        version: incrementVersion ? document.version + 1 : document.version,
        isLocked: document.isLocked,
        lockedBy: document.lockedBy,
        sharedWithIds: document.sharedWithIds,
        createdBy: document.createdBy,
        lastEditedBy: UserManagementService.currentUser?.id ?? document.lastEditedBy,
        createdAt: document.createdAt,
        updatedAt: DateTime.now(),
      );

      _documents[documentId] = updatedDocument;
      await _saveDocuments();
      
      // تسجيل النشاط
      await _logActivity(
        workspaceId: document.workspaceId,
        type: ActivityType.documentEdited,
        description: 'تم تحديث المستند: ${updatedDocument.title}',
        documentId: documentId,
        projectId: document.projectId,
      );
      
      debugPrint('✅ تم تحديث المستند: $documentId');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في تحديث المستند: $e');
      return false;
    }
  }

  /// قفل مستند للتحرير
  static Future<bool> lockDocument(String documentId) async {
    try {
      final document = _documents[documentId];
      if (document == null) return false;

      if (document.isLocked) {
        debugPrint('❌ المستند مقفل بالفعل');
        return false;
      }

      final updatedDocument = WorkspaceDocument(
        id: document.id,
        workspaceId: document.workspaceId,
        projectId: document.projectId,
        title: document.title,
        content: document.content,
        type: document.type,
        version: document.version,
        isLocked: true,
        lockedBy: UserManagementService.currentUser?.id,
        sharedWithIds: document.sharedWithIds,
        createdBy: document.createdBy,
        lastEditedBy: document.lastEditedBy,
        createdAt: document.createdAt,
        updatedAt: DateTime.now(),
      );

      _documents[documentId] = updatedDocument;
      await _saveDocuments();
      
      debugPrint('🔒 تم قفل المستند: $documentId');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في قفل المستند: $e');
      return false;
    }
  }

  /// إلغاء قفل مستند
  static Future<bool> unlockDocument(String documentId) async {
    try {
      final document = _documents[documentId];
      if (document == null) return false;

      // التحقق من الصلاحية
      if (document.lockedBy != UserManagementService.currentUser?.id) {
        debugPrint('❌ لا يمكن إلغاء قفل المستند - غير مخول');
        return false;
      }

      final updatedDocument = WorkspaceDocument(
        id: document.id,
        workspaceId: document.workspaceId,
        projectId: document.projectId,
        title: document.title,
        content: document.content,
        type: document.type,
        version: document.version,
        isLocked: false,
        lockedBy: null,
        sharedWithIds: document.sharedWithIds,
        createdBy: document.createdBy,
        lastEditedBy: document.lastEditedBy,
        createdAt: document.createdAt,
        updatedAt: DateTime.now(),
      );

      _documents[documentId] = updatedDocument;
      await _saveDocuments();
      
      debugPrint('🔓 تم إلغاء قفل المستند: $documentId');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في إلغاء قفل المستند: $e');
      return false;
    }
  }

  /// تحديث تقدم المشروع
  static Future<bool> updateProjectProgress(String projectId, double progress) async {
    try {
      final project = _projects[projectId];
      if (project == null) return false;

      final updatedProject = WorkspaceProject(
        id: project.id,
        workspaceId: project.workspaceId,
        name: project.name,
        description: project.description,
        status: _getStatusFromProgress(progress),
        priority: project.priority,
        progress: progress,
        deadline: project.deadline,
        assignedMemberIds: project.assignedMemberIds,
        createdBy: project.createdBy,
        createdAt: project.createdAt,
        updatedAt: DateTime.now(),
      );

      _projects[projectId] = updatedProject;
      await _saveProjects();
      
      // تسجيل النشاط
      await _logActivity(
        workspaceId: project.workspaceId,
        type: ActivityType.projectUpdated,
        description: 'تم تحديث تقدم المشروع: ${(progress * 100).toInt()}%',
        projectId: projectId,
      );
      
      debugPrint('✅ تم تحديث تقدم المشروع: $projectId');
      return true;
    } catch (e) {
      debugPrint('❌ فشل في تحديث تقدم المشروع: $e');
      return false;
    }
  }

  /// إضافة عضو لمساحة العمل
  static Future<bool> addWorkspaceMember(String workspaceId, String userId) async {
    try {
      final workspace = _workspaces[workspaceId];
      if (workspace == null) return false;

      if (!workspace.memberIds.contains(userId)) {
        workspace.memberIds.add(userId);
        
        final updatedWorkspace = Workspace(
          id: workspace.id,
          name: workspace.name,
          description: workspace.description,
          avatar: workspace.avatar,
          ownerId: workspace.ownerId,
          memberIds: workspace.memberIds,
          type: workspace.type,
          settings: workspace.settings,
          createdAt: workspace.createdAt,
          updatedAt: DateTime.now(),
        );

        _workspaces[workspaceId] = updatedWorkspace;
        await _saveWorkspaces();
        
        // تسجيل النشاط
        await _logActivity(
          workspaceId: workspaceId,
          type: ActivityType.memberAdded,
          description: 'تم إضافة عضو جديد لمساحة العمل',
          userId: userId,
        );
        
        debugPrint('✅ تم إضافة عضو لمساحة العمل: $userId');
        return true;
      }
      return true;
    } catch (e) {
      debugPrint('❌ فشل في إضافة عضو لمساحة العمل: $e');
      return false;
    }
  }

  /// الحصول على مساحات عمل المستخدم
  static List<Workspace> getUserWorkspaces(String userId) {
    return _workspaces.values
        .where((workspace) => workspace.memberIds.contains(userId))
        .toList();
  }

  /// الحصول على مشاريع مساحة العمل
  static List<WorkspaceProject> getWorkspaceProjects(String workspaceId) {
    return _projects.values
        .where((project) => project.workspaceId == workspaceId)
        .toList();
  }

  /// الحصول على مستندات مساحة العمل
  static List<WorkspaceDocument> getWorkspaceDocuments(String workspaceId) {
    return _documents.values
        .where((document) => document.workspaceId == workspaceId)
        .toList();
  }

  /// الحصول على أنشطة مساحة العمل
  static List<WorkspaceActivity> getWorkspaceActivities(String workspaceId, {int? limit}) {
    var activities = _activities
        .where((activity) => activity.workspaceId == workspaceId)
        .toList();
    
    // ترتيب حسب التاريخ (الأحدث أولاً)
    activities.sort((a, b) => b.timestamp.compareTo(a.timestamp));
    
    if (limit != null && limit > 0) {
      activities = activities.take(limit).toList();
    }
    
    return activities;
  }

  /// تسجيل نشاط في مساحة العمل
  static Future<void> _logActivity({
    required String workspaceId,
    required ActivityType type,
    required String description,
    String? userId,
    String? projectId,
    String? documentId,
  }) async {
    final activity = WorkspaceActivity(
      id: 'act_${DateTime.now().millisecondsSinceEpoch}',
      workspaceId: workspaceId,
      type: type,
      description: description,
      userId: userId ?? UserManagementService.currentUser?.id ?? '',
      projectId: projectId,
      documentId: documentId,
      timestamp: DateTime.now(),
    );

    _activities.add(activity);
    
    // الاحتفاظ بآخر 1000 نشاط فقط
    if (_activities.length > 1000) {
      _activities.sort((a, b) => b.timestamp.compareTo(a.timestamp));
      _activities = _activities.take(1000).toList();
    }
    
    await _saveActivities();
  }

  /// تحديد حالة المشروع من التقدم
  static ProjectStatus _getStatusFromProgress(double progress) {
    if (progress == 0.0) return ProjectStatus.planning;
    if (progress < 1.0) return ProjectStatus.inProgress;
    return ProjectStatus.completed;
  }

  // Private methods for data persistence

  static Future<void> _saveWorkspaces() async {
    final data = _workspaces.map((key, value) => MapEntry(key, value.toMap()));
    await StorageService.saveData(_workspacesKey, data);
  }

  static Future<void> _loadWorkspaces() async {
    try {
      final data = await StorageService.getData(_workspacesKey);
      if (data != null && data is Map) {
        _workspaces = data.map((key, value) => MapEntry(key, Workspace.fromMap(value)));
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل مساحات العمل: $e');
    }
  }

  static Future<void> _saveProjects() async {
    final data = _projects.map((key, value) => MapEntry(key, value.toMap()));
    await StorageService.saveData(_projectsKey, data);
  }

  static Future<void> _loadProjects() async {
    try {
      final data = await StorageService.getData(_projectsKey);
      if (data != null && data is Map) {
        _projects = data.map((key, value) => MapEntry(key, WorkspaceProject.fromMap(value)));
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل المشاريع: $e');
    }
  }

  static Future<void> _saveDocuments() async {
    final data = _documents.map((key, value) => MapEntry(key, value.toMap()));
    await StorageService.saveData(_documentsKey, data);
  }

  static Future<void> _loadDocuments() async {
    try {
      final data = await StorageService.getData(_documentsKey);
      if (data != null && data is Map) {
        _documents = data.map((key, value) => MapEntry(key, WorkspaceDocument.fromMap(value)));
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل المستندات: $e');
    }
  }

  static Future<void> _saveActivities() async {
    final data = _activities.map((activity) => activity.toMap()).toList();
    await StorageService.saveData(_activitiesKey, data);
  }

  static Future<void> _loadActivities() async {
    try {
      final data = await StorageService.getData(_activitiesKey);
      if (data != null && data is List) {
        _activities = data.map((item) => WorkspaceActivity.fromMap(item)).toList();
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل الأنشطة: $e');
    }
  }

  // Getters
  static bool get isInitialized => _isInitialized;
  static List<Workspace> get allWorkspaces => _workspaces.values.toList();
  static List<WorkspaceProject> get allProjects => _projects.values.toList();
  static List<WorkspaceDocument> get allDocuments => _documents.values.toList();
  static int get workspacesCount => _workspaces.length;
  static int get projectsCount => _projects.length;
  static int get documentsCount => _documents.length;
}

/// مساحة العمل المشتركة
class Workspace {
  final String id;
  final String name;
  final String description;
  final String? avatar;
  final String ownerId;
  final List<String> memberIds;
  final WorkspaceType type;
  final WorkspaceSettings settings;
  final DateTime createdAt;
  final DateTime updatedAt;

  Workspace({
    required this.id,
    required this.name,
    required this.description,
    this.avatar,
    required this.ownerId,
    required this.memberIds,
    required this.type,
    required this.settings,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'name': name,
      'description': description,
      'avatar': avatar,
      'ownerId': ownerId,
      'memberIds': memberIds,
      'type': type.toString(),
      'settings': settings.toMap(),
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory Workspace.fromMap(Map<String, dynamic> map) {
    return Workspace(
      id: map['id'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      avatar: map['avatar'],
      ownerId: map['ownerId'] ?? '',
      memberIds: List<String>.from(map['memberIds'] ?? []),
      type: WorkspaceType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => WorkspaceType.team,
      ),
      settings: WorkspaceSettings.fromMap(map['settings'] ?? {}),
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }
}

/// إعدادات مساحة العمل
class WorkspaceSettings {
  final bool allowGuestAccess;
  final bool enableNotifications;
  final bool allowFileSharing;
  final bool enableVersionControl;

  WorkspaceSettings({
    this.allowGuestAccess = false,
    this.enableNotifications = true,
    this.allowFileSharing = true,
    this.enableVersionControl = true,
  });

  Map<String, dynamic> toMap() {
    return {
      'allowGuestAccess': allowGuestAccess,
      'enableNotifications': enableNotifications,
      'allowFileSharing': allowFileSharing,
      'enableVersionControl': enableVersionControl,
    };
  }

  factory WorkspaceSettings.fromMap(Map<String, dynamic> map) {
    return WorkspaceSettings(
      allowGuestAccess: map['allowGuestAccess'] ?? false,
      enableNotifications: map['enableNotifications'] ?? true,
      allowFileSharing: map['allowFileSharing'] ?? true,
      enableVersionControl: map['enableVersionControl'] ?? true,
    );
  }
}

/// مشروع مساحة العمل
class WorkspaceProject {
  final String id;
  final String workspaceId;
  final String name;
  final String description;
  final ProjectStatus status;
  final ProjectPriority priority;
  final double progress;
  final DateTime? deadline;
  final List<String> assignedMemberIds;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  WorkspaceProject({
    required this.id,
    required this.workspaceId,
    required this.name,
    required this.description,
    required this.status,
    required this.priority,
    required this.progress,
    this.deadline,
    required this.assignedMemberIds,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'workspaceId': workspaceId,
      'name': name,
      'description': description,
      'status': status.toString(),
      'priority': priority.toString(),
      'progress': progress,
      'deadline': deadline?.toIso8601String(),
      'assignedMemberIds': assignedMemberIds,
      'createdBy': createdBy,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory WorkspaceProject.fromMap(Map<String, dynamic> map) {
    return WorkspaceProject(
      id: map['id'] ?? '',
      workspaceId: map['workspaceId'] ?? '',
      name: map['name'] ?? '',
      description: map['description'] ?? '',
      status: ProjectStatus.values.firstWhere(
        (e) => e.toString() == map['status'],
        orElse: () => ProjectStatus.planning,
      ),
      priority: ProjectPriority.values.firstWhere(
        (e) => e.toString() == map['priority'],
        orElse: () => ProjectPriority.medium,
      ),
      progress: map['progress']?.toDouble() ?? 0.0,
      deadline: map['deadline'] != null ? DateTime.parse(map['deadline']) : null,
      assignedMemberIds: List<String>.from(map['assignedMemberIds'] ?? []),
      createdBy: map['createdBy'] ?? '',
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }
}

/// مستند مساحة العمل
class WorkspaceDocument {
  final String id;
  final String workspaceId;
  final String? projectId;
  final String title;
  final String content;
  final DocumentType type;
  final int version;
  final bool isLocked;
  final String? lockedBy;
  final List<String> sharedWithIds;
  final String createdBy;
  final String lastEditedBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  WorkspaceDocument({
    required this.id,
    required this.workspaceId,
    this.projectId,
    required this.title,
    required this.content,
    required this.type,
    required this.version,
    required this.isLocked,
    this.lockedBy,
    required this.sharedWithIds,
    required this.createdBy,
    required this.lastEditedBy,
    required this.createdAt,
    required this.updatedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'workspaceId': workspaceId,
      'projectId': projectId,
      'title': title,
      'content': content,
      'type': type.toString(),
      'version': version,
      'isLocked': isLocked,
      'lockedBy': lockedBy,
      'sharedWithIds': sharedWithIds,
      'createdBy': createdBy,
      'lastEditedBy': lastEditedBy,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  factory WorkspaceDocument.fromMap(Map<String, dynamic> map) {
    return WorkspaceDocument(
      id: map['id'] ?? '',
      workspaceId: map['workspaceId'] ?? '',
      projectId: map['projectId'],
      title: map['title'] ?? '',
      content: map['content'] ?? '',
      type: DocumentType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => DocumentType.text,
      ),
      version: map['version'] ?? 1,
      isLocked: map['isLocked'] ?? false,
      lockedBy: map['lockedBy'],
      sharedWithIds: List<String>.from(map['sharedWithIds'] ?? []),
      createdBy: map['createdBy'] ?? '',
      lastEditedBy: map['lastEditedBy'] ?? '',
      createdAt: DateTime.parse(map['createdAt']),
      updatedAt: DateTime.parse(map['updatedAt']),
    );
  }
}

/// نشاط مساحة العمل
class WorkspaceActivity {
  final String id;
  final String workspaceId;
  final ActivityType type;
  final String description;
  final String userId;
  final String? projectId;
  final String? documentId;
  final DateTime timestamp;

  WorkspaceActivity({
    required this.id,
    required this.workspaceId,
    required this.type,
    required this.description,
    required this.userId,
    this.projectId,
    this.documentId,
    required this.timestamp,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'workspaceId': workspaceId,
      'type': type.toString(),
      'description': description,
      'userId': userId,
      'projectId': projectId,
      'documentId': documentId,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory WorkspaceActivity.fromMap(Map<String, dynamic> map) {
    return WorkspaceActivity(
      id: map['id'] ?? '',
      workspaceId: map['workspaceId'] ?? '',
      type: ActivityType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => ActivityType.other,
      ),
      description: map['description'] ?? '',
      userId: map['userId'] ?? '',
      projectId: map['projectId'],
      documentId: map['documentId'],
      timestamp: DateTime.parse(map['timestamp']),
    );
  }
}

/// نوع مساحة العمل
enum WorkspaceType {
  personal,
  team,
  organization,
  public,
}

/// حالة المشروع
enum ProjectStatus {
  planning,
  inProgress,
  review,
  completed,
  cancelled,
}

/// أولوية المشروع
enum ProjectPriority {
  low,
  medium,
  high,
  urgent,
}

/// نوع المستند
enum DocumentType {
  text,
  markdown,
  code,
  spreadsheet,
  presentation,
}

/// نوع النشاط
enum ActivityType {
  workspaceCreated,
  projectCreated,
  projectUpdated,
  documentCreated,
  documentEdited,
  memberAdded,
  memberRemoved,
  other,
}
