/// نماذج البيانات لتحسين الأداء

/// إعدادات الأداء
class PerformanceSettings {
  final bool enableAutoOptimization;
  final bool enablePerformanceMonitoring;
  final int monitoringIntervalMinutes;
  final bool enableMemoryOptimization;
  final bool enableRenderingOptimization;
  final bool enableNetworkOptimization;
  final bool enableStorageOptimization;
  final bool enableBatteryOptimization;
  final OptimizationLevel optimizationLevel;

  PerformanceSettings({
    this.enableAutoOptimization = true,
    this.enablePerformanceMonitoring = true,
    this.monitoringIntervalMinutes = 5,
    this.enableMemoryOptimization = true,
    this.enableRenderingOptimization = true,
    this.enableNetworkOptimization = true,
    this.enableStorageOptimization = true,
    this.enableBatteryOptimization = true,
    this.optimizationLevel = OptimizationLevel.balanced,
  });

  Map<String, dynamic> toMap() {
    return {
      'enableAutoOptimization': enableAutoOptimization,
      'enablePerformanceMonitoring': enablePerformanceMonitoring,
      'monitoringIntervalMinutes': monitoringIntervalMinutes,
      'enableMemoryOptimization': enableMemoryOptimization,
      'enableRenderingOptimization': enableRenderingOptimization,
      'enableNetworkOptimization': enableNetworkOptimization,
      'enableStorageOptimization': enableStorageOptimization,
      'enableBatteryOptimization': enableBatteryOptimization,
      'optimizationLevel': optimizationLevel.toString(),
    };
  }

  factory PerformanceSettings.fromMap(Map<String, dynamic> map) {
    return PerformanceSettings(
      enableAutoOptimization: map['enableAutoOptimization'] ?? true,
      enablePerformanceMonitoring: map['enablePerformanceMonitoring'] ?? true,
      monitoringIntervalMinutes: map['monitoringIntervalMinutes'] ?? 5,
      enableMemoryOptimization: map['enableMemoryOptimization'] ?? true,
      enableRenderingOptimization: map['enableRenderingOptimization'] ?? true,
      enableNetworkOptimization: map['enableNetworkOptimization'] ?? true,
      enableStorageOptimization: map['enableStorageOptimization'] ?? true,
      enableBatteryOptimization: map['enableBatteryOptimization'] ?? true,
      optimizationLevel: OptimizationLevel.values.firstWhere(
        (e) => e.toString() == map['optimizationLevel'],
        orElse: () => OptimizationLevel.balanced,
      ),
    );
  }
}

/// نتيجة التحسين
class OptimizationResult {
  final OptimizationType type;
  final bool success;
  final double improvementPercentage;
  final Duration? duration;
  final String description;
  final String? error;

  OptimizationResult({
    required this.type,
    required this.success,
    this.improvementPercentage = 0.0,
    this.duration,
    required this.description,
    this.error,
  });

  Map<String, dynamic> toMap() {
    return {
      'type': type.toString(),
      'success': success,
      'improvementPercentage': improvementPercentage,
      'duration': duration?.inMilliseconds,
      'description': description,
      'error': error,
    };
  }

  factory OptimizationResult.fromMap(Map<String, dynamic> map) {
    return OptimizationResult(
      type: OptimizationType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => OptimizationType.memory,
      ),
      success: map['success'] ?? false,
      improvementPercentage: map['improvementPercentage']?.toDouble() ?? 0.0,
      duration: map['duration'] != null 
          ? Duration(milliseconds: map['duration']) 
          : null,
      description: map['description'] ?? '',
      error: map['error'],
    );
  }
}

/// مقياس الأداء
class PerformanceMetric {
  final MetricType type;
  final double value;
  final DateTime timestamp;
  final String? unit;

  PerformanceMetric({
    required this.type,
    required this.value,
    required this.timestamp,
    this.unit,
  });

  Map<String, dynamic> toMap() {
    return {
      'type': type.toString(),
      'value': value,
      'timestamp': timestamp.toIso8601String(),
      'unit': unit,
    };
  }

  factory PerformanceMetric.fromMap(Map<String, dynamic> map) {
    return PerformanceMetric(
      type: MetricType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => MetricType.memory,
      ),
      value: map['value']?.toDouble() ?? 0.0,
      timestamp: DateTime.parse(map['timestamp']),
      unit: map['unit'],
    );
  }
}

/// تقرير الأداء
class PerformanceReport {
  final double memoryUsage;
  final double frameRate;
  final double responseTime;
  final double batteryUsage;
  final double networkUsage;
  final double overallScore;
  final Duration measurementTime;
  final DateTime timestamp;

  PerformanceReport({
    required this.memoryUsage,
    required this.frameRate,
    required this.responseTime,
    required this.batteryUsage,
    required this.networkUsage,
    required this.overallScore,
    required this.measurementTime,
    required this.timestamp,
  });

  Map<String, dynamic> toMap() {
    return {
      'memoryUsage': memoryUsage,
      'frameRate': frameRate,
      'responseTime': responseTime,
      'batteryUsage': batteryUsage,
      'networkUsage': networkUsage,
      'overallScore': overallScore,
      'measurementTime': measurementTime.inMilliseconds,
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory PerformanceReport.fromMap(Map<String, dynamic> map) {
    return PerformanceReport(
      memoryUsage: map['memoryUsage']?.toDouble() ?? 0.0,
      frameRate: map['frameRate']?.toDouble() ?? 0.0,
      responseTime: map['responseTime']?.toDouble() ?? 0.0,
      batteryUsage: map['batteryUsage']?.toDouble() ?? 0.0,
      networkUsage: map['networkUsage']?.toDouble() ?? 0.0,
      overallScore: map['overallScore']?.toDouble() ?? 0.0,
      measurementTime: Duration(milliseconds: map['measurementTime'] ?? 0),
      timestamp: DateTime.parse(map['timestamp']),
    );
  }

  /// تقييم الأداء بناءً على النتيجة الإجمالية
  PerformanceGrade get grade {
    if (overallScore >= 90) return PerformanceGrade.excellent;
    if (overallScore >= 80) return PerformanceGrade.good;
    if (overallScore >= 70) return PerformanceGrade.average;
    if (overallScore >= 60) return PerformanceGrade.poor;
    return PerformanceGrade.critical;
  }

  /// وصف تقييم الأداء
  String get gradeDescription {
    switch (grade) {
      case PerformanceGrade.excellent:
        return 'ممتاز - الأداء في أفضل حالاته';
      case PerformanceGrade.good:
        return 'جيد - أداء مقبول مع إمكانية تحسين طفيف';
      case PerformanceGrade.average:
        return 'متوسط - يحتاج إلى بعض التحسينات';
      case PerformanceGrade.poor:
        return 'ضعيف - يحتاج إلى تحسينات كبيرة';
      case PerformanceGrade.critical:
        return 'حرج - يحتاج إلى تحسين فوري';
    }
  }
}

/// عملية تحسين
class OptimizationAction {
  final String id;
  final OptimizationType type;
  final OptimizationResult result;
  final DateTime timestamp;

  OptimizationAction({
    required this.id,
    required this.type,
    required this.result,
    required this.timestamp,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.toString(),
      'result': result.toMap(),
      'timestamp': timestamp.toIso8601String(),
    };
  }

  factory OptimizationAction.fromMap(Map<String, dynamic> map) {
    return OptimizationAction(
      id: map['id'] ?? '',
      type: OptimizationType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => OptimizationType.memory,
      ),
      result: OptimizationResult.fromMap(map['result'] ?? {}),
      timestamp: DateTime.parse(map['timestamp']),
    );
  }
}

/// توصية التحسين
class OptimizationRecommendation {
  final OptimizationType type;
  final RecommendationPriority priority;
  final String title;
  final String description;
  final double estimatedImprovement;

  OptimizationRecommendation({
    required this.type,
    required this.priority,
    required this.title,
    required this.description,
    required this.estimatedImprovement,
  });

  Map<String, dynamic> toMap() {
    return {
      'type': type.toString(),
      'priority': priority.toString(),
      'title': title,
      'description': description,
      'estimatedImprovement': estimatedImprovement,
    };
  }

  factory OptimizationRecommendation.fromMap(Map<String, dynamic> map) {
    return OptimizationRecommendation(
      type: OptimizationType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => OptimizationType.memory,
      ),
      priority: RecommendationPriority.values.firstWhere(
        (e) => e.toString() == map['priority'],
        orElse: () => RecommendationPriority.medium,
      ),
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      estimatedImprovement: map['estimatedImprovement']?.toDouble() ?? 0.0,
    );
  }
}

/// تشخيص الأداء
class PerformanceDiagnostic {
  final String id;
  final DiagnosticType type;
  final DiagnosticSeverity severity;
  final String title;
  final String description;
  final String? solution;
  final DateTime detectedAt;

  PerformanceDiagnostic({
    required this.id,
    required this.type,
    required this.severity,
    required this.title,
    required this.description,
    this.solution,
    required this.detectedAt,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'type': type.toString(),
      'severity': severity.toString(),
      'title': title,
      'description': description,
      'solution': solution,
      'detectedAt': detectedAt.toIso8601String(),
    };
  }

  factory PerformanceDiagnostic.fromMap(Map<String, dynamic> map) {
    return PerformanceDiagnostic(
      id: map['id'] ?? '',
      type: DiagnosticType.values.firstWhere(
        (e) => e.toString() == map['type'],
        orElse: () => DiagnosticType.performance,
      ),
      severity: DiagnosticSeverity.values.firstWhere(
        (e) => e.toString() == map['severity'],
        orElse: () => DiagnosticSeverity.medium,
      ),
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      solution: map['solution'],
      detectedAt: DateTime.parse(map['detectedAt']),
    );
  }
}

/// إحصائيات الأداء
class PerformanceStatistics {
  final int totalOptimizations;
  final double averageImprovement;
  final Duration totalOptimizationTime;
  final Map<OptimizationType, int> optimizationsByType;
  final DateTime lastOptimization;
  final double currentOverallScore;

  PerformanceStatistics({
    required this.totalOptimizations,
    required this.averageImprovement,
    required this.totalOptimizationTime,
    required this.optimizationsByType,
    required this.lastOptimization,
    required this.currentOverallScore,
  });

  Map<String, dynamic> toMap() {
    return {
      'totalOptimizations': totalOptimizations,
      'averageImprovement': averageImprovement,
      'totalOptimizationTime': totalOptimizationTime.inMilliseconds,
      'optimizationsByType': optimizationsByType.map(
        (key, value) => MapEntry(key.toString(), value)),
      'lastOptimization': lastOptimization.toIso8601String(),
      'currentOverallScore': currentOverallScore,
    };
  }

  factory PerformanceStatistics.fromMap(Map<String, dynamic> map) {
    return PerformanceStatistics(
      totalOptimizations: map['totalOptimizations'] ?? 0,
      averageImprovement: map['averageImprovement']?.toDouble() ?? 0.0,
      totalOptimizationTime: Duration(
        milliseconds: map['totalOptimizationTime'] ?? 0),
      optimizationsByType: (map['optimizationsByType'] as Map? ?? {})
          .map((key, value) => MapEntry(
            OptimizationType.values.firstWhere(
              (e) => e.toString() == key,
              orElse: () => OptimizationType.memory,
            ),
            value as int,
          )),
      lastOptimization: DateTime.parse(map['lastOptimization']),
      currentOverallScore: map['currentOverallScore']?.toDouble() ?? 0.0,
    );
  }
}

/// أنواع التحسين
enum OptimizationType {
  memory,
  rendering,
  network,
  storage,
  battery,
  responseTime,
}

/// مستوى التحسين
enum OptimizationLevel {
  conservative,
  balanced,
  aggressive,
  maximum,
}

/// أنواع المقاييس
enum MetricType {
  memory,
  frameRate,
  responseTime,
  battery,
  network,
  cpu,
  storage,
}

/// تقييم الأداء
enum PerformanceGrade {
  excellent,
  good,
  average,
  poor,
  critical,
}

/// أولوية التوصية
enum RecommendationPriority {
  low,
  medium,
  high,
  critical,
}

/// نوع التشخيص
enum DiagnosticType {
  performance,
  memory,
  network,
  storage,
  battery,
  security,
}

/// شدة التشخيص
enum DiagnosticSeverity {
  info,
  warning,
  medium,
  high,
  critical,
}
