import 'package:flutter/foundation.dart';
import '../services/storage_service.dart';
import 'performance_models.dart';

/// خدمة إدارة الذاكرة الذكية
class MemoryManager {
  static const String _memoryDataKey = 'memory_data';
  static const String _cacheDataKey = 'cache_data';

  static bool _isInitialized = false;
  static Map<String, CacheEntry> _cache = {};
  static MemoryStats _stats = MemoryStats();

  /// تهيئة مدير الذاكرة
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadCacheData();
      await _loadMemoryStats();

      _isInitialized = true;
      debugPrint('🧠 تم تهيئة مدير الذاكرة الذكية');

      // بدء التنظيف التلقائي
      _startAutoCleanup();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة مدير الذاكرة: $e');
    }
  }

  /// إضافة عنصر للتخزين المؤقت
  static Future<void> cacheData({
    required String key,
    required dynamic data,
    Duration? expiry,
    CachePriority priority = CachePriority.normal,
  }) async {
    try {
      final entry = CacheEntry(
        key: key,
        data: data,
        createdAt: DateTime.now(),
        expiresAt: expiry != null ? DateTime.now().add(expiry) : null,
        priority: priority,
        accessCount: 0,
        lastAccessed: DateTime.now(),
      );

      _cache[key] = entry;
      _stats.totalCacheEntries = _cache.length;

      await _saveCacheData();

      debugPrint('💾 تم حفظ البيانات في التخزين المؤقت: $key');
    } catch (e) {
      debugPrint('❌ فشل في حفظ البيانات: $e');
    }
  }

  /// استرجاع بيانات من التخزين المؤقت
  static T? getCachedData<T>(String key) {
    try {
      final entry = _cache[key];
      if (entry == null) return null;

      // فحص انتهاء الصلاحية
      if (entry.expiresAt != null && DateTime.now().isAfter(entry.expiresAt!)) {
        _cache.remove(key);
        return null;
      }

      // تحديث إحصائيات الوصول
      entry.accessCount++;
      entry.lastAccessed = DateTime.now();

      return entry.data as T?;
    } catch (e) {
      debugPrint('❌ فشل في استرجاع البيانات: $e');
      return null;
    }
  }

  /// تنظيف التخزين المؤقت
  static Future<MemoryCleanupResult> cleanupCache() async {
    try {
      final startTime = DateTime.now();
      final initialCount = _cache.length;

      // إزالة البيانات المنتهية الصلاحية
      final expiredKeys = _cache.entries
          .where((entry) => entry.value.expiresAt != null &&
                          DateTime.now().isAfter(entry.value.expiresAt!))
          .map((entry) => entry.key)
          .toList();

      for (final key in expiredKeys) {
        _cache.remove(key);
      }

      // إزالة البيانات الأقل استخداماً إذا تجاوز الحد الأقصى
      if (_cache.length > 1000) {
        final sortedEntries = _cache.entries.toList()
          ..sort((a, b) => a.value.accessCount.compareTo(b.value.accessCount));

        final keysToRemove = sortedEntries
            .take(_cache.length - 800)
            .map((entry) => entry.key)
            .toList();

        for (final key in keysToRemove) {
          _cache.remove(key);
        }
      }

      final endTime = DateTime.now();
      final cleanedCount = initialCount - _cache.length;

      _stats.totalCacheEntries = _cache.length;
      _stats.lastCleanup = DateTime.now();

      await _saveCacheData();
      await _saveMemoryStats();

      final result = MemoryCleanupResult(
        cleanedEntries: cleanedCount,
        remainingEntries: _cache.length,
        duration: endTime.difference(startTime),
        freedMemoryMB: cleanedCount * 0.1, // تقدير
      );

      debugPrint('🧹 تم تنظيف التخزين المؤقت: $cleanedCount عنصر');
      return result;
    } catch (e) {
      debugPrint('❌ فشل في تنظيف التخزين المؤقت: $e');
      return MemoryCleanupResult(
        cleanedEntries: 0,
        remainingEntries: _cache.length,
        duration: Duration.zero,
        freedMemoryMB: 0,
      );
    }
  }

  /// تحسين استهلاك الذاكرة
  static Future<MemoryOptimizationResult> optimizeMemory() async {
    try {
      final startTime = DateTime.now();

      // تنظيف التخزين المؤقت
      final cleanupResult = await cleanupCache();

      // ضغط البيانات
      await _compressData();

      // تحسين هياكل البيانات
      await _optimizeDataStructures();

      final endTime = DateTime.now();

      final result = MemoryOptimizationResult(
        success: true,
        freedMemoryMB: cleanupResult.freedMemoryMB + 5.0,
        optimizationTime: endTime.difference(startTime),
        cleanupResult: cleanupResult,
      );

      debugPrint('⚡ تم تحسين الذاكرة: ${result.freedMemoryMB} MB');
      return result;
    } catch (e) {
      debugPrint('❌ فشل في تحسين الذاكرة: $e');
      return MemoryOptimizationResult(
        success: false,
        error: 'فشل في تحسين الذاكرة: $e',
      );
    }
  }

  /// الحصول على إحصائيات الذاكرة
  static MemoryStats getMemoryStats() {
    _stats.totalCacheEntries = _cache.length;
    _stats.cacheHitRate = _calculateCacheHitRate();
    _stats.estimatedMemoryUsageMB = _estimateMemoryUsage();

    return _stats;
  }

  /// مسح التخزين المؤقت بالكامل
  static Future<void> clearAllCache() async {
    try {
      final clearedCount = _cache.length;
      _cache.clear();

      _stats.totalCacheEntries = 0;
      _stats.lastCleanup = DateTime.now();

      await _saveCacheData();
      await _saveMemoryStats();

      debugPrint('🗑️ تم مسح جميع البيانات المؤقتة: $clearedCount عنصر');
    } catch (e) {
      debugPrint('❌ فشل في مسح التخزين المؤقت: $e');
    }
  }

  /// إزالة عنصر محدد من التخزين المؤقت
  static Future<bool> removeCachedData(String key) async {
    try {
      final removed = _cache.remove(key) != null;
      if (removed) {
        _stats.totalCacheEntries = _cache.length;
        await _saveCacheData();
        debugPrint('🗑️ تم إزالة البيانات من التخزين المؤقت: $key');
      }
      return removed;
    } catch (e) {
      debugPrint('❌ فشل في إزالة البيانات: $e');
      return false;
    }
  }

  /// فحص وجود بيانات في التخزين المؤقت
  static bool hasCachedData(String key) {
    final entry = _cache[key];
    if (entry == null) return false;

    // فحص انتهاء الصلاحية
    if (entry.expiresAt != null && DateTime.now().isAfter(entry.expiresAt!)) {
      _cache.remove(key);
      return false;
    }

    return true;
  }

  /// الحصول على قائمة مفاتيح التخزين المؤقت
  static List<String> getCacheKeys() {
    return _cache.keys.toList();
  }

  /// بدء التنظيف التلقائي
  static void _startAutoCleanup() {
    // تنظيف كل 10 دقائق
    Stream.periodic(const Duration(minutes: 10)).listen((_) async {
      await cleanupCache();
    });
  }

  /// ضغط البيانات
  static Future<void> _compressData() async {
    // محاكاة ضغط البيانات
    await Future.delayed(const Duration(milliseconds: 200));
  }

  /// تحسين هياكل البيانات
  static Future<void> _optimizeDataStructures() async {
    // محاكاة تحسين هياكل البيانات
    await Future.delayed(const Duration(milliseconds: 150));
  }

  /// حساب معدل نجاح التخزين المؤقت
  static double _calculateCacheHitRate() {
    if (_cache.isEmpty) return 0.0;

    final totalAccess = _cache.values
        .map((entry) => entry.accessCount)
        .fold(0, (sum, count) => sum + count);

    return totalAccess > 0 ? (totalAccess / _cache.length) * 10 : 0.0;
  }

  /// تقدير استهلاك الذاكرة
  static double _estimateMemoryUsage() {
    // تقدير بسيط: كل عنصر = 0.1 MB
    return _cache.length * 0.1;
  }

  // Data persistence methods
  static Future<void> _saveCacheData() async {
    try {
      final data = _cache.map((key, value) => MapEntry(key, value.toMap()));
      await StorageService.saveData(_cacheDataKey, data);
    } catch (e) {
      debugPrint('❌ فشل في حفظ بيانات التخزين المؤقت: $e');
    }
  }

  static Future<void> _loadCacheData() async {
    try {
      final data = await StorageService.getData(_cacheDataKey);
      if (data != null && data is Map) {
        _cache = data.map((key, value) =>
            MapEntry(key, CacheEntry.fromMap(value)));
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل بيانات التخزين المؤقت: $e');
    }
  }

  static Future<void> _saveMemoryStats() async {
    try {
      await StorageService.saveData(_memoryDataKey, _stats.toMap());
    } catch (e) {
      debugPrint('❌ فشل في حفظ إحصائيات الذاكرة: $e');
    }
  }

  static Future<void> _loadMemoryStats() async {
    try {
      final data = await StorageService.getData(_memoryDataKey);
      if (data != null) {
        _stats = MemoryStats.fromMap(data);
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل إحصائيات الذاكرة: $e');
    }
  }

  // Getters
  static bool get isInitialized => _isInitialized;
  static int get cacheSize => _cache.length;
  static double get estimatedMemoryUsage => _estimateMemoryUsage();
}

/// عنصر التخزين المؤقت
class CacheEntry {
  final String key;
  final dynamic data;
  final DateTime createdAt;
  final DateTime? expiresAt;
  final CachePriority priority;
  int accessCount;
  DateTime lastAccessed;

  CacheEntry({
    required this.key,
    required this.data,
    required this.createdAt,
    this.expiresAt,
    required this.priority,
    required this.accessCount,
    required this.lastAccessed,
  });

  Map<String, dynamic> toMap() {
    return {
      'key': key,
      'data': data,
      'createdAt': createdAt.toIso8601String(),
      'expiresAt': expiresAt?.toIso8601String(),
      'priority': priority.toString(),
      'accessCount': accessCount,
      'lastAccessed': lastAccessed.toIso8601String(),
    };
  }

  factory CacheEntry.fromMap(Map<String, dynamic> map) {
    return CacheEntry(
      key: map['key'] ?? '',
      data: map['data'],
      createdAt: DateTime.parse(map['createdAt']),
      expiresAt: map['expiresAt'] != null
          ? DateTime.parse(map['expiresAt'])
          : null,
      priority: CachePriority.values.firstWhere(
        (e) => e.toString() == map['priority'],
        orElse: () => CachePriority.normal,
      ),
      accessCount: map['accessCount'] ?? 0,
      lastAccessed: DateTime.parse(map['lastAccessed']),
    );
  }
}

/// إحصائيات الذاكرة
class MemoryStats {
  int totalCacheEntries;
  double cacheHitRate;
  double estimatedMemoryUsageMB;
  DateTime? lastCleanup;

  MemoryStats({
    this.totalCacheEntries = 0,
    this.cacheHitRate = 0.0,
    this.estimatedMemoryUsageMB = 0.0,
    this.lastCleanup,
  });

  Map<String, dynamic> toMap() {
    return {
      'totalCacheEntries': totalCacheEntries,
      'cacheHitRate': cacheHitRate,
      'estimatedMemoryUsageMB': estimatedMemoryUsageMB,
      'lastCleanup': lastCleanup?.toIso8601String(),
    };
  }

  factory MemoryStats.fromMap(Map<String, dynamic> map) {
    return MemoryStats(
      totalCacheEntries: map['totalCacheEntries'] ?? 0,
      cacheHitRate: map['cacheHitRate']?.toDouble() ?? 0.0,
      estimatedMemoryUsageMB: map['estimatedMemoryUsageMB']?.toDouble() ?? 0.0,
      lastCleanup: map['lastCleanup'] != null
          ? DateTime.parse(map['lastCleanup'])
          : null,
    );
  }
}

/// نتيجة تنظيف الذاكرة
class MemoryCleanupResult {
  final int cleanedEntries;
  final int remainingEntries;
  final Duration duration;
  final double freedMemoryMB;

  MemoryCleanupResult({
    required this.cleanedEntries,
    required this.remainingEntries,
    required this.duration,
    required this.freedMemoryMB,
  });
}

/// نتيجة تحسين الذاكرة
class MemoryOptimizationResult {
  final bool success;
  final double freedMemoryMB;
  final Duration? optimizationTime;
  final MemoryCleanupResult? cleanupResult;
  final String? error;

  MemoryOptimizationResult({
    required this.success,
    this.freedMemoryMB = 0.0,
    this.optimizationTime,
    this.cleanupResult,
    this.error,
  });
}

/// أولوية التخزين المؤقت
enum CachePriority {
  low,
  normal,
  high,
  critical,
}
