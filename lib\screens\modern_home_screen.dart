import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'dart:ui';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../widgets/modern_ui_components.dart';
import '../core/auth/auth_service.dart';
import '../core/analytics/usage_analytics.dart';

/// الشاشة الرئيسية الحديثة مستوحاة من التصميم الجديد
class ModernHomeScreen extends ConsumerStatefulWidget {
  const ModernHomeScreen({super.key});

  @override
  ConsumerState<ModernHomeScreen> createState() => _ModernHomeScreenState();
}

class _ModernHomeScreenState extends ConsumerState<ModernHomeScreen>
    with TickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  
  final PageController _pageController = PageController();
  int _currentPage = 0;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _trackScreenView();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1200),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: const Interval(0.2, 0.8, curve: Curves.easeOut),
    ));

    _animationController.forward();
  }

  Future<void> _trackScreenView() async {
    await UsageAnalytics.trackScreenView('modern_home');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: Container(
        decoration: BoxDecoration(
          gradient: AppColors.backgroundGradient,
        ),
        child: SafeArea(
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return FadeTransition(
                opacity: _fadeAnimation,
                child: SlideTransition(
                  position: _slideAnimation,
                  child: _buildContent(),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildContent() {
    return CustomScrollView(
      slivers: [
        _buildModernAppBar(),
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildWelcomeSection(),
                const SizedBox(height: 32),
                _buildQuickActionsSection(),
                const SizedBox(height: 32),
                _buildFeaturedToolsSection(),
                const SizedBox(height: 32),
                _buildStatsSection(),
                const SizedBox(height: 32),
                _buildRecentActivitySection(),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModernAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      backgroundColor: Colors.transparent,
      elevation: 0,
      flexibleSpace: FlexibleSpaceBar(
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppColors.primaryPurple.withValues(alpha: 0.1),
                AppColors.electricBlue.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: ClipRRect(
            child: BackdropFilter(
              filter: ImageFilter.blur(sigmaX: 10, sigmaY: 10),
              child: Container(
                decoration: BoxDecoration(
                  gradient: AppColors.glassGradient,
                  border: Border(
                    bottom: BorderSide(
                      color: AppColors.electricBlue.withValues(alpha: 0.2),
                      width: 1,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ),
      ),
      title: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              gradient: AppColors.primaryGradient,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: AppColors.primaryPurple.withValues(alpha: 0.3),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: const Icon(
              Icons.auto_awesome,
              color: AppColors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Text(
            'DeepSeek AI',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
              letterSpacing: -0.5,
            ),
          ),
        ],
      ),
      actions: [
        ModernUIComponents.glassCard(
          padding: const EdgeInsets.all(8),
          borderRadius: BorderRadius.circular(12),
          child: IconButton(
            icon: const Icon(Icons.notifications_outlined),
            color: AppColors.electricBlue,
            onPressed: () {
              // فتح الإشعارات
            },
          ),
        ),
        const SizedBox(width: 8),
        ModernUIComponents.glassCard(
          padding: const EdgeInsets.all(8),
          borderRadius: BorderRadius.circular(12),
          child: IconButton(
            icon: const Icon(Icons.person_outline),
            color: AppColors.electricBlue,
            onPressed: () {
              Navigator.pushNamed(context, '/profile');
            },
          ),
        ),
        const SizedBox(width: 16),
      ],
    );
  }

  Widget _buildWelcomeSection() {
    final user = AuthService.currentUser;
    final userName = user?.displayName ?? 'المستخدم';
    
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'مرحباً، $userName! 👋',
                      style: TextStyle(
                        color: AppColors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      'ماذا تريد أن تنجز اليوم؟',
                      style: TextStyle(
                        color: AppColors.textSecondary,
                        fontSize: 16,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: AppColors.glowGradient,
                  borderRadius: BorderRadius.circular(20),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.glowPink.withValues(alpha: 0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 8),
                    ),
                  ],
                ),
                child: const Icon(
                  Icons.rocket_launch,
                  color: AppColors.white,
                  size: 28,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickActionsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'إجراءات سريعة',
          style: TextStyle(
            color: AppColors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        SizedBox(
          height: 120,
          child: ListView(
            scrollDirection: Axis.horizontal,
            children: [
              _buildQuickActionCard(
                title: 'دردشة ذكية',
                icon: Icons.chat_bubble_outline,
                gradient: AppColors.primaryGradient,
                onTap: () => Navigator.pushNamed(context, '/chat'),
              ),
              _buildQuickActionCard(
                title: 'إنشاء صورة',
                icon: Icons.image_outlined,
                gradient: AppColors.glowGradient,
                onTap: () => Navigator.pushNamed(context, '/create_image'),
              ),
              _buildQuickActionCard(
                title: 'تحليل البيانات',
                icon: Icons.analytics_outlined,
                gradient: LinearGradient(
                  colors: [AppColors.electricBlue, AppColors.primaryPurple],
                ),
                onTap: () => Navigator.pushNamed(context, '/analyze_data'),
              ),
              _buildQuickActionCard(
                title: 'الدردشة الصوتية',
                icon: Icons.mic_outlined,
                gradient: LinearGradient(
                  colors: [AppColors.glowPink, AppColors.lightPurple],
                ),
                onTap: () => Navigator.pushNamed(context, '/voice_chat'),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildQuickActionCard({
    required String title,
    required IconData icon,
    required Gradient gradient,
    required VoidCallback onTap,
  }) {
    return Container(
      width: 140,
      margin: const EdgeInsets.only(right: 16),
      child: ModernUIComponents.glassCard(
        padding: const EdgeInsets.all(16),
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  gradient: gradient,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: AppColors.primaryPurple.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Icon(
                  icon,
                  color: AppColors.white,
                  size: 24,
                ),
              ),
              const SizedBox(height: 12),
              Text(
                title,
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeaturedToolsSection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'الأدوات المميزة',
              style: TextStyle(
                color: AppColors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            TextButton(
              onPressed: () {
                // عرض جميع الأدوات
              },
              child: Text(
                'عرض الكل',
                style: TextStyle(
                  color: AppColors.electricBlue,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        ModernUIComponents.modernToolCard(
          title: 'تلخيص النصوص',
          description: 'لخص النصوص الطويلة في نقاط مفيدة',
          icon: Icons.summarize_outlined,
          onTap: () => Navigator.pushNamed(context, '/summarize_text'),
          badge: ModernUIComponents.modernBadge(
            text: 'جديد',
            backgroundColor: AppColors.glowPink,
          ),
        ),
        ModernUIComponents.modernToolCard(
          title: 'الترجمة الذكية',
          description: 'ترجم النصوص بدقة عالية',
          icon: Icons.translate_outlined,
          onTap: () => Navigator.pushNamed(context, '/smart_translation'),
        ),
        ModernUIComponents.modernToolCard(
          title: 'تحليل الصور',
          description: 'اكتشف محتوى الصور وحللها',
          icon: Icons.image_search_outlined,
          onTap: () => Navigator.pushNamed(context, '/image_analysis'),
          badge: ModernUIComponents.modernBadge(
            text: 'شائع',
            backgroundColor: AppColors.electricBlue,
          ),
        ),
      ],
    );
  }

  Widget _buildStatsSection() {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'إحصائياتك',
            style: TextStyle(
              color: AppColors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  title: 'المحادثات',
                  value: '127',
                  icon: Icons.chat_outlined,
                  color: AppColors.electricBlue,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  title: 'الصور المنشأة',
                  value: '43',
                  icon: Icons.image_outlined,
                  color: AppColors.glowPink,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  title: 'النصوص الملخصة',
                  value: '89',
                  icon: Icons.summarize_outlined,
                  color: AppColors.lightPurple,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildStatCard(
                  title: 'الترجمات',
                  value: '156',
                  icon: Icons.translate_outlined,
                  color: AppColors.primaryPurple,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            color.withValues(alpha: 0.1),
            color.withValues(alpha: 0.05),
          ],
        ),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              color: AppColors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              color: AppColors.textSecondary,
              fontSize: 12,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildRecentActivitySection() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'النشاط الأخير',
          style: TextStyle(
            color: AppColors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 16),
        ModernUIComponents.glassCard(
          padding: const EdgeInsets.all(20),
          child: Column(
            children: [
              _buildActivityItem(
                title: 'تم إنشاء صورة جديدة',
                subtitle: 'منذ ساعتين',
                icon: Icons.image_outlined,
                color: AppColors.glowPink,
              ),
              const Divider(color: AppColors.darkGrey, height: 32),
              _buildActivityItem(
                title: 'تم تلخيص مستند',
                subtitle: 'منذ 4 ساعات',
                icon: Icons.summarize_outlined,
                color: AppColors.electricBlue,
              ),
              const Divider(color: AppColors.darkGrey, height: 32),
              _buildActivityItem(
                title: 'محادثة ذكية جديدة',
                subtitle: 'أمس',
                icon: Icons.chat_outlined,
                color: AppColors.lightPurple,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildActivityItem({
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
  }) {
    return Row(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(10),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
              Text(
                subtitle,
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
        Icon(
          Icons.arrow_forward_ios,
          color: AppColors.textSecondary,
          size: 14,
        ),
      ],
    );
  }
}
