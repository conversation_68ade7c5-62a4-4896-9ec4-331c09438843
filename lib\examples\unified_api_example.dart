import 'package:flutter/material.dart';
import '../core/services/unified_api_gateway.dart';
import '../core/services/api_provider_service.dart';
import '../core/models/api_provider_model.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../widgets/model_selector_widget.dart';
import '../widgets/animated_button.dart';

/// مثال شامل لاستخدام نظام APIs الموحد
class UnifiedApiExample extends StatefulWidget {
  const UnifiedApiExample({super.key});

  @override
  State<UnifiedApiExample> createState() => _UnifiedApiExampleState();
}

class _UnifiedApiExampleState extends State<UnifiedApiExample> {
  final TextEditingController _messageController = TextEditingController();
  String _response = '';
  bool _isLoading = false;
  String? _selectedProviderId;
  String? _selectedModelId;

  @override
  void initState() {
    super.initState();
    _initializeApi();
  }

  Future<void> _initializeApi() async {
    try {
      await UnifiedApiGateway.initialize();
      final activeProvider = UnifiedApiGateway.getActiveProvider();
      final activeModel = UnifiedApiGateway.getActiveModel();
      
      setState(() {
        _selectedProviderId = activeProvider?.id;
        _selectedModelId = activeModel?.id;
      });
    } catch (e) {
      debugPrint('خطأ في تهيئة API: $e');
    }
  }

  Future<void> _sendMessage() async {
    if (_messageController.text.trim().isEmpty) return;

    setState(() {
      _isLoading = true;
      _response = '';
    });

    try {
      final response = await UnifiedApiGateway.sendChatRequest(
        message: _messageController.text.trim(),
        temperature: 0.7,
        maxTokens: 1000,
      );

      setState(() {
        _response = response['content'] as String;
      });
    } catch (e) {
      setState(() {
        _response = 'خطأ: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _generateImage() async {
    if (_messageController.text.trim().isEmpty) return;

    setState(() {
      _isLoading = true;
      _response = '';
    });

    try {
      final imageUrl = await UnifiedApiGateway.sendImageGenerationRequest(
        prompt: _messageController.text.trim(),
        size: '1024x1024',
        quality: 'standard',
      );

      setState(() {
        _response = 'تم إنشاء الصورة: $imageUrl';
      });
    } catch (e) {
      setState(() {
        _response = 'خطأ في إنشاء الصورة: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _refreshModels() async {
    setState(() => _isLoading = true);
    
    try {
      await UnifiedApiGateway.refreshAllModels();
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تحديث النماذج بنجاح'),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('خطأ في تحديث النماذج: $e'),
          backgroundColor: Colors.red,
        ),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('مثال APIs الموحدة'),
        backgroundColor: AppColors.darkGrey,
        elevation: 0,
        actions: [
          IconButton(
            onPressed: _refreshModels,
            icon: _isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Icon(Icons.refresh),
          ),
        ],
      ),
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppColors.darkPurple.withValues(alpha: 0.2),
              AppColors.background,
              AppColors.primaryPurple.withValues(alpha: 0.1),
            ],
          ),
        ),
        child: Column(
          children: [
            // ويدجت اختيار النموذج
            Container(
              margin: const EdgeInsets.all(16),
              child: ModelSelectorWidget(
                compactMode: true,
                onModelSelected: (providerId, modelId) {
                  setState(() {
                    _selectedProviderId = providerId;
                    _selectedModelId = modelId;
                  });
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text('تم اختيار النموذج: $modelId'),
                      backgroundColor: Colors.green,
                    ),
                  );
                },
              ),
            ),

            // معلومات الحالة
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [AppColors.darkGrey, AppColors.midGrey],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.lightGrey),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'حالة النظام',
                    style: AppTextStyles.body.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  _buildStatusInfo(),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // منطقة الإدخال
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16),
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [AppColors.darkGrey, AppColors.midGrey],
                ),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: AppColors.lightGrey),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'اختبار النظام',
                    style: AppTextStyles.body.copyWith(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 12),
                  TextField(
                    controller: _messageController,
                    maxLines: 3,
                    style: AppTextStyles.body.copyWith(color: Colors.white),
                    decoration: InputDecoration(
                      hintText: 'اكتب رسالتك أو وصف الصورة هنا...',
                      hintStyle: AppTextStyles.body.copyWith(
                        color: Colors.white60,
                      ),
                      filled: true,
                      fillColor: AppColors.background.withValues(alpha: 0.5),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide.none,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Expanded(
                        child: AnimatedButton(
                          onTap: _isLoading ? null : _sendMessage,
                          gradient: LinearGradient(
                            colors: [AppColors.primaryPurple, AppColors.lightPurple],
                          ),
                          borderRadius: BorderRadius.circular(8),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.chat, color: Colors.white, size: 20),
                              const SizedBox(width: 8),
                              Text(
                                'إرسال رسالة',
                                style: AppTextStyles.body.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: AnimatedButton(
                          onTap: _isLoading ? null : _generateImage,
                          gradient: LinearGradient(
                            colors: [Colors.orange, Colors.deepOrange],
                          ),
                          borderRadius: BorderRadius.circular(8),
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              const Icon(Icons.image, color: Colors.white, size: 20),
                              const SizedBox(width: 8),
                              Text(
                                'إنشاء صورة',
                                style: AppTextStyles.body.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            const SizedBox(height: 16),

            // منطقة النتائج
            Expanded(
              child: Container(
                margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [AppColors.darkGrey, AppColors.midGrey],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: AppColors.lightGrey),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          'النتيجة',
                          style: AppTextStyles.body.copyWith(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        if (_isLoading)
                          const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Expanded(
                      child: Container(
                        width: double.infinity,
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: AppColors.background.withValues(alpha: 0.5),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: AppColors.lightGrey.withValues(alpha: 0.3),
                          ),
                        ),
                        child: SingleChildScrollView(
                          child: Text(
                            _response.isEmpty ? 'لا توجد نتائج بعد...' : _response,
                            style: AppTextStyles.body.copyWith(
                              color: _response.isEmpty ? Colors.white60 : Colors.white,
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatusInfo() {
    final stats = UnifiedApiGateway.getUsageStats();
    
    return Column(
      children: [
        _buildStatusRow('المقدم النشط', stats['active_provider'] ?? 'غير محدد'),
        _buildStatusRow('النموذج النشط', stats['active_model'] ?? 'غير محدد'),
        _buildStatusRow('المقدمين المتاحين', '${stats['active_providers']}/${stats['total_providers']}'),
        _buildStatusRow('النماذج المتاحة', '${stats['available_models']}'),
      ],
    );
  }

  Widget _buildStatusRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Text(
            '$label: ',
            style: AppTextStyles.caption.copyWith(
              color: Colors.white70,
            ),
          ),
          Text(
            value,
            style: AppTextStyles.caption.copyWith(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}
