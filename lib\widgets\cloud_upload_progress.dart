import 'package:flutter/material.dart';
import '../utils/app_colors.dart';
import '../widgets/modern_ui_components.dart';

/// مؤشر تقدم رفع الملفات للسحابة
class CloudUploadProgress extends StatefulWidget {
  final String fileName;
  final double progress;
  final bool isCompleted;
  final bool hasError;
  final String? errorMessage;
  final VoidCallback? onCancel;
  final VoidCallback? onRetry;

  const CloudUploadProgress({
    super.key,
    required this.fileName,
    required this.progress,
    this.isCompleted = false,
    this.hasError = false,
    this.errorMessage,
    this.onCancel,
    this.onRetry,
  });

  @override
  State<CloudUploadProgress> createState() => _CloudUploadProgressState();
}

class _CloudUploadProgressState extends State<CloudUploadProgress>
    with TickerProviderStateMixin {
  late AnimationController _progressController;
  late AnimationController _pulseController;
  late Animation<double> _progressAnimation;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
  }

  @override
  void dispose() {
    _progressController.dispose();
    _pulseController.dispose();
    super.dispose();
  }

  void _initializeAnimations() {
    _progressController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );

    _pulseController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    _progressAnimation = Tween<double>(
      begin: 0.0,
      end: widget.progress,
    ).animate(CurvedAnimation(
      parent: _progressController,
      curve: Curves.easeInOut,
    ));

    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));

    _progressController.forward();
    
    if (!widget.isCompleted && !widget.hasError) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  void didUpdateWidget(CloudUploadProgress oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (oldWidget.progress != widget.progress) {
      _progressAnimation = Tween<double>(
        begin: oldWidget.progress,
        end: widget.progress,
      ).animate(CurvedAnimation(
        parent: _progressController,
        curve: Curves.easeInOut,
      ));
      _progressController.reset();
      _progressController.forward();
    }

    if (widget.isCompleted || widget.hasError) {
      _pulseController.stop();
    } else if (!oldWidget.isCompleted && !oldWidget.hasError) {
      _pulseController.repeat(reverse: true);
    }
  }

  @override
  Widget build(BuildContext context) {
    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildHeader(),
          const SizedBox(height: 12),
          _buildProgressBar(),
          const SizedBox(height: 8),
          _buildStatusText(),
          if (widget.hasError) ...[
            const SizedBox(height: 12),
            _buildErrorSection(),
          ],
        ],
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      children: [
        AnimatedBuilder(
          animation: _pulseAnimation,
          builder: (context, child) {
            return Transform.scale(
              scale: widget.isCompleted || widget.hasError 
                  ? 1.0 
                  : _pulseAnimation.value,
              child: Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  gradient: _getStatusGradient(),
                  borderRadius: BorderRadius.circular(10),
                  boxShadow: [
                    BoxShadow(
                      color: _getStatusColor().withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Icon(
                  _getStatusIcon(),
                  color: AppColors.white,
                  size: 20,
                ),
              ),
            );
          },
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                widget.fileName,
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              ),
              const SizedBox(height: 2),
              Text(
                _getStatusMessage(),
                style: TextStyle(
                  color: AppColors.textSecondary,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
        if (!widget.isCompleted && !widget.hasError)
          IconButton(
            icon: Icon(
              Icons.close,
              color: AppColors.textSecondary,
              size: 20,
            ),
            onPressed: widget.onCancel,
          ),
      ],
    );
  }

  Widget _buildProgressBar() {
    return AnimatedBuilder(
      animation: _progressAnimation,
      builder: (context, child) {
        return Container(
          height: 6,
          decoration: BoxDecoration(
            color: AppColors.darkGrey,
            borderRadius: BorderRadius.circular(3),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(3),
            child: LinearProgressIndicator(
              value: widget.hasError ? 0 : _progressAnimation.value,
              backgroundColor: Colors.transparent,
              valueColor: AlwaysStoppedAnimation<Color>(_getStatusColor()),
            ),
          ),
        );
      },
    );
  }

  Widget _buildStatusText() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          _getDetailedStatus(),
          style: TextStyle(
            color: AppColors.textSecondary,
            fontSize: 11,
          ),
        ),
        Text(
          '${(widget.progress * 100).toInt()}%',
          style: TextStyle(
            color: _getStatusColor(),
            fontSize: 11,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }

  Widget _buildErrorSection() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppColors.error.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: AppColors.error.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.error_outline,
                color: AppColors.error,
                size: 16,
              ),
              const SizedBox(width: 8),
              Text(
                'خطأ في الرفع',
                style: TextStyle(
                  color: AppColors.error,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ),
          if (widget.errorMessage != null) ...[
            const SizedBox(height: 4),
            Text(
              widget.errorMessage!,
              style: TextStyle(
                color: AppColors.textSecondary,
                fontSize: 11,
              ),
            ),
          ],
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: ModernUIComponents.modernButton(
                  text: 'إعادة المحاولة',
                  onPressed: widget.onRetry,
                  height: 32,
                  fontSize: 12,
                  gradient: LinearGradient(
                    colors: [AppColors.error, AppColors.error.withValues(alpha: 0.8)],
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ModernUIComponents.modernButton(
                  text: 'إلغاء',
                  onPressed: widget.onCancel,
                  height: 32,
                  fontSize: 12,
                  gradient: LinearGradient(
                    colors: [AppColors.darkGrey, AppColors.midGrey],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  IconData _getStatusIcon() {
    if (widget.hasError) return Icons.error;
    if (widget.isCompleted) return Icons.check;
    return Icons.cloud_upload;
  }

  Color _getStatusColor() {
    if (widget.hasError) return AppColors.error;
    if (widget.isCompleted) return AppColors.success;
    return AppColors.primaryPurple;
  }

  Gradient _getStatusGradient() {
    if (widget.hasError) {
      return LinearGradient(
        colors: [AppColors.error, AppColors.error.withValues(alpha: 0.8)],
      );
    }
    if (widget.isCompleted) {
      return LinearGradient(
        colors: [AppColors.success, AppColors.success.withValues(alpha: 0.8)],
      );
    }
    return AppColors.primaryGradient;
  }

  String _getStatusMessage() {
    if (widget.hasError) return 'فشل في الرفع';
    if (widget.isCompleted) return 'تم الرفع بنجاح';
    return 'جاري الرفع...';
  }

  String _getDetailedStatus() {
    if (widget.hasError) return 'حدث خطأ أثناء رفع الملف';
    if (widget.isCompleted) return 'تم رفع الملف إلى السحابة';
    
    final mbUploaded = (widget.progress * 10).toStringAsFixed(1);
    return 'تم رفع $mbUploaded من 10.0 MB';
  }
}

/// مؤشر تقدم متعدد الملفات
class MultiFileUploadProgress extends StatelessWidget {
  final List<CloudUploadItem> uploads;
  final VoidCallback? onCancelAll;

  const MultiFileUploadProgress({
    super.key,
    required this.uploads,
    this.onCancelAll,
  });

  @override
  Widget build(BuildContext context) {
    final completedCount = uploads.where((u) => u.isCompleted).length;
    final totalCount = uploads.length;
    final overallProgress = uploads.isEmpty 
        ? 0.0 
        : uploads.map((u) => u.progress).reduce((a, b) => a + b) / uploads.length;

    return ModernUIComponents.glassCard(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'رفع الملفات ($completedCount/$totalCount)',
                style: TextStyle(
                  color: AppColors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
              if (uploads.any((u) => !u.isCompleted && !u.hasError))
                TextButton(
                  onPressed: onCancelAll,
                  child: Text(
                    'إلغاء الكل',
                    style: TextStyle(color: AppColors.error),
                  ),
                ),
            ],
          ),
          const SizedBox(height: 16),
          ModernUIComponents.modernProgressBar(
            progress: overallProgress,
            showPercentage: true,
            height: 8,
          ),
          const SizedBox(height: 16),
          ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: uploads.length,
            separatorBuilder: (context, index) => const SizedBox(height: 12),
            itemBuilder: (context, index) {
              final upload = uploads[index];
              return CloudUploadProgress(
                fileName: upload.fileName,
                progress: upload.progress,
                isCompleted: upload.isCompleted,
                hasError: upload.hasError,
                errorMessage: upload.errorMessage,
                onCancel: upload.onCancel,
                onRetry: upload.onRetry,
              );
            },
          ),
        ],
      ),
    );
  }
}

/// عنصر رفع ملف
class CloudUploadItem {
  final String fileName;
  final double progress;
  final bool isCompleted;
  final bool hasError;
  final String? errorMessage;
  final VoidCallback? onCancel;
  final VoidCallback? onRetry;

  CloudUploadItem({
    required this.fileName,
    required this.progress,
    this.isCompleted = false,
    this.hasError = false,
    this.errorMessage,
    this.onCancel,
    this.onRetry,
  });
}
