import 'package:flutter/foundation.dart';
import '../services/storage_service.dart';

/// خدمة الدفع المتكاملة
class PaymentService {
  static const String _subscriptionsKey = 'user_subscriptions';
  static const String _paymentsKey = 'payment_history';

  static List<Subscription> _subscriptions = [];
  static List<PaymentRecord> _paymentHistory = [];
  static bool _isInitialized = false;

  /// تهيئة خدمة الدفع
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadSubscriptions();
      await _loadPaymentHistory();

      _isInitialized = true;
      debugPrint('💳 تم تهيئة خدمة الدفع');
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة خدمة الدفع: $e');
    }
  }

  /// معالجة الدفع
  static Future<PaymentResult> processPayment({
    required String planId,
    required PaymentMethod method,
    required double amount,
    String? promoCode,
  }) async {
    try {
      // محاكاة معالجة الدفع
      await Future.delayed(const Duration(seconds: 2));

      final paymentId = 'pay_${DateTime.now().millisecondsSinceEpoch}';

      // تطبيق كود الخصم
      double finalAmount = amount;
      if (promoCode != null) {
        finalAmount = _applyPromoCode(amount, promoCode);
      }

      // إنشاء سجل الدفع
      final payment = PaymentRecord(
        id: paymentId,
        planId: planId,
        amount: finalAmount,
        originalAmount: amount,
        method: method,
        status: PaymentStatus.completed,
        timestamp: DateTime.now(),
        promoCode: promoCode,
      );

      _paymentHistory.add(payment);
      await _savePaymentHistory();

      // إنشاء الاشتراك
      final subscription = Subscription(
        id: 'sub_${DateTime.now().millisecondsSinceEpoch}',
        planId: planId,
        paymentId: paymentId,
        startDate: DateTime.now(),
        endDate: _calculateEndDate(planId),
        status: SubscriptionStatus.active,
        autoRenew: true,
      );

      _subscriptions.add(subscription);
      await _saveSubscriptions();

      debugPrint('✅ تم الدفع بنجاح: $paymentId');

      return PaymentResult(
        success: true,
        paymentId: paymentId,
        subscriptionId: subscription.id,
        message: 'تم الدفع بنجاح',
      );

    } catch (e) {
      debugPrint('❌ فشل في الدفع: $e');
      return PaymentResult(
        success: false,
        message: 'فشل في معالجة الدفع: $e',
      );
    }
  }

  /// الحصول على الاشتراك النشط
  static Subscription? getActiveSubscription() {
    return _subscriptions
        .where((sub) => sub.status == SubscriptionStatus.active)
        .where((sub) => sub.endDate.isAfter(DateTime.now()))
        .firstOrNull;
  }

  /// التحقق من صحة الاشتراك
  static bool hasValidSubscription() {
    final activeSub = getActiveSubscription();
    return activeSub != null;
  }

  /// إلغاء الاشتراك
  static Future<bool> cancelSubscription(String subscriptionId) async {
    try {
      final index = _subscriptions.indexWhere((sub) => sub.id == subscriptionId);
      if (index != -1) {
        _subscriptions[index] = _subscriptions[index].copyWith(
          status: SubscriptionStatus.cancelled,
          autoRenew: false,
        );
        await _saveSubscriptions();
        debugPrint('✅ تم إلغاء الاشتراك: $subscriptionId');
        return true;
      }
      return false;
    } catch (e) {
      debugPrint('❌ فشل في إلغاء الاشتراك: $e');
      return false;
    }
  }

  /// تجديد الاشتراك
  static Future<PaymentResult> renewSubscription(String subscriptionId) async {
    try {
      final subscription = _subscriptions.firstWhere((sub) => sub.id == subscriptionId);
      final plan = getSubscriptionPlan(subscription.planId);

      return await processPayment(
        planId: subscription.planId,
        method: PaymentMethod.card, // افتراضي
        amount: plan.price,
      );
    } catch (e) {
      return PaymentResult(
        success: false,
        message: 'فشل في تجديد الاشتراك: $e',
      );
    }
  }

  /// الحصول على خطط الاشتراك
  static List<SubscriptionPlan> getSubscriptionPlans() {
    return [
      SubscriptionPlan(
        id: 'basic_monthly',
        name: 'الخطة الأساسية - شهرية',
        description: 'وصول محدود للأدوات الأساسية',
        price: 29.99,
        duration: const Duration(days: 30),
        features: [
          'دردشة ذكية محدودة',
          'إنشاء صور أساسي',
          'تلخيص النصوص',
          'دعم فني أساسي',
        ],
        tokensLimit: 10000,
        priority: PlanPriority.normal,
      ),
      SubscriptionPlan(
        id: 'pro_monthly',
        name: 'الخطة المتقدمة - شهرية',
        description: 'وصول كامل لجميع الأدوات',
        price: 59.99,
        duration: const Duration(days: 30),
        features: [
          'دردشة ذكية غير محدودة',
          'إنشاء صور متقدم',
          'جميع أدوات التحليل',
          'دردشة صوتية',
          'العمل بدون إنترنت',
          'دعم فني متقدم',
        ],
        tokensLimit: 100000,
        priority: PlanPriority.high,
      ),
      SubscriptionPlan(
        id: 'enterprise_monthly',
        name: 'خطة المؤسسات - شهرية',
        description: 'حلول متقدمة للمؤسسات',
        price: 199.99,
        duration: const Duration(days: 30),
        features: [
          'جميع ميزات الخطة المتقدمة',
          'API مخصص',
          'تكامل مؤسسي',
          'تحليلات متقدمة',
          'دعم فني 24/7',
          'تدريب مخصص',
        ],
        tokensLimit: 1000000,
        priority: PlanPriority.enterprise,
      ),
    ];
  }

  /// الحصول على خطة اشتراك محددة
  static SubscriptionPlan getSubscriptionPlan(String planId) {
    return getSubscriptionPlans().firstWhere(
      (plan) => plan.id == planId,
      orElse: () => getSubscriptionPlans().first,
    );
  }

  /// تطبيق كود الخصم
  static double _applyPromoCode(double amount, String promoCode) {
    final promoCodes = {
      'WELCOME20': 0.20, // خصم 20%
      'STUDENT50': 0.50, // خصم 50%
      'FIRST10': 0.10,   // خصم 10%
      'SAVE30': 0.30,    // خصم 30%
    };

    final discount = promoCodes[promoCode.toUpperCase()] ?? 0.0;
    return amount * (1 - discount);
  }

  /// حساب تاريخ انتهاء الاشتراك
  static DateTime _calculateEndDate(String planId) {
    final plan = getSubscriptionPlan(planId);
    return DateTime.now().add(plan.duration);
  }

  /// حفظ الاشتراكات
  static Future<void> _saveSubscriptions() async {
    final data = _subscriptions.map((sub) => sub.toMap()).toList();
    await StorageService.saveData(_subscriptionsKey, data);
  }

  /// تحميل الاشتراكات
  static Future<void> _loadSubscriptions() async {
    try {
      final data = await StorageService.getData(_subscriptionsKey);
      if (data != null && data is List) {
        _subscriptions = data.map((item) => Subscription.fromMap(item)).toList();
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل الاشتراكات: $e');
    }
  }

  /// حفظ سجل الدفعات
  static Future<void> _savePaymentHistory() async {
    final data = _paymentHistory.map((payment) => payment.toMap()).toList();
    await StorageService.saveData(_paymentsKey, data);
  }

  /// تحميل سجل الدفعات
  static Future<void> _loadPaymentHistory() async {
    try {
      final data = await StorageService.getData(_paymentsKey);
      if (data != null && data is List) {
        _paymentHistory = data.map((item) => PaymentRecord.fromMap(item)).toList();
      }
    } catch (e) {
      debugPrint('❌ خطأ في تحميل سجل الدفعات: $e');
    }
  }

  /// الحصول على سجل الدفعات
  static List<PaymentRecord> get paymentHistory => List.unmodifiable(_paymentHistory);

  /// الحصول على الاشتراكات
  static List<Subscription> get subscriptions => List.unmodifiable(_subscriptions);
}

/// خطة الاشتراك
class SubscriptionPlan {
  final String id;
  final String name;
  final String description;
  final double price;
  final Duration duration;
  final List<String> features;
  final int tokensLimit;
  final PlanPriority priority;

  SubscriptionPlan({
    required this.id,
    required this.name,
    required this.description,
    required this.price,
    required this.duration,
    required this.features,
    required this.tokensLimit,
    required this.priority,
  });
}

/// الاشتراك
class Subscription {
  final String id;
  final String planId;
  final String paymentId;
  final DateTime startDate;
  final DateTime endDate;
  final SubscriptionStatus status;
  final bool autoRenew;

  Subscription({
    required this.id,
    required this.planId,
    required this.paymentId,
    required this.startDate,
    required this.endDate,
    required this.status,
    required this.autoRenew,
  });

  Subscription copyWith({
    SubscriptionStatus? status,
    bool? autoRenew,
  }) {
    return Subscription(
      id: id,
      planId: planId,
      paymentId: paymentId,
      startDate: startDate,
      endDate: endDate,
      status: status ?? this.status,
      autoRenew: autoRenew ?? this.autoRenew,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'planId': planId,
      'paymentId': paymentId,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'status': status.toString(),
      'autoRenew': autoRenew,
    };
  }

  factory Subscription.fromMap(Map<String, dynamic> map) {
    return Subscription(
      id: map['id'] ?? '',
      planId: map['planId'] ?? '',
      paymentId: map['paymentId'] ?? '',
      startDate: DateTime.parse(map['startDate']),
      endDate: DateTime.parse(map['endDate']),
      status: SubscriptionStatus.values.firstWhere(
        (e) => e.toString() == map['status'],
        orElse: () => SubscriptionStatus.inactive,
      ),
      autoRenew: map['autoRenew'] ?? false,
    );
  }
}

/// سجل الدفع
class PaymentRecord {
  final String id;
  final String planId;
  final double amount;
  final double originalAmount;
  final PaymentMethod method;
  final PaymentStatus status;
  final DateTime timestamp;
  final String? promoCode;

  PaymentRecord({
    required this.id,
    required this.planId,
    required this.amount,
    required this.originalAmount,
    required this.method,
    required this.status,
    required this.timestamp,
    this.promoCode,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'planId': planId,
      'amount': amount,
      'originalAmount': originalAmount,
      'method': method.toString(),
      'status': status.toString(),
      'timestamp': timestamp.toIso8601String(),
      'promoCode': promoCode,
    };
  }

  factory PaymentRecord.fromMap(Map<String, dynamic> map) {
    return PaymentRecord(
      id: map['id'] ?? '',
      planId: map['planId'] ?? '',
      amount: map['amount']?.toDouble() ?? 0.0,
      originalAmount: map['originalAmount']?.toDouble() ?? 0.0,
      method: PaymentMethod.values.firstWhere(
        (e) => e.toString() == map['method'],
        orElse: () => PaymentMethod.card,
      ),
      status: PaymentStatus.values.firstWhere(
        (e) => e.toString() == map['status'],
        orElse: () => PaymentStatus.pending,
      ),
      timestamp: DateTime.parse(map['timestamp']),
      promoCode: map['promoCode'],
    );
  }
}

/// نتيجة الدفع
class PaymentResult {
  final bool success;
  final String? paymentId;
  final String? subscriptionId;
  final String message;

  PaymentResult({
    required this.success,
    this.paymentId,
    this.subscriptionId,
    required this.message,
  });
}

/// حالة الاشتراك
enum SubscriptionStatus {
  active,
  inactive,
  cancelled,
  expired,
}

/// طريقة الدفع
enum PaymentMethod {
  card,
  paypal,
  applePay,
  googlePay,
  bankTransfer,
}

/// حالة الدفع
enum PaymentStatus {
  pending,
  completed,
  failed,
  refunded,
}

/// أولوية الخطة
enum PlanPriority {
  normal,
  high,
  enterprise,
}

/// إضافة extension للـ List
extension ListExtension<T> on List<T> {
  T? get firstOrNull => isEmpty ? null : first;
}
