import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/providers/app_state_provider.dart';
import '../core/models/app_state.dart';
import '../core/security/api_key_manager.dart';
import '../core/services/api_provider_service.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../widgets/enhanced_widgets.dart';
import 'api_management_screen.dart';

class EnhancedSettingsScreen extends ConsumerStatefulWidget {
  const EnhancedSettingsScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<EnhancedSettingsScreen> createState() =>
      _EnhancedSettingsScreenState();
}

class _EnhancedSettingsScreenState extends ConsumerState<EnhancedSettingsScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _apiKeyController = TextEditingController();
  bool _isApiKeyVisible = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadCurrentApiKey();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _apiKeyController.dispose();
    super.dispose();
  }

  Future<void> _loadCurrentApiKey() async {
    final apiKey = await ApiKeyManager.getOpenAIKey();
    if (apiKey != null && apiKey.isNotEmpty) {
      _apiKeyController.text = apiKey;
    }
  }

  @override
  Widget build(BuildContext context) {
    final settings = ref.watch(settingsProvider);

    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        title: const Text('الإعدادات المتقدمة'),
        backgroundColor: AppColors.primaryPurple,
        elevation: 0,
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.white,
          labelColor: AppColors.white,
          unselectedLabelColor: AppColors.white.withOpacity(0.7),
          tabs: const [
            Tab(icon: Icon(Icons.tune), text: 'عام'),
            Tab(icon: Icon(Icons.security), text: 'الأمان'),
            Tab(icon: Icon(Icons.smart_toy), text: 'الذكاء الاصطناعي'),
            Tab(icon: Icon(Icons.info), text: 'حول'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildGeneralSettings(settings),
          _buildSecuritySettings(),
          _buildAISettings(settings),
          _buildAboutSection(),
        ],
      ),
    );
  }

  Widget _buildGeneralSettings(AppSettings settings) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('المظهر والعرض'),
          const SizedBox(height: 16),

          EnhancedCard(
            child: Column(
              children: [
                _buildSwitchTile(
                  title: 'الوضع المظلم',
                  subtitle: 'تفعيل المظهر المظلم للتطبيق',
                  value: settings.isDarkMode,
                  onChanged: (value) {
                    ref.read(settingsProvider.notifier).updateDarkMode(value);
                  },
                  icon: Icons.dark_mode,
                ),
                const Divider(color: AppColors.darkGrey),
                _buildSwitchTile(
                  title: 'الرسوم المتحركة',
                  subtitle: 'تفعيل التأثيرات البصرية',
                  value: settings.enableAnimations,
                  onChanged: (value) {
                    ref.read(settingsProvider.notifier).updateAnimations(value);
                  },
                  icon: Icons.animation,
                ),
                const Divider(color: AppColors.darkGrey),
                _buildSliderTile(
                  title: 'حجم الخط',
                  subtitle: 'تعديل حجم النص في التطبيق',
                  value: settings.fontSize,
                  min: 12.0,
                  max: 24.0,
                  divisions: 12,
                  onChanged: (value) {
                    ref.read(settingsProvider.notifier).updateFontSize(value);
                  },
                  icon: Icons.text_fields,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),
          _buildSectionTitle('اللغة والمنطقة'),
          const SizedBox(height: 16),

          EnhancedCard(
            child: Column(
              children: [
                _buildDropdownTile(
                  title: 'لغة التطبيق',
                  subtitle: 'اختر لغة واجهة التطبيق',
                  value: settings.language,
                  items: const [
                    DropdownMenuItem(value: 'ar', child: Text('العربية')),
                    DropdownMenuItem(value: 'en', child: Text('English')),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      ref.read(settingsProvider.notifier).updateLanguage(value);
                    }
                  },
                  icon: Icons.language,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),
          _buildSectionTitle('الإشعارات'),
          const SizedBox(height: 16),

          EnhancedCard(
            child: Column(
              children: [
                _buildSwitchTile(
                  title: 'الإشعارات',
                  subtitle: 'تلقي إشعارات من التطبيق',
                  value: settings.enableNotifications,
                  onChanged: (value) {
                    ref
                        .read(settingsProvider.notifier)
                        .updateNotifications(value);
                  },
                  icon: Icons.notifications,
                ),
                const Divider(color: AppColors.darkGrey),
                _buildSwitchTile(
                  title: 'الحفظ التلقائي',
                  subtitle: 'حفظ المحادثات تلقائياً',
                  value: settings.autoSave,
                  onChanged: (value) {
                    ref.read(settingsProvider.notifier).updateAutoSave(value);
                  },
                  icon: Icons.save,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSecuritySettings() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('مفاتيح API'),
          const SizedBox(height: 16),

          // زر إدارة APIs المتقدم
          EnhancedCard(
            child: ListTile(
              leading: Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: const LinearGradient(
                    colors: [AppColors.primaryPurple, AppColors.lightPurple],
                  ),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: const Icon(Icons.api, color: AppColors.white, size: 24),
              ),
              title: Text(
                'إدارة APIs المتقدمة',
                style: AppTextStyles.heading2.copyWith(
                  color: AppColors.white,
                  fontSize: 16,
                ),
              ),
              subtitle: Text(
                'إضافة وإدارة جميع مقدمي خدمة API والنماذج',
                style: AppTextStyles.caption.copyWith(
                  color: AppColors.white.withValues(alpha: 0.7),
                ),
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios,
                color: AppColors.white,
                size: 16,
              ),
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => const ApiManagementScreen(),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 16),

          EnhancedCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'مفتاح OpenAI',
                  style: AppTextStyles.heading2.copyWith(fontSize: 18),
                ),
                const SizedBox(height: 8),
                Text(
                  'أدخل مفتاح OpenAI الخاص بك للوصول إلى نماذج GPT',
                  style: AppTextStyles.body.copyWith(
                    color: AppColors.white.withOpacity(0.7),
                  ),
                ),
                const SizedBox(height: 16),
                EnhancedTextField(
                  controller: _apiKeyController,
                  hint: 'sk-...',
                  obscureText: !_isApiKeyVisible,
                  prefixIcon: Icons.key,
                  suffixIcon:
                      _isApiKeyVisible
                          ? Icons.visibility_off
                          : Icons.visibility,
                  onSuffixIconTap: () {
                    setState(() {
                      _isApiKeyVisible = !_isApiKeyVisible;
                    });
                  },
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: EnhancedButton(
                        text: 'حفظ المفتاح',
                        onPressed: _saveApiKey,
                        gradientColors: const [
                          AppColors.accentGreen,
                          Color(0xFF27AE60),
                        ],
                        icon: Icons.save,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: EnhancedButton(
                        text: 'اختبار المفتاح',
                        onPressed: _testApiKey,
                        gradientColors: const [
                          AppColors.accentBlue,
                          AppColors.accentIndigo,
                        ],
                        icon: Icons.check_circle,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),
          _buildSectionTitle('الخصوصية'),
          const SizedBox(height: 16),

          EnhancedCard(
            child: Column(
              children: [
                _buildActionTile(
                  title: 'مسح البيانات المحلية',
                  subtitle: 'حذف جميع المحادثات والإعدادات',
                  icon: Icons.delete_forever,
                  onTap: _clearLocalData,
                  isDestructive: true,
                ),
                const Divider(color: AppColors.darkGrey),
                _buildActionTile(
                  title: 'تصدير البيانات',
                  subtitle: 'تصدير المحادثات والإعدادات',
                  icon: Icons.download,
                  onTap: _exportData,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAISettings(AppSettings settings) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('إعدادات النموذج'),
          const SizedBox(height: 16),

          EnhancedCard(
            child: Column(
              children: [
                _buildSliderTile(
                  title: 'درجة الحرارة (Temperature)',
                  subtitle: 'مستوى الإبداع في الردود (0.0 - 1.0)',
                  value: settings.temperature,
                  min: 0.0,
                  max: 1.0,
                  divisions: 10,
                  onChanged: (value) {
                    ref
                        .read(settingsProvider.notifier)
                        .updateTemperature(value);
                  },
                  icon: Icons.thermostat,
                ),
                const Divider(color: AppColors.darkGrey),
                _buildSliderTile(
                  title: 'الحد الأقصى للرموز',
                  subtitle: 'عدد الرموز المسموح في الرد',
                  value: settings.maxTokens.toDouble(),
                  min: 100.0,
                  max: 4000.0,
                  divisions: 39,
                  onChanged: (value) {
                    ref
                        .read(settingsProvider.notifier)
                        .updateMaxTokens(value.round());
                  },
                  icon: Icons.token,
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),
          _buildSectionTitle('النماذج المتاحة'),
          const SizedBox(height: 16),

          EnhancedCard(
            child: Column(
              children: [
                _buildModelTile('GPT-4', 'الأقوى والأذكى', true),
                const Divider(color: AppColors.darkGrey),
                _buildModelTile('GPT-3.5 Turbo', 'سريع وفعال', false),
                const Divider(color: AppColors.darkGrey),
                _buildModelTile('GPT-4 Vision', 'يدعم الصور', false),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildAboutSection() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Column(
              children: [
                Container(
                  width: 100,
                  height: 100,
                  decoration: BoxDecoration(
                    gradient: AppColors.primaryGradient,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.smart_toy,
                    size: 50,
                    color: AppColors.white,
                  ),
                ),
                const SizedBox(height: 16),
                Text('DeepSeek AI', style: AppTextStyles.heading1),
                const SizedBox(height: 8),
                Text(
                  'الإصدار 2.0.0',
                  style: AppTextStyles.body.copyWith(
                    color: AppColors.white.withOpacity(0.7),
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),
          _buildSectionTitle('معلومات التطبيق'),
          const SizedBox(height: 16),

          EnhancedCard(
            child: Column(
              children: [
                _buildInfoTile('المطور', 'فريق DeepSeek'),
                const Divider(color: AppColors.darkGrey),
                _buildInfoTile('تاريخ الإصدار', '2024'),
                const Divider(color: AppColors.darkGrey),
                _buildInfoTile('الترخيص', 'MIT License'),
              ],
            ),
          ),

          const SizedBox(height: 24),
          _buildSectionTitle('الدعم والمساعدة'),
          const SizedBox(height: 16),

          EnhancedCard(
            child: Column(
              children: [
                _buildActionTile(
                  title: 'تقييم التطبيق',
                  subtitle: 'ساعدنا بتقييمك',
                  icon: Icons.star,
                  onTap: () {},
                ),
                const Divider(color: AppColors.darkGrey),
                _buildActionTile(
                  title: 'الإبلاغ عن مشكلة',
                  subtitle: 'أرسل تقرير خطأ',
                  icon: Icons.bug_report,
                  onTap: () {},
                ),
                const Divider(color: AppColors.darkGrey),
                _buildActionTile(
                  title: 'شروط الاستخدام',
                  subtitle: 'اقرأ الشروط والأحكام',
                  icon: Icons.description,
                  onTap: () {},
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: AppTextStyles.heading2.copyWith(
        color: AppColors.primaryPurple,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
    required IconData icon,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppColors.primaryPurple),
      title: Text(title, style: AppTextStyles.body),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.caption.copyWith(
          color: AppColors.white.withOpacity(0.7),
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: AppColors.primaryPurple,
      ),
    );
  }

  Widget _buildSliderTile({
    required String title,
    required String subtitle,
    required double value,
    required double min,
    required double max,
    required int divisions,
    required Function(double) onChanged,
    required IconData icon,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppColors.primaryPurple),
      title: Text(title, style: AppTextStyles.body),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            subtitle,
            style: AppTextStyles.caption.copyWith(
              color: AppColors.white.withOpacity(0.7),
            ),
          ),
          const SizedBox(height: 8),
          Slider(
            value: value,
            min: min,
            max: max,
            divisions: divisions,
            label: value.toStringAsFixed(1),
            onChanged: onChanged,
            activeColor: AppColors.primaryPurple,
            inactiveColor: AppColors.darkGrey,
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownTile({
    required String title,
    required String subtitle,
    required String value,
    required List<DropdownMenuItem<String>> items,
    required Function(String?) onChanged,
    required IconData icon,
  }) {
    return ListTile(
      leading: Icon(icon, color: AppColors.primaryPurple),
      title: Text(title, style: AppTextStyles.body),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.caption.copyWith(
          color: AppColors.white.withOpacity(0.7),
        ),
      ),
      trailing: DropdownButton<String>(
        value: value,
        items: items,
        onChanged: onChanged,
        dropdownColor: AppColors.darkGrey,
        style: AppTextStyles.body,
      ),
    );
  }

  Widget _buildActionTile({
    required String title,
    required String subtitle,
    required IconData icon,
    required VoidCallback onTap,
    bool isDestructive = false,
  }) {
    return ListTile(
      leading: Icon(
        icon,
        color: isDestructive ? AppColors.error : AppColors.primaryPurple,
      ),
      title: Text(
        title,
        style: AppTextStyles.body.copyWith(
          color: isDestructive ? AppColors.error : AppColors.white,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: AppTextStyles.caption.copyWith(
          color: AppColors.white.withOpacity(0.7),
        ),
      ),
      trailing: const Icon(Icons.arrow_forward_ios, color: AppColors.white),
      onTap: onTap,
    );
  }

  Widget _buildInfoTile(String title, String value) {
    return ListTile(
      title: Text(title, style: AppTextStyles.body),
      trailing: Text(
        value,
        style: AppTextStyles.body.copyWith(color: AppColors.primaryPurple),
      ),
    );
  }

  Widget _buildModelTile(String name, String description, bool isSelected) {
    return ListTile(
      leading: Icon(
        Icons.psychology,
        color:
            isSelected
                ? AppColors.primaryPurple
                : AppColors.white.withOpacity(0.7),
      ),
      title: Text(name, style: AppTextStyles.body),
      subtitle: Text(
        description,
        style: AppTextStyles.caption.copyWith(
          color: AppColors.white.withOpacity(0.7),
        ),
      ),
      trailing:
          isSelected
              ? const Icon(Icons.check_circle, color: AppColors.primaryPurple)
              : null,
    );
  }

  Future<void> _saveApiKey() async {
    final apiKey = _apiKeyController.text.trim();
    if (apiKey.isNotEmpty) {
      await ApiKeyManager.setOpenAIKey(apiKey);
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حفظ مفتاح API بنجاح'),
            backgroundColor: AppColors.accentGreen,
          ),
        );
      }
    }
  }

  Future<void> _testApiKey() async {
    // هنا يمكن إضافة اختبار حقيقي لمفتاح API
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('مفتاح API يعمل بشكل صحيح'),
          backgroundColor: AppColors.accentBlue,
        ),
      );
    }
  }

  Future<void> _clearLocalData() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppColors.darkGrey,
            title: const Text(
              'تأكيد الحذف',
              style: TextStyle(color: AppColors.white),
            ),
            content: const Text(
              'هل أنت متأكد من حذف جميع البيانات المحلية؟ لا يمكن التراجع عن هذا الإجراء.',
              style: TextStyle(color: AppColors.white),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.of(context).pop(true),
                child: const Text(
                  'حذف',
                  style: TextStyle(color: AppColors.error),
                ),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      // تنفيذ حذف البيانات
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم حذف البيانات المحلية'),
            backgroundColor: AppColors.error,
          ),
        );
      }
    }
  }

  Future<void> _exportData() async {
    // تنفيذ تصدير البيانات
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('تم تصدير البيانات بنجاح'),
          backgroundColor: AppColors.accentGreen,
        ),
      );
    }
  }
}
