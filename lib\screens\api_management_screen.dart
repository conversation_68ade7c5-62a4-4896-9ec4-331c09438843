import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../core/models/api_provider_model.dart';
import '../core/services/api_provider_service.dart';
import '../utils/app_colors.dart';
import '../utils/app_text_styles.dart';
import '../widgets/enhanced_widgets.dart';

class ApiManagementScreen extends ConsumerStatefulWidget {
  const ApiManagementScreen({Key? key}) : super(key: key);

  @override
  ConsumerState<ApiManagementScreen> createState() =>
      _ApiManagementScreenState();
}

class _ApiManagementScreenState extends ConsumerState<ApiManagementScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<ApiProvider> _providers = [];
  List<AvailableModel> _models = [];
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadData() async {
    setState(() => _isLoading = true);

    _providers = ApiProviderService.getAllProviders();
    _models = ApiProviderService.getAllModels();

    setState(() => _isLoading = false);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.darkGrey,
        title: Text(
          'إدارة APIs',
          style: AppTextStyles.heading2.copyWith(color: AppColors.white),
        ),
        bottom: TabBar(
          controller: _tabController,
          indicatorColor: AppColors.primaryPurple,
          labelColor: AppColors.white,
          unselectedLabelColor: AppColors.white.withValues(alpha: 0.6),
          tabs: const [
            Tab(text: 'مقدمو الخدمة', icon: Icon(Icons.api)),
            Tab(text: 'النماذج', icon: Icon(Icons.model_training)),
            Tab(text: 'إضافة جديد', icon: Icon(Icons.add)),
          ],
        ),
        actions: [
          IconButton(onPressed: _refreshAll, icon: const Icon(Icons.refresh)),
        ],
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildProvidersTab(),
          _buildModelsTab(),
          _buildAddProviderTab(),
        ],
      ),
    );
  }

  Widget _buildProvidersTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    return RefreshIndicator(
      onRefresh: _loadData,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _providers.length,
        itemBuilder: (context, index) {
          final provider = _providers[index];
          return _buildProviderCard(provider);
        },
      ),
    );
  }

  Widget _buildProviderCard(ApiProvider provider) {
    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors:
                        provider.isWorking
                            ? [
                              AppColors.accentGreen,
                              AppColors.accentGreen.withValues(alpha: 0.7),
                            ]
                            : [
                              AppColors.accentRed,
                              AppColors.accentRed.withValues(alpha: 0.7),
                            ],
                  ),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Icon(
                  _getProviderIcon(provider.type),
                  color: AppColors.white,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      provider.displayName,
                      style: AppTextStyles.heading2.copyWith(
                        fontSize: 16,
                        color: AppColors.white,
                      ),
                    ),
                    Text(
                      provider.description ?? provider.baseUrl,
                      style: AppTextStyles.caption.copyWith(
                        color: AppColors.white.withValues(alpha: 0.7),
                      ),
                    ),
                  ],
                ),
              ),
              _buildStatusIndicator(provider),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildInfoChip(
                  'النماذج',
                  '${provider.supportedModels.length}',
                  Icons.model_training,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildInfoChip(
                  'الحالة',
                  provider.isActive ? 'نشط' : 'غير نشط',
                  provider.isActive ? Icons.check_circle : Icons.cancel,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildInfoChip(
                  'آخر اختبار',
                  provider.lastTested != null
                      ? _formatDate(provider.lastTested!)
                      : 'لم يتم',
                  Icons.schedule,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _editProvider(provider),
                  icon: const Icon(Icons.edit),
                  label: const Text('تعديل'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryPurple,
                    foregroundColor: AppColors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: () => _testProvider(provider),
                  icon: const Icon(Icons.wifi_tethering),
                  label: const Text('اختبار'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.accentBlue,
                    foregroundColor: AppColors.white,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: () => _deleteProvider(provider),
                icon: const Icon(Icons.delete),
                color: AppColors.accentRed,
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildStatusIndicator(ApiProvider provider) {
    Color color;
    IconData icon;
    String text;

    if (!provider.isActive) {
      color = AppColors.midGrey;
      icon = Icons.power_off;
      text = 'غير نشط';
    } else if (provider.isWorking) {
      color = AppColors.accentGreen;
      icon = Icons.check_circle;
      text = 'يعمل';
    } else {
      color = AppColors.accentRed;
      icon = Icons.error;
      text = 'خطأ';
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.2),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, color: color, size: 16),
          const SizedBox(width: 4),
          Text(text, style: AppTextStyles.caption.copyWith(color: color)),
        ],
      ),
    );
  }

  Widget _buildInfoChip(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: AppColors.midGrey.withValues(alpha: 0.3),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          Icon(icon, color: AppColors.white.withValues(alpha: 0.7), size: 16),
          const SizedBox(height: 4),
          Text(
            value,
            style: AppTextStyles.caption.copyWith(
              color: AppColors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: AppTextStyles.caption.copyWith(
              color: AppColors.white.withValues(alpha: 0.7),
              fontSize: 10,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModelsTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    final activeModels = ApiProviderService.getActiveModels();

    return RefreshIndicator(
      onRefresh: _refreshModels,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: activeModels.length,
        itemBuilder: (context, index) {
          final model = activeModels[index];
          final provider = ApiProviderService.getProvider(model.providerId);
          return _buildModelCard(model, provider);
        },
      ),
    );
  }

  Widget _buildModelCard(AvailableModel model, ApiProvider? provider) {
    return EnhancedCard(
      margin: const EdgeInsets.only(bottom: 12),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [AppColors.primaryPurple, AppColors.lightPurple],
            ),
            borderRadius: BorderRadius.circular(20),
          ),
          child: Icon(
            _getProviderIcon(provider?.type ?? ApiProviderType.custom),
            color: AppColors.white,
            size: 20,
          ),
        ),
        title: Text(
          model.displayName,
          style: AppTextStyles.body.copyWith(color: AppColors.white),
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              provider?.displayName ?? 'مقدم غير معروف',
              style: AppTextStyles.caption.copyWith(
                color: AppColors.white.withValues(alpha: 0.7),
              ),
            ),
            if (model.maxTokens != null)
              Text(
                'الحد الأقصى: ${model.maxTokens} رمز',
                style: AppTextStyles.caption.copyWith(
                  color: AppColors.accentBlue,
                ),
              ),
          ],
        ),
        trailing:
            model.isActive
                ? Icon(Icons.check_circle, color: AppColors.accentGreen)
                : Icon(Icons.cancel, color: AppColors.accentRed),
      ),
    );
  }

  Widget _buildAddProviderTab() {
    return _AddProviderForm(
      onProviderAdded: () {
        _loadData();
        _tabController.animateTo(0);
      },
    );
  }

  IconData _getProviderIcon(ApiProviderType type) {
    switch (type) {
      case ApiProviderType.openai:
        return Icons.psychology;
      case ApiProviderType.openrouter:
        return Icons.router;
      case ApiProviderType.gemini:
        return Icons.auto_awesome;
      case ApiProviderType.anthropic:
        return Icons.smart_toy;
      case ApiProviderType.huggingface:
        return Icons.face;
      case ApiProviderType.custom:
        return Icons.api;
    }
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return '${difference.inMinutes}د';
    } else if (difference.inDays < 1) {
      return '${difference.inHours}س';
    } else {
      return '${difference.inDays}ي';
    }
  }

  Future<void> _refreshAll() async {
    setState(() => _isLoading = true);

    await ApiProviderService.testAllProviders();
    await ApiProviderService.refreshAllModels();
    await _loadData();
  }

  Future<void> _refreshModels() async {
    setState(() => _isLoading = true);
    await ApiProviderService.refreshAllModels();
    await _loadData();
  }

  Future<void> _testProvider(ApiProvider provider) async {
    final isWorking = await ApiProviderService.testProvider(provider.id);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            isWorking ? 'المقدم يعمل بشكل صحيح' : 'فشل في الاتصال بالمقدم',
          ),
          backgroundColor:
              isWorking ? AppColors.accentGreen : AppColors.accentRed,
        ),
      );
      _loadData();
    }
  }

  Future<void> _editProvider(ApiProvider provider) async {
    // TODO: إضافة شاشة تعديل المقدم
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(const SnackBar(content: Text('ميزة التعديل ستتوفر قريباً')));
  }

  Future<void> _deleteProvider(ApiProvider provider) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder:
          (context) => AlertDialog(
            backgroundColor: AppColors.darkGrey,
            title: Text(
              'حذف المقدم',
              style: AppTextStyles.heading2.copyWith(
                color: AppColors.white,
                fontSize: 16,
              ),
            ),
            content: Text(
              'هل أنت متأكد من حذف ${provider.displayName}؟',
              style: AppTextStyles.body.copyWith(color: AppColors.white),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context, false),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () => Navigator.pop(context, true),
                child: Text(
                  'حذف',
                  style: TextStyle(color: AppColors.accentRed),
                ),
              ),
            ],
          ),
    );

    if (confirmed == true) {
      await ApiProviderService.removeProvider(provider.id);
      _loadData();
    }
  }
}

class _AddProviderForm extends StatefulWidget {
  final VoidCallback onProviderAdded;

  const _AddProviderForm({required this.onProviderAdded});

  @override
  State<_AddProviderForm> createState() => _AddProviderFormState();
}

class _AddProviderFormState extends State<_AddProviderForm> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _displayNameController = TextEditingController();
  final _baseUrlController = TextEditingController();
  final _apiKeyController = TextEditingController();
  final _descriptionController = TextEditingController();

  ApiProviderType _selectedType = ApiProviderType.custom;
  bool _isLoading = false;

  @override
  void dispose() {
    _nameController.dispose();
    _displayNameController.dispose();
    _baseUrlController.dispose();
    _apiKeyController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'إضافة مقدم خدمة جديد',
              style: AppTextStyles.heading2.copyWith(color: AppColors.white),
            ),
            const SizedBox(height: 24),

            _buildDropdownField(),
            const SizedBox(height: 16),

            _buildTextField(
              controller: _nameController,
              label: 'اسم المقدم (بالإنجليزية)',
              hint: 'openai, custom-api, etc.',
              validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
            ),
            const SizedBox(height: 16),

            _buildTextField(
              controller: _displayNameController,
              label: 'الاسم المعروض',
              hint: 'OpenAI, Custom API, etc.',
              validator: (value) => value?.isEmpty == true ? 'مطلوب' : null,
            ),
            const SizedBox(height: 16),

            _buildTextField(
              controller: _baseUrlController,
              label: 'رابط API الأساسي',
              hint: 'https://api.example.com/v1',
              validator: (value) {
                if (value?.isEmpty == true) return 'مطلوب';
                final uri = Uri.tryParse(value!);
                if (uri == null || !uri.hasAbsolutePath) {
                  return 'رابط غير صحيح';
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            _buildTextField(
              controller: _apiKeyController,
              label: 'مفتاح API',
              hint: 'sk-...',
              obscureText: true,
            ),
            const SizedBox(height: 16),

            _buildTextField(
              controller: _descriptionController,
              label: 'الوصف (اختياري)',
              hint: 'وصف مختصر للمقدم',
              maxLines: 3,
            ),
            const SizedBox(height: 24),

            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _isLoading ? null : _addProvider,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryPurple,
                  foregroundColor: AppColors.white,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                ),
                child:
                    _isLoading
                        ? const CircularProgressIndicator(
                          color: AppColors.white,
                        )
                        : const Text('إضافة المقدم'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDropdownField() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'نوع المقدم',
          style: AppTextStyles.body.copyWith(color: AppColors.white),
        ),
        const SizedBox(height: 8),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: AppColors.midGrey,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppColors.lightGrey),
          ),
          child: DropdownButtonHideUnderline(
            child: DropdownButton<ApiProviderType>(
              value: _selectedType,
              isExpanded: true,
              dropdownColor: AppColors.darkGrey,
              style: AppTextStyles.body.copyWith(color: AppColors.white),
              onChanged: (value) {
                setState(() {
                  _selectedType = value!;
                  _updateFieldsForType(value);
                });
              },
              items:
                  ApiProviderType.values.map((type) {
                    return DropdownMenuItem(
                      value: type,
                      child: Text(_getTypeDisplayName(type)),
                    );
                  }).toList(),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    String? Function(String?)? validator,
    bool obscureText = false,
    int maxLines = 1,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label, style: AppTextStyles.body.copyWith(color: AppColors.white)),
        const SizedBox(height: 8),
        TextFormField(
          controller: controller,
          validator: validator,
          obscureText: obscureText,
          maxLines: maxLines,
          style: AppTextStyles.body.copyWith(color: AppColors.white),
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: AppTextStyles.body.copyWith(
              color: AppColors.white.withValues(alpha: 0.5),
            ),
            filled: true,
            fillColor: AppColors.midGrey,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.lightGrey),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.lightGrey),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(12),
              borderSide: BorderSide(color: AppColors.primaryPurple),
            ),
          ),
        ),
      ],
    );
  }

  String _getTypeDisplayName(ApiProviderType type) {
    switch (type) {
      case ApiProviderType.openai:
        return 'OpenAI';
      case ApiProviderType.openrouter:
        return 'OpenRouter';
      case ApiProviderType.gemini:
        return 'Google Gemini';
      case ApiProviderType.anthropic:
        return 'Anthropic Claude';
      case ApiProviderType.huggingface:
        return 'Hugging Face';
      case ApiProviderType.custom:
        return 'مخصص';
    }
  }

  void _updateFieldsForType(ApiProviderType type) {
    switch (type) {
      case ApiProviderType.openai:
        _nameController.text = 'openai';
        _displayNameController.text = 'OpenAI';
        _baseUrlController.text = 'https://api.openai.com/v1';
        _descriptionController.text = 'نماذج GPT الرسمية من OpenAI';
        break;
      case ApiProviderType.openrouter:
        _nameController.text = 'openrouter';
        _displayNameController.text = 'OpenRouter';
        _baseUrlController.text = 'https://openrouter.ai/api/v1';
        _descriptionController.text = 'وصول لنماذج متعددة عبر OpenRouter';
        break;
      case ApiProviderType.gemini:
        _nameController.text = 'gemini';
        _displayNameController.text = 'Google Gemini';
        _baseUrlController.text =
            'https://generativelanguage.googleapis.com/v1beta';
        _descriptionController.text = 'نماذج Gemini من Google';
        break;
      case ApiProviderType.anthropic:
        _nameController.text = 'anthropic';
        _displayNameController.text = 'Anthropic Claude';
        _baseUrlController.text = 'https://api.anthropic.com/v1';
        _descriptionController.text = 'نماذج Claude من Anthropic';
        break;
      case ApiProviderType.huggingface:
        _nameController.text = 'huggingface';
        _displayNameController.text = 'Hugging Face';
        _baseUrlController.text = 'https://api-inference.huggingface.co';
        _descriptionController.text = 'نماذج مجانية من Hugging Face';
        break;
      case ApiProviderType.custom:
        _nameController.clear();
        _displayNameController.clear();
        _baseUrlController.clear();
        _descriptionController.clear();
        break;
    }
  }

  Future<void> _addProvider() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final provider = ApiProvider.create(
        name: _nameController.text.trim(),
        displayName: _displayNameController.text.trim(),
        baseUrl: _baseUrlController.text.trim(),
        apiKey: _apiKeyController.text.trim(),
        type: _selectedType,
        description: _descriptionController.text.trim(),
      );

      await ApiProviderService.addProvider(provider);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('تم إضافة المقدم بنجاح'),
            backgroundColor: AppColors.accentGreen,
          ),
        );
        widget.onProviderAdded();
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('خطأ في إضافة المقدم: $e'),
            backgroundColor: AppColors.accentRed,
          ),
        );
      }
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
