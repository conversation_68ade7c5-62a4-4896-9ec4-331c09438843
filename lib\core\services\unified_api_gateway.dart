import 'dart:convert';
import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import '../models/api_provider_model.dart';
import 'api_provider_service.dart';
import '../security/api_key_manager.dart';

/// بوابة API موحدة - نقطة دخول واحدة لجميع مقدمي الخدمة
class UnifiedApiGateway {
  static final Dio _dio = Dio();
  static String? _activeProviderId;
  static String? _activeModelId;
  
  /// تهيئة البوابة
  static Future<void> initialize() async {
    await ApiProviderService.initialize();
    await _loadActiveProvider();
    await _setupDio();
  }

  /// إعداد Dio
  static Future<void> _setupDio() async {
    _dio.options = BaseOptions(
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 60),
      headers: {
        'Content-Type': 'application/json',
        'User-Agent': 'DeepSeek-AI-App/1.0',
      },
    );

    // إضافة interceptor للتسجيل
    if (kDebugMode) {
      _dio.interceptors.add(LogInterceptor(
        requestBody: true,
        responseBody: true,
        logPrint: (obj) => debugPrint(obj.toString()),
      ));
    }
  }

  /// تحميل المقدم النشط
  static Future<void> _loadActiveProvider() async {
    final activeProviders = ApiProviderService.getActiveProviders();
    if (activeProviders.isNotEmpty) {
      _activeProviderId = activeProviders.first.id;
      final models = ApiProviderService.getModelsForProvider(_activeProviderId!);
      if (models.isNotEmpty) {
        _activeModelId = models.first.id;
      }
    }
  }

  /// تعيين المقدم النشط
  static Future<void> setActiveProvider(String providerId, {String? modelId}) async {
    final provider = ApiProviderService.getProvider(providerId);
    if (provider != null && provider.isActive && provider.isWorking) {
      _activeProviderId = providerId;
      
      if (modelId != null) {
        _activeModelId = modelId;
      } else {
        // اختيار أول نموذج متاح
        final models = ApiProviderService.getModelsForProvider(providerId);
        if (models.isNotEmpty) {
          _activeModelId = models.first.id;
        }
      }
      
      await _saveActiveProvider();
      debugPrint('✅ تم تعيين المقدم النشط: ${provider.displayName} - النموذج: $_activeModelId');
    }
  }

  /// حفظ المقدم النشط
  static Future<void> _saveActiveProvider() async {
    // يمكن حفظه في SharedPreferences أو قاعدة البيانات
  }

  /// الحصول على المقدم النشط
  static ApiProvider? getActiveProvider() {
    if (_activeProviderId != null) {
      return ApiProviderService.getProvider(_activeProviderId!);
    }
    return null;
  }

  /// الحصول على النموذج النشط
  static AvailableModel? getActiveModel() {
    if (_activeModelId != null) {
      final models = ApiProviderService.getAllModels();
      try {
        return models.firstWhere((m) => m.id == _activeModelId);
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  /// إرسال طلب دردشة موحد
  static Future<Map<String, dynamic>> sendChatRequest({
    required String message,
    List<Map<String, dynamic>>? messages,
    String? customModel,
    double temperature = 0.7,
    int maxTokens = 1000,
    Map<String, dynamic>? additionalParams,
  }) async {
    final provider = getActiveProvider();
    if (provider == null) {
      throw Exception('لا يوجد مقدم خدمة نشط');
    }

    final model = customModel ?? _activeModelId;
    if (model == null) {
      throw Exception('لا يوجد نموذج محدد');
    }

    // بناء الرسائل
    final chatMessages = messages ?? [
      {'role': 'user', 'content': message}
    ];

    // بناء البيانات حسب نوع المقدم
    final requestData = await _buildRequestData(
      provider: provider,
      model: model,
      messages: chatMessages,
      temperature: temperature,
      maxTokens: maxTokens,
      additionalParams: additionalParams,
    );

    // إرسال الطلب
    final response = await _sendRequest(
      provider: provider,
      endpoint: _getChatEndpoint(provider),
      data: requestData,
    );

    return _parseResponse(provider, response);
  }

  /// إرسال طلب إنشاء صورة موحد
  static Future<String> sendImageGenerationRequest({
    required String prompt,
    String size = '1024x1024',
    String quality = 'standard',
    String style = 'vivid',
    Map<String, dynamic>? additionalParams,
  }) async {
    // للصور، نستخدم OpenAI مباشرة لأن معظم المقدمين لا يدعمون إنشاء الصور
    final openaiKey = await ApiKeyManager.getOpenAIKey();
    if (openaiKey == null || openaiKey.isEmpty) {
      throw Exception('مفتاح OpenAI مطلوب لإنشاء الصور');
    }

    final requestData = {
      'model': 'dall-e-3',
      'prompt': prompt,
      'n': 1,
      'size': size,
      'quality': quality,
      'style': style,
      ...?additionalParams,
    };

    final response = await _dio.post(
      'https://api.openai.com/v1/images/generations',
      options: Options(headers: {
        'Authorization': 'Bearer $openaiKey',
        'Content-Type': 'application/json',
      }),
      data: requestData,
    );

    if (response.statusCode == 200) {
      return response.data['data'][0]['url'];
    } else {
      throw Exception('فشل في إنشاء الصورة: ${response.statusCode}');
    }
  }

  /// بناء بيانات الطلب حسب نوع المقدم
  static Future<Map<String, dynamic>> _buildRequestData({
    required ApiProvider provider,
    required String model,
    required List<Map<String, dynamic>> messages,
    required double temperature,
    required int maxTokens,
    Map<String, dynamic>? additionalParams,
  }) async {
    switch (provider.type) {
      case ApiProviderType.openai:
      case ApiProviderType.openrouter:
        return {
          'model': model,
          'messages': messages,
          'temperature': temperature,
          'max_tokens': maxTokens,
          ...?additionalParams,
        };

      case ApiProviderType.gemini:
        // تحويل تنسيق الرسائل لـ Gemini
        final geminiMessages = messages.map((msg) => {
          'role': msg['role'] == 'user' ? 'user' : 'model',
          'parts': [{'text': msg['content']}],
        }).toList();

        return {
          'contents': geminiMessages,
          'generationConfig': {
            'temperature': temperature,
            'maxOutputTokens': maxTokens,
            ...?additionalParams,
          },
        };

      case ApiProviderType.anthropic:
        return {
          'model': model,
          'messages': messages,
          'temperature': temperature,
          'max_tokens': maxTokens,
          ...?additionalParams,
        };

      default:
        return {
          'model': model,
          'messages': messages,
          'temperature': temperature,
          'max_tokens': maxTokens,
          ...?additionalParams,
        };
    }
  }

  /// الحصول على endpoint الدردشة
  static String _getChatEndpoint(ApiProvider provider) {
    switch (provider.type) {
      case ApiProviderType.gemini:
        return '/models/${_activeModelId}:generateContent';
      case ApiProviderType.anthropic:
        return '/messages';
      default:
        return '/chat/completions';
    }
  }

  /// إرسال الطلب
  static Future<Response> _sendRequest({
    required ApiProvider provider,
    required String endpoint,
    required Map<String, dynamic> data,
  }) async {
    final headers = <String, String>{
      ...Map<String, String>.from(provider.headers),
    };

    // إضافة مفتاح API حسب نوع المقدم
    switch (provider.type) {
      case ApiProviderType.gemini:
        headers.remove('Authorization');
        // سيتم إضافة المفتاح في URL
        break;
      case ApiProviderType.anthropic:
        headers['x-api-key'] = provider.apiKey!;
        headers['anthropic-version'] = '2023-06-01';
        break;
      default:
        headers['Authorization'] = 'Bearer ${provider.apiKey}';
        break;
    }

    String url = '${provider.baseUrl}$endpoint';
    
    // إضافة مفتاح API في URL للـ Gemini
    if (provider.type == ApiProviderType.gemini) {
      url += '?key=${provider.apiKey}';
    }

    return await _dio.post(
      url,
      options: Options(headers: headers),
      data: data,
    );
  }

  /// تحليل الاستجابة
  static Map<String, dynamic> _parseResponse(ApiProvider provider, Response response) {
    if (response.statusCode != 200) {
      throw Exception('خطأ في الطلب: ${response.statusCode}');
    }

    final data = response.data;
    
    switch (provider.type) {
      case ApiProviderType.gemini:
        if (data['candidates'] != null && data['candidates'].isNotEmpty) {
          final content = data['candidates'][0]['content']['parts'][0]['text'];
          return {
            'content': content,
            'model': _activeModelId,
            'provider': provider.displayName,
            'usage': data['usageMetadata'] ?? {},
          };
        }
        break;

      case ApiProviderType.anthropic:
        if (data['content'] != null && data['content'].isNotEmpty) {
          final content = data['content'][0]['text'];
          return {
            'content': content,
            'model': _activeModelId,
            'provider': provider.displayName,
            'usage': data['usage'] ?? {},
          };
        }
        break;

      default:
        if (data['choices'] != null && data['choices'].isNotEmpty) {
          final content = data['choices'][0]['message']['content'];
          return {
            'content': content,
            'model': _activeModelId,
            'provider': provider.displayName,
            'usage': data['usage'] ?? {},
          };
        }
        break;
    }

    throw Exception('استجابة غير متوقعة من المقدم');
  }

  /// الحصول على جميع المقدمين المتاحين
  static List<ApiProvider> getAvailableProviders() {
    return ApiProviderService.getActiveProviders();
  }

  /// الحصول على النماذج المتاحة للمقدم النشط
  static List<AvailableModel> getAvailableModels([String? providerId]) {
    final targetProviderId = providerId ?? _activeProviderId;
    if (targetProviderId != null) {
      return ApiProviderService.getModelsForProvider(targetProviderId);
    }
    return [];
  }

  /// تحديث النماذج لجميع المقدمين النشطين
  static Future<void> refreshAllModels() async {
    final providers = ApiProviderService.getActiveProviders();
    for (final provider in providers) {
      try {
        await ApiProviderService.fetchModelsForProvider(provider.id);
        debugPrint('✅ تم تحديث النماذج للمقدم: ${provider.displayName}');
      } catch (e) {
        debugPrint('❌ فشل تحديث النماذج للمقدم ${provider.displayName}: $e');
      }
    }
  }

  /// اختبار الاتصال مع المقدم النشط
  static Future<bool> testActiveProvider() async {
    final provider = getActiveProvider();
    if (provider != null) {
      return await ApiProviderService.testProvider(provider.id);
    }
    return false;
  }

  /// الحصول على إحصائيات الاستخدام
  static Map<String, dynamic> getUsageStats() {
    final provider = getActiveProvider();
    final model = getActiveModel();
    
    return {
      'active_provider': provider?.displayName ?? 'غير محدد',
      'active_model': model?.displayName ?? 'غير محدد',
      'total_providers': ApiProviderService.getAllProviders().length,
      'active_providers': ApiProviderService.getActiveProviders().length,
      'total_models': ApiProviderService.getAllModels().length,
      'available_models': getAvailableModels().length,
    };
  }
}
