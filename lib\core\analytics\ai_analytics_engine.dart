import 'package:flutter/foundation.dart';
import '../storage/storage_service.dart';

/// محرك التحليلات الذكية
class AIAnalyticsEngine {
  static const String _analyticsDataKey = 'ai_analytics_data';
  static const String _insightsKey = 'ai_insights';
  static const String _predictionsKey = 'ai_predictions';
  
  static bool _isInitialized = false;
  static Map<String, AnalyticsData> _analyticsData = {};
  static List<AIInsight> _insights = [];
  static List<AIPrediction> _predictions = [];

  /// تهيئة محرك التحليلات
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      await _loadAnalyticsData();
      await _loadInsights();
      await _loadPredictions();
      
      _isInitialized = true;
      debugPrint('🧠 تم تهيئة محرك التحليلات الذكية');
      
      // بدء التحليل التلقائي
      _startAutoAnalysis();
    } catch (e) {
      debugPrint('❌ خطأ في تهيئة محرك التحليلات: $e');
    }
  }

  /// تسجيل حدث للتحليل
  static Future<void> trackEvent({
    required String eventName,
    required String category,
    Map<String, dynamic>? properties,
    String? userId,
  }) async {
    final event = AnalyticsEvent(
      id: 'event_${DateTime.now().millisecondsSinceEpoch}',
      name: eventName,
      category: category,
      properties: properties ?? {},
      userId: userId,
      timestamp: DateTime.now(),
    );

    // إضافة الحدث للبيانات
    final key = '${category}_${eventName}';
    if (_analyticsData[key] == null) {
      _analyticsData[key] = AnalyticsData(
        id: key,
        category: category,
        name: eventName,
        events: [],
        metrics: AnalyticsMetrics(),
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    }

    _analyticsData[key]!.events.add(event);
    _analyticsData[key]!.updatedAt = DateTime.now();
    
    // تحديث المقاييس
    await _updateMetrics(key);
    
    await _saveAnalyticsData();
    
    debugPrint('📊 تم تسجيل حدث: $eventName');
  }

  /// تحليل البيانات وإنتاج الرؤى
  static Future<List<AIInsight>> analyzeData() async {
    try {
      final newInsights = <AIInsight>[];
      
      // تحليل أنماط الاستخدام
      final usageInsights = await _analyzeUsagePatterns();
      newInsights.addAll(usageInsights);
      
      // تحليل الأداء
      final performanceInsights = await _analyzePerformance();
      newInsights.addAll(performanceInsights);
      
      // تحليل سلوك المستخدم
      final behaviorInsights = await _analyzeBehavior();
      newInsights.addAll(behaviorInsights);
      
      // إضافة الرؤى الجديدة
      _insights.addAll(newInsights);
      
      // الاحتفاظ بآخر 100 رؤية فقط
      if (_insights.length > 100) {
        _insights.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        _insights = _insights.take(100).toList();
      }
      
      await _saveInsights();
      
      debugPrint('🔍 تم إنتاج ${newInsights.length} رؤية جديدة');
      return newInsights;
    } catch (e) {
      debugPrint('❌ فشل في تحليل البيانات: $e');
      return [];
    }
  }

  /// إنتاج توقعات ذكية
  static Future<List<AIPrediction>> generatePredictions() async {
    try {
      final newPredictions = <AIPrediction>[];
      
      // توقع الاستخدام المستقبلي
      final usagePrediction = await _predictUsage();
      if (usagePrediction != null) newPredictions.add(usagePrediction);
      
      // توقع الأداء
      final performancePrediction = await _predictPerformance();
      if (performancePrediction != null) newPredictions.add(performancePrediction);
      
      // توقع الاتجاهات
      final trendPrediction = await _predictTrends();
      if (trendPrediction != null) newPredictions.add(trendPrediction);
      
      _predictions.addAll(newPredictions);
      
      // الاحتفاظ بآخر 50 توقع
      if (_predictions.length > 50) {
        _predictions.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        _predictions = _predictions.take(50).toList();
      }
      
      await _savePredictions();
      
      debugPrint('🔮 تم إنتاج ${newPredictions.length} توقع جديد');
      return newPredictions;
    } catch (e) {
      debugPrint('❌ فشل في إنتاج التوقعات: $e');
      return [];
    }
  }

  /// الحصول على إحصائيات شاملة
  static AnalyticsSummary getAnalyticsSummary() {
    final totalEvents = _analyticsData.values
        .map((data) => data.events.length)
        .fold(0, (sum, count) => sum + count);
    
    final categories = _analyticsData.values
        .map((data) => data.category)
        .toSet()
        .toList();
    
    final topEvents = _analyticsData.values
        .toList()
        ..sort((a, b) => b.events.length.compareTo(a.events.length));
    
    return AnalyticsSummary(
      totalEvents: totalEvents,
      totalCategories: categories.length,
      totalInsights: _insights.length,
      totalPredictions: _predictions.length,
      topCategories: categories.take(5).toList(),
      topEvents: topEvents.take(10).map((data) => data.name).toList(),
      lastAnalysis: _insights.isNotEmpty 
          ? _insights.first.createdAt 
          : null,
    );
  }

  // Private methods

  /// تحليل أنماط الاستخدام
  static Future<List<AIInsight>> _analyzeUsagePatterns() async {
    final insights = <AIInsight>[];
    
    // تحليل الأوقات الأكثر نشاطاً
    final hourlyUsage = <int, int>{};
    for (final data in _analyticsData.values) {
      for (final event in data.events) {
        final hour = event.timestamp.hour;
        hourlyUsage[hour] = (hourlyUsage[hour] ?? 0) + 1;
      }
    }
    
    if (hourlyUsage.isNotEmpty) {
      final peakHour = hourlyUsage.entries
          .reduce((a, b) => a.value > b.value ? a : b)
          .key;
      
      insights.add(AIInsight(
        id: 'usage_peak_${DateTime.now().millisecondsSinceEpoch}',
        type: InsightType.usage,
        title: 'ساعة الذروة',
        description: 'أعلى نشاط للمستخدمين في الساعة $peakHour:00',
        confidence: 0.85,
        impact: InsightImpact.medium,
        data: {'peakHour': peakHour, 'usage': hourlyUsage[peakHour]},
        createdAt: DateTime.now(),
      ));
    }
    
    return insights;
  }

  /// تحليل الأداء
  static Future<List<AIInsight>> _analyzePerformance() async {
    final insights = <AIInsight>[];
    
    // تحليل معدل الاستخدام
    final now = DateTime.now();
    final lastWeek = now.subtract(const Duration(days: 7));
    
    int recentEvents = 0;
    for (final data in _analyticsData.values) {
      recentEvents += data.events
          .where((event) => event.timestamp.isAfter(lastWeek))
          .length;
    }
    
    if (recentEvents > 0) {
      final dailyAverage = recentEvents / 7;
      
      insights.add(AIInsight(
        id: 'performance_${DateTime.now().millisecondsSinceEpoch}',
        type: InsightType.performance,
        title: 'معدل الاستخدام الأسبوعي',
        description: 'متوسط ${dailyAverage.toStringAsFixed(1)} حدث يومياً',
        confidence: 0.9,
        impact: InsightImpact.high,
        data: {'dailyAverage': dailyAverage, 'totalEvents': recentEvents},
        createdAt: DateTime.now(),
      ));
    }
    
    return insights;
  }

  /// تحليل سلوك المستخدم
  static Future<List<AIInsight>> _analyzeBehavior() async {
    final insights = <AIInsight>[];
    
    // تحليل الميزات الأكثر استخداماً
    final featureUsage = <String, int>{};
    for (final data in _analyticsData.values) {
      featureUsage[data.name] = data.events.length;
    }
    
    if (featureUsage.isNotEmpty) {
      final topFeature = featureUsage.entries
          .reduce((a, b) => a.value > b.value ? a : b);
      
      insights.add(AIInsight(
        id: 'behavior_${DateTime.now().millisecondsSinceEpoch}',
        type: InsightType.behavior,
        title: 'الميزة الأكثر استخداماً',
        description: '${topFeature.key} تم استخدامها ${topFeature.value} مرة',
        confidence: 0.95,
        impact: InsightImpact.high,
        data: {'feature': topFeature.key, 'usage': topFeature.value},
        createdAt: DateTime.now(),
      ));
    }
    
    return insights;
  }

  /// توقع الاستخدام المستقبلي
  static Future<AIPrediction?> _predictUsage() async {
    if (_analyticsData.isEmpty) return null;
    
    // حساب الاتجاه العام
    final now = DateTime.now();
    final lastMonth = now.subtract(const Duration(days: 30));
    
    int recentEvents = 0;
    for (final data in _analyticsData.values) {
      recentEvents += data.events
          .where((event) => event.timestamp.isAfter(lastMonth))
          .length;
    }
    
    final predictedGrowth = recentEvents * 1.2; // توقع نمو 20%
    
    return AIPrediction(
      id: 'usage_prediction_${DateTime.now().millisecondsSinceEpoch}',
      type: PredictionType.usage,
      title: 'توقع الاستخدام الشهري',
      description: 'متوقع زيادة الاستخدام إلى ${predictedGrowth.toInt()} حدث',
      confidence: 0.75,
      timeframe: PredictionTimeframe.month,
      data: {
        'current': recentEvents,
        'predicted': predictedGrowth,
        'growth': 20,
      },
      createdAt: DateTime.now(),
    );
  }

  /// توقع الأداء
  static Future<AIPrediction?> _predictPerformance() async {
    // محاكاة توقع الأداء
    return AIPrediction(
      id: 'performance_prediction_${DateTime.now().millisecondsSinceEpoch}',
      type: PredictionType.performance,
      title: 'توقع تحسن الأداء',
      description: 'متوقع تحسن الأداء بنسبة 15% خلال الأسبوع القادم',
      confidence: 0.8,
      timeframe: PredictionTimeframe.week,
      data: {'improvement': 15, 'metric': 'response_time'},
      createdAt: DateTime.now(),
    );
  }

  /// توقع الاتجاهات
  static Future<AIPrediction?> _predictTrends() async {
    // محاكاة توقع الاتجاهات
    return AIPrediction(
      id: 'trend_prediction_${DateTime.now().millisecondsSinceEpoch}',
      type: PredictionType.trend,
      title: 'اتجاه جديد متوقع',
      description: 'توقع زيادة في استخدام ميزات الذكاء الاصطناعي',
      confidence: 0.7,
      timeframe: PredictionTimeframe.month,
      data: {'trend': 'ai_features', 'growth_rate': 25},
      createdAt: DateTime.now(),
    );
  }

  /// تحديث المقاييس
  static Future<void> _updateMetrics(String dataKey) async {
    final data = _analyticsData[dataKey];
    if (data == null) return;

    final events = data.events;
    final now = DateTime.now();
    
    // حساب المقاييس
    final totalEvents = events.length;
    final todayEvents = events
        .where((e) => e.timestamp.day == now.day)
        .length;
    final weekEvents = events
        .where((e) => now.difference(e.timestamp).inDays <= 7)
        .length;
    
    data.metrics = AnalyticsMetrics(
      totalEvents: totalEvents,
      todayEvents: todayEvents,
      weekEvents: weekEvents,
      averagePerDay: weekEvents / 7,
      lastEventTime: events.isNotEmpty ? events.last.timestamp : null,
    );
  }

  /// بدء التحليل التلقائي
  static void _startAutoAnalysis() {
    // تحليل كل ساعة
    Stream.periodic(const Duration(hours: 1)).listen((_) async {
      await analyzeData();
      await generatePredictions();
    });
  }

  // Data persistence methods
  static Future<void> _saveAnalyticsData() async {
    final data = _analyticsData.map((key, value) => MapEntry(key, value.toMap()));
    await StorageService.saveData(_analyticsDataKey, data);
  }

  static Future<void> _loadAnalyticsData() async {
    try {
      final data = await StorageService.getData(_analyticsDataKey);
      if (data != null && data is Map) {
        _analyticsData = data.map((key, value) => 
            MapEntry(key, AnalyticsData.fromMap(value)));
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل بيانات التحليلات: $e');
    }
  }

  static Future<void> _saveInsights() async {
    final data = _insights.map((insight) => insight.toMap()).toList();
    await StorageService.saveData(_insightsKey, data);
  }

  static Future<void> _loadInsights() async {
    try {
      final data = await StorageService.getData(_insightsKey);
      if (data != null && data is List) {
        _insights = data.map((item) => AIInsight.fromMap(item)).toList();
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل الرؤى: $e');
    }
  }

  static Future<void> _savePredictions() async {
    final data = _predictions.map((prediction) => prediction.toMap()).toList();
    await StorageService.saveData(_predictionsKey, data);
  }

  static Future<void> _loadPredictions() async {
    try {
      final data = await StorageService.getData(_predictionsKey);
      if (data != null && data is List) {
        _predictions = data.map((item) => AIPrediction.fromMap(item)).toList();
      }
    } catch (e) {
      debugPrint('❌ فشل في تحميل التوقعات: $e');
    }
  }

  // Getters
  static bool get isInitialized => _isInitialized;
  static List<AIInsight> get insights => List.unmodifiable(_insights);
  static List<AIPrediction> get predictions => List.unmodifiable(_predictions);
  static Map<String, AnalyticsData> get analyticsData => Map.unmodifiable(_analyticsData);
}
